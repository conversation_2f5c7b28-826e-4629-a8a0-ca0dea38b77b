// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_place.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoPlace _$GeoPlaceFromJson(Map<String, dynamic> json) => GeoPlace(
      placeId: json['placeId'] as String?,
      internationalPhoneNumber: json['internationalPhoneNumber'] as String?,
      types:
          (json['types'] as List<dynamic>?)?.map((e) => e as String).toList(),
      allowsDogs: json['allowsDogs'] as bool?,
      formattedAddress: json['formattedAddress'] as String?,
      displayName: json['displayName'] == null
          ? null
          : GeoDisplayName.fromJson(
              json['displayName'] as Map<String, dynamic>),
      postalAddress: json['postalAddress'] == null
          ? null
          : GeoPostalAddress.fromJson(
              json['postalAddress'] as Map<String, dynamic>),
      businessStatus:
          $enumDecodeNullable(_$BusinessStatusEnumMap, json['businessStatus']),
      location: json['location'] == null
          ? null
          : GeoLocation.fromJson(json['location'] as Map<String, dynamic>),
      regularOpeningHours: json['regularOpeningHours'] == null
          ? null
          : GeoOpeningHours.fromJson(
              json['regularOpeningHours'] as Map<String, dynamic>),
      photos: (json['photos'] as List<dynamic>?)
          ?.map((e) => GeoPhoto.fromJson(e as Map<String, dynamic>))
          .toList(),
      rating: (json['rating'] as num?)?.toDouble(),
      reviews: (json['reviews'] as List<dynamic>?)
          ?.map((e) => GeoReviews.fromJson(e as Map<String, dynamic>))
          .toList(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$GeoPlaceToJson(GeoPlace instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'placeId': instance.placeId,
      'internationalPhoneNumber': instance.internationalPhoneNumber,
      'types': instance.types,
      'allowsDogs': instance.allowsDogs,
      'formattedAddress': instance.formattedAddress,
      'displayName': instance.displayName,
      'postalAddress': instance.postalAddress,
      'businessStatus': _$BusinessStatusEnumMap[instance.businessStatus],
      'location': instance.location,
      'regularOpeningHours': instance.regularOpeningHours,
      'photos': instance.photos,
      'rating': instance.rating,
      'reviews': instance.reviews,
    };

const _$BusinessStatusEnumMap = {
  BusinessStatus.unspecified: 'BUSINESS_STATUS_UNSPECIFIED',
  BusinessStatus.operational: 'OPERATIONAL',
  BusinessStatus.closedTemporarily: 'CLOSED_TEMPORARILY',
  BusinessStatus.closedPermanently: 'CLOSED_PERMANENTLY',
};

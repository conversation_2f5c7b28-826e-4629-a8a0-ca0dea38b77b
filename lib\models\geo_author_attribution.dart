import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

part 'geo_author_attribution.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoAuthorAttribution {

  String? displayName;
  String? uri;
  String? photoUri;

  GeoAuthorAttribution({
    this.displayName,
    this.uri,
    this.photoUri,
  });

  factory GeoAuthorAttribution.fromJson(Map<String, dynamic> json) => _$GeoAuthorAttributionFromJson(json);
  Map<String, dynamic> toJson() => _$GeoAuthorAttributionToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory GeoAuthorAttribution.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoAuthorAttribution.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory GeoAuthorAttribution.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoAuthorAttribution.fromJson(jsonData);
  }

  factory GeoAuthorAttribution.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoAuthorAttribution.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoAuthorAttribution? o) {
    return o?.toFirestoreData() ?? {};
  }

  static List<GeoAuthorAttribution>? fromFirestoreDataList(List<DocumentSnapshot>? list) =>
      list?.map((e) => GeoAuthorAttribution.fromFirestoreData(e)).toList();

  static List<GeoAuthorAttribution>? fromApiList(List<Map<String, dynamic>>? list) =>
      list?.map((e) => GeoAuthorAttribution.fromApiMap(e)).toList();

  static create ({
    String? displayName,
    String? uri,
    String? photoUri,
  }) {

    return GeoAuthorAttribution(
      displayName: displayName,
      uri: uri,
      photoUri: photoUri,
    );
  }

  static GeoAuthorAttribution copyFrom(GeoAuthorAttribution other) {
    return GeoAuthorAttribution(
      displayName: other.displayName,
      uri: other.uri,
      photoUri: other.photoUri,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/theme/text_schemes/onenata_classic_text_style_extension.dart';

class CommonTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final bool isEnabled;
  final bool isPassword;
  final bool isPasswordVisible;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Color textColor;
  final Color borderColor;
  final Color iconColor;
  final VoidCallback? onClear;
  final VoidCallback? onTogglePasswordVisibility;
  final ValueChanged<String>? onChanged;

  const CommonTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.isPassword = false,
    this.isPasswordVisible = false,
    this.isEnabled = true,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.textColor = OneNataClassicColors.veronica,
    this.borderColor = OneNataClassicColors.veronica,
    this.iconColor = OneNataClassicColors.veronica,
    this.onClear,
    this.onTogglePasswordVisibility,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 327.w,
      height: 60.h,
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: isPassword && !isPasswordVisible, // ✅ 处理密码可见性
        enabled: isEnabled,
        inputFormatters: inputFormatters,
        onChanged: onChanged,
        style: OneNataClassicTextStyleExtension().inputLarge.copyWith(
          color: textColor,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          fillColor: OneNataClassicColors.white,
          filled: true,
          suffixIcon: controller.text.isNotEmpty
              ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  controller.clear();
                  if (onClear != null) onClear!();
                  if (onChanged != null) onChanged!("");
                },
                child: Icon(Icons.close, size: 24.sp, color: iconColor),
              ),
              if (isPassword) SizedBox(width: 8.w), // ✅ 只有密码时才加间距
              if (isPassword) // ✅ 只有在有输入时，才显示眼睛 Icon
                GestureDetector(
                  onTap: onTogglePasswordVisibility,
                  child: Icon(
                    isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    size: 24.sp,
                    color: iconColor,
                  ),
                ),
            ],
          )
              : null, // ✅ 没有输入时，不显示任何 Icon
          hintStyle: OneNataClassicTextStyleExtension().inputLargeHint,
          contentPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 15.h),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16.r),
            borderSide: BorderSide(
              color: borderColor,
              width: 1.w,
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/component/appointment/service_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/note_input_bottom_sheet.dart';

// 时间段数据模型
class TimeSlot {
  final String id;
  final String time;
  final bool isAvailable;

  TimeSlot({
    required this.id,
    required this.time,
    this.isAvailable = true,
  });
}

// 人员数据模型
class Staff {
  final String id;
  final String name;
  final bool isAvailable;

  Staff({
    required this.id,
    required this.name,
    this.isAvailable = true,
  });
}

class DateTimeSelectionBottomSheet extends StatefulWidget {
  final Pet selectedPet;
  final ServiceType selectedService;
  final VoidCallback? onAppointmentConfirmed;

  const DateTimeSelectionBottomSheet({
    Key? key,
    required this.selectedPet,
    required this.selectedService,
    this.onAppointmentConfirmed,
  }) : super(key: key);

  @override
  State<DateTimeSelectionBottomSheet> createState() => _DateTimeSelectionBottomSheetState();
}

class _DateTimeSelectionBottomSheetState extends State<DateTimeSelectionBottomSheet> {
  DateTime? selectedDate;
  TimeSlot? selectedTimeSlot;
  Staff? selectedStaff;
  
  // 写死的时间段数据
  final List<TimeSlot> timeSlots = [
    TimeSlot(id: '1', time: '10:00'),
    TimeSlot(id: '2', time: '10:30'),
    TimeSlot(id: '3', time: '14:00'),
    TimeSlot(id: '4', time: '15:00'),
    TimeSlot(id: '5', time: '16:00'),
  ];
  
  // 写死的人员数据
  final List<Staff> staffList = [
    Staff(id: '1', name: 'Wendy'),
    Staff(id: '2', name: 'Amber'),
    Staff(id: '3', name: 'Zoe'),
    Staff(id: '4', name: 'Jason'),
  ];

  final List<String> weekLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  final List<String> months = ['2024', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  void _selectDate(DateTime date) {
    setState(() {
      selectedDate = date;
      // 重置后续选择
      selectedTimeSlot = null;
      selectedStaff = null;
    });
  }

  void _selectTimeSlot(TimeSlot timeSlot) {
    if (selectedDate != null && timeSlot.isAvailable) {
      setState(() {
        selectedTimeSlot = timeSlot;
        // 重置人员选择
        selectedStaff = null;
      });
    }
  }

  void _selectStaff(Staff staff) {
    if (selectedDate != null && selectedTimeSlot != null && staff.isAvailable) {
      setState(() {
        selectedStaff = staff;
      });
    }
  }

  void _continueToNext() {
    if (selectedDate != null && selectedTimeSlot != null && selectedStaff != null) {
      // 关闭当前弹窗并打开备注输入弹窗
      Get.back();
      NoteInputBottomSheetHelper.show(
        selectedPet: widget.selectedPet,
        selectedService: widget.selectedService,
        selectedDate: selectedDate!,
        selectedTimeSlot: selectedTimeSlot!,
        selectedStaff: selectedStaff!,
        onAppointmentSubmitted: () {
          widget.onAppointmentConfirmed?.call();
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: 36.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
          
          // 月份选择器
          _buildMonthSelector(),

          // 日历 - 减少空间占用
          SizedBox(
            height: 280.w, // 固定高度，减少空间占用
            child: _buildCalendar(),
          ),

          // 时间段选择 - 紧跟在日历下方
          if (selectedDate != null) ...[
            SizedBox(height: 16.w),
            _buildTimeSlotSelection(),
          ],

          // 人员选择 - 紧跟在时间选择下方
          if (selectedDate != null && selectedTimeSlot != null) ...[
            SizedBox(height: 16.w),
            _buildStaffSelection(),
          ],

          // 弹性空间，将Continue按钮推到底部
          Expanded(child: SizedBox()),
          
          // Continue 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: (selectedDate != null && selectedTimeSlot != null && selectedStaff != null) 
                  ? _continueToNext 
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: (selectedDate != null && selectedTimeSlot != null && selectedStaff != null)
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Continue',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthSelector() {
    final currentMonth = selectedDate?.month ?? DateTime.now().month;

    return Container(
      height: 50.w,
      margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: months.length,
        separatorBuilder: (_, __) => SizedBox(width: 16.w),
        itemBuilder: (context, index) {
          final month = months[index];
          final isYear = index == 0;
          final isSelected = !isYear && currentMonth == index;

          return GestureDetector(
            onTap: () {
              if (!isYear) {
                final newDate = DateTime(2024, index, 1);
                _selectDate(newDate);
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.w),
              decoration: BoxDecoration(
                color: isSelected ? OneNataClassicColors.veronica.withValues(alpha: 0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: Text(
                month,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? OneNataClassicColors.veronica : Color(0xFF666666),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCalendar() {
    final now = DateTime.now();
    final currentYear = 2024;
    final currentMonth = selectedDate?.month ?? now.month;

    final firstDayOfMonth = DateTime(currentYear, currentMonth, 1);
    final startWeekday = firstDayOfMonth.weekday % 7;
    final totalDaysThisMonth = DateTime(currentYear, currentMonth + 1, 0).day;
    final totalDaysLastMonth = DateTime(currentYear, currentMonth, 0).day;

    List<DateTime> visibleDates = [];

    // 上个月的日期
    for (int i = startWeekday - 1; i >= 0; i--) {
      visibleDates.add(DateTime(currentYear, currentMonth - 1, totalDaysLastMonth - i));
    }

    // 当前月的日期
    for (int i = 1; i <= totalDaysThisMonth; i++) {
      visibleDates.add(DateTime(currentYear, currentMonth, i));
    }

    // 下个月的日期
    int remaining = 42 - visibleDates.length;
    for (int i = 1; i <= remaining; i++) {
      visibleDates.add(DateTime(currentYear, currentMonth + 1, i));
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          // 星期标题
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: weekLabels.map((label) {
              return SizedBox(
                width: 42.w,
                child: Center(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF999999),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          
          SizedBox(height: 8.w),
          
          // 日历网格
          Expanded(
            child: GridView.builder(
              padding: EdgeInsets.zero,
              itemCount: 42,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.w,
                childAspectRatio: 1,
              ),
              itemBuilder: (context, index) {
                final date = visibleDates[index];
                final isSelected = selectedDate != null &&
                    selectedDate!.year == date.year &&
                    selectedDate!.month == date.month &&
                    selectedDate!.day == date.day;
                final isCurrentMonth = date.month == currentMonth;
                final isToday = date.year == now.year &&
                    date.month == now.month &&
                    date.day == now.day;

                return GestureDetector(
                  onTap: () => _selectDate(date),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Color(0xFFF2D3A4)
                          : (isToday ? OneNataClassicColors.veronica.withValues(alpha: 0.1) : Colors.transparent),
                      borderRadius: BorderRadius.circular(8.w),
                      border: isCurrentMonth 
                          ? Border.all(color: Color(0xFFE5E5E5), width: 1.w)
                          : null,
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '${date.day}',
                      style: TextStyle(
                        fontFamily: 'Manrope',
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: isSelected 
                            ? Colors.white
                            : (isCurrentMonth ? Color(0xFF262626) : Color(0xFFCCCCCC)),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotSelection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Wrap(
        spacing: 12.w,
        runSpacing: 12.w,
        children: timeSlots.map((timeSlot) {
          final isSelected = selectedTimeSlot?.id == timeSlot.id;
          final isEnabled = selectedDate != null && timeSlot.isAvailable;
          
          return GestureDetector(
            onTap: () => _selectTimeSlot(timeSlot),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
              decoration: BoxDecoration(
                color: isSelected 
                    ? OneNataClassicColors.veronica 
                    : (isEnabled ? Colors.white : Color(0xFFF5F5F5)),
                borderRadius: BorderRadius.circular(20.w),
                border: Border.all(
                  color: isSelected 
                      ? OneNataClassicColors.veronica 
                      : (isEnabled ? Color(0xFFE5E5E5) : Color(0xFFCCCCCC)),
                  width: 1.w,
                ),
              ),
              child: Text(
                timeSlot.time,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: isSelected 
                      ? Colors.white 
                      : (isEnabled ? Color(0xFF262626) : Color(0xFF999999)),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStaffSelection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      child: Wrap(
        spacing: 12.w,
        runSpacing: 12.w,
        children: staffList.map((staff) {
          final isSelected = selectedStaff?.id == staff.id;
          final isEnabled = selectedDate != null && selectedTimeSlot != null && staff.isAvailable;
          
          return GestureDetector(
            onTap: () => _selectStaff(staff),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
              decoration: BoxDecoration(
                color: isSelected 
                    ? OneNataClassicColors.veronica 
                    : (isEnabled ? Colors.white : Color(0xFFF5F5F5)),
                borderRadius: BorderRadius.circular(20.w),
                border: Border.all(
                  color: isSelected 
                      ? OneNataClassicColors.veronica 
                      : (isEnabled ? Color(0xFFE5E5E5) : Color(0xFFCCCCCC)),
                  width: 1.w,
                ),
              ),
              child: Text(
                staff.name,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: isSelected 
                      ? Colors.white 
                      : (isEnabled ? Color(0xFF262626) : Color(0xFF999999)),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// 显示日期时间选择底部弹窗的静态方法
class DateTimeSelectionBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    required ServiceType selectedService,
    VoidCallback? onAppointmentConfirmed,
  }) {
    Get.bottomSheet(
      DateTimeSelectionBottomSheet(
        selectedPet: selectedPet,
        selectedService: selectedService,
        onAppointmentConfirmed: onAppointmentConfirmed,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}

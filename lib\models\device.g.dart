// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Device _$DeviceFromJson(Map<String, dynamic> json) => Device(
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      deviceType: $enumDecodeNullable(_$DeviceTypeEnumMap, json['deviceType']),
      deviceModel:
          $enumDecodeNullable(_$DeviceModelEnumMap, json['deviceModel']),
      manufacturer: json['manufacturer'] as String?,
      manufacturerSerialNumber: json['manufacturerSerialNumber'] as String?,
      firmwareVersion: json['firmwareVersion'] as String?,
      hardwareVersion: json['hardwareVersion'] as String?,
      uid: json['uid'] as String,
      pets: (json['pets'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$DeviceToJson(Device instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'deviceType': _$DeviceTypeEnumMap[instance.deviceType],
      'deviceModel': _$DeviceModelEnumMap[instance.deviceModel],
      'manufacturer': instance.manufacturer,
      'manufacturerSerialNumber': instance.manufacturerSerialNumber,
      'firmwareVersion': instance.firmwareVersion,
      'hardwareVersion': instance.hardwareVersion,
      'uid': instance.uid,
      'pets': instance.pets,
    };

const _$DeviceTypeEnumMap = {
  DeviceType.feedingMachine: 'FEEDING_MACHINE',
  DeviceType.locator: 'LOCATOR',
};

const _$DeviceModelEnumMap = {
  DeviceModel.piFI: 'P01',
  DeviceModel.locator: 'L01',
};

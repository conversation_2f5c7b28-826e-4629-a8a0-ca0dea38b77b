import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';

class LogInPage extends StatefulWidget {
  const LogInPage({super.key});

  @override
  LogInPageState createState() => LogInPageState();
}

class LogInPageState extends State<LogInPage> {
  late Future<LogInPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => LogInPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<LogInPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [

                      // Avatar
                      SizedBox(
                        height: controller.theme.layout.authAvatarHeaderLineHeight
                      ),

                      // Sign up label
                      SizedBox(
                        height: 149.w,
                        child: Column(
                          children: [
                            Expanded(child: SizedBox.shrink()),
                            Obx(()=> AuthWidgetBuilder.buildLabel(
                              controller.theme,
                              title: controller.pageLabelTitle.value,
                              desc: controller.pageLabelDesc.value)
                            ),
                            SizedBox(height: 20.w,),
                          ],
                        )
                      ),

                      Expanded(
                        child: SingleChildScrollView(
                          padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom,
                          ),
                          controller: controller.scrollController,
                          child: Column(
                            children: [

                              // Phone input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPhone2(
                                  controller.theme, controller.usernameInputController,
                                )
                              ),
                              SizedBox(height: 20.w,),

                              // Verification code input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxVerificationCode2(
                                    controller.theme, controller.passwordInputController,
                                  )
                              ),

                              // Resend section
                              Obx(()=> (controller.isVerificationCodeSent.isTrue && controller.isPhoneNumberValid.isTrue) ?
                                SizedBox(
                                  height: 50.w,
                                  child: controller.isReSendEnabled.value ?
                                  // Resend enabled
                                  GestureDetector(
                                    onTap: () async {
                                      await controller.sendVerificationCodeOfPhoneNumber();
                                    },
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        CommonText(
                                            'auth.button.resend'.t18,
                                            controller.theme.textStyleExtension.authPageResend
                                        ),
                                        SizedBox(width: 38.w,)
                                      ],
                                    ),

                                  ) :
                                  // Resend disabled
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Obx(()=> CommonText(
                                          'auth.button.resend.in.second'.t19({
                                            'second': '${controller.remainedTime.value.toInt()}'
                                          }),
                                          controller.theme.textStyleExtension.authPageResendDisabled
                                      )),
                                      SizedBox(width: 38.w,)
                                    ],
                                  )
                                ) :
                                SizedBox(
                                  height: 50.w,
                                  child: SizedBox.shrink(),
                                )
                              ),

                              SizedBox(
                                height: screenSize.height
                                - controller.theme.layout.authAvatarHeaderLineHeight
                                - 149.w
                                - 140.w
                                - 50.w
                                - 160.w,
                              ),
                              // Expanded(child: SizedBox.shrink()),

                              // Send verification code
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.buttonController,
                                )
                              ),
                              SizedBox(height: 20.w,),

                              // Change to email
                              SizedBox(
                                  height: 50.w,
                                  child: AuthWidgetBuilder.buildButton2(
                                    controller.theme,
                                    controller.alterButtonController,
                                  )),
                              SizedBox(height: 30.w,),

                            ],
                          ),
                        )
                      ),
                    ]
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
                ],
              ),
            );
          }
        });
  }
}

class LogInPageController extends GetxController {

  // Services
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;

  final ScrollController scrollController = ScrollController();
  final _usernameKey = GlobalKey(debugLabel: 'usernameKey${DateTime.now().millisecondsSinceEpoch}');
  final _passwordKey = GlobalKey(debugLabel: 'passwordKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  var isVerificationCodeSent = false.obs;
  var isVerificationCodeValid = false.obs;
  var verificationId = ''.obs;
  var verificationCode = ''.obs;
  var isPhoneNumberValid = false.obs;
  var isReSendEnabled = false.obs;
  var remainedTime = 30.obs;
  var areaCode = '+1'.obs;
  late int phoneNumberValidLength = 10;
  Timer? timer;

  final String validCode = '123';

  // Theme plugin
  late ThemePlugin theme;

  Future<LogInPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.login'.t18,
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
        'auth.label.login.phone'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildPhoneNumberTextFieldController(
      theme, usernameInputBox.obs,
      globalKey: _usernameKey.obs,
      onCleared: onPhoneNumberCleared.obs,
      onChanged: isInputPhoneNumberValid.obs,
    );
    // Listen to the focus node
    usernameInputController.textFieldFocusNode.addListener(() {
      usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_usernameKey);
    });

    // build password input controller
    passwordInputController = AuthWidgetBuilder.buildVerificationCodeTextFieldController(
        theme, passwordInputBox.obs,
        globalKey: _passwordKey.obs,
        onCleared: onVerificationCodeCleared.obs,
        onChanged: isInputVerificationCodeValid.obs
    );
    // Listen to the focus node
    passwordInputController.textFieldFocusNode.addListener(() {
      passwordInputController.isFocused.value = passwordInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_passwordKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.send.code'.t18.obs,
      onPressed: sendVerificationCodeOfPhoneNumber.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.change.to.email'.t18.obs,
      onPressed: alter.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    if (areaCode.value == '+1') {
      phoneNumberValidLength = 10;
    }
    else if (areaCode.value == '+86') {
      phoneNumberValidLength = 11;
    }
    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
    // resetState();
  }

  @override
  void onClose() {
    timer?.cancel();
    // scrollController.dispose();
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && (key == _usernameKey && usernameInputController.textFieldFocusNode.hasFocus ||
        key == _passwordKey && passwordInputController.textFieldFocusNode.hasFocus)) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  void startCountdown() {

    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainedTime.value == 0) {
        isReSendEnabled.value = true;
        timer.cancel();
      } else {
        remainedTime.value--;
      }
    });
  }

  bool isButtonEnabled() {

    // Click to verify code
    if (isVerificationCodeSent.value) {
      return isVerificationCodeValid.value;
    }
    // Click to send verification code
    else {
      return isPhoneNumberValid.value;
    }
  }

  Future<void> onPhoneNumberCleared(BuildContext context) async {
    await resetState();
  }

  Future<void> onVerificationCodeCleared(BuildContext context) async {

    // isVerificationCodeSent.value = true;
    isVerificationCodeValid.value = false;
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    // buttonController.onPressed.value = verifyOtp;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputPhoneNumberValid({required BuildContext context, required bool result}) async {
    isPhoneNumberValid.value = result;
    usernameInputController.hasMistake.value = isPhoneNumberValid.isFalse && usernameInputBox.text.length > phoneNumberValidLength;

    if (isPhoneNumberValid.isTrue) {
      buttonController.text = 'auth.button.send.code'.t18.obs;
      buttonController.onPressed = sendVerificationCodeOfPhoneNumber.obs;
      buttonController.isEnabled.value = true;
    }
    else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputVerificationCodeValid({required BuildContext context, required bool result}) async {
    isVerificationCodeValid.value = result;
    passwordInputController.hasMistake.value = isVerificationCodeValid.isFalse && passwordInputBox.text != '';

    if (isVerificationCodeValid.isTrue && isPhoneNumberValid.isTrue) {
      buttonController.text = 'auth.button.verify.code'.t18.obs;
      buttonController.onPressed = verifyOtp.obs;
      buttonController.isEnabled.value = true;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationCodeOfPhoneNumber() async {

    if (isPhoneNumberValid.isFalse) return;

    String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';
    // String phoneNumber = InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text);
    // String phoneNumber = kReleaseMode ? InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text) : '+15555555555';

    // Check existing
    ServiceResponse<String> response = await authService.sendVerificationCodePhoneNumber(phoneNumber);

    if (response.code == 201) {

      // success callback
      await callbackCodeVerified(phoneNumber);

      isReSendEnabled.value = false;
      timer?.cancel();

      // Login success
      await resetState();
      Get.offAll(RootPage());
      return;
    }
    else if (response.code == 200) {

      verificationId.value = response.data!;
      isReSendEnabled.value = true;
      isVerificationCodeSent.value = true;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.sent'.t18,
        theme.textStyleExtension.authPageNotification,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.isEnabled.value = true;
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;

      buttonController.isEnabled.value = false;
      buttonController.onPressed.value = verifyOtp;

      startCountdown();
    }
    else if (response.code == 408) {

      verificationId.value = response.data!;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.timeout'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
    else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.desc'.t18);

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }

  }

  Future<void> verifyOtp() async {

    String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';

    try {
      // Sign in the user with the sms code
      ServiceResponse<User> response = await authService.verifyCodeLogIn(phoneNumber, verificationId.value, passwordInputBox.text);

      if (response.code == 200) {

        // success callback
        await callbackCodeVerified(phoneNumber);
        await resetState();
        Get.offAll(RootPage());
        return;
      }
      else {

        pageLabelDesc.value = CommonText(
          'auth.error.code.invalid'.t18,
          theme.textStyleExtension.authPageWarning,
          width: 350.w,
          height: 50.w,
          maxLines: 2,
          softWrap: true,
        );
        //
        // passwordInputBox.text = '';
        // passwordInputController.obscureText.value = true;
        // passwordInputController.hasMistake.value = false;
        // passwordInputController.isEnabled.value = false;
        //
        // isReSendEnabled.value = false;
        // buttonController.isEnabled.value = true;
        // isVerificationCodeSent.value = false;
        // buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
      }
    }
    catch (e) {
      isVerificationCodeValid.value = false;
      // Get.snackbar("Error", "Invalid OTP. Please try again.");
    }
  }

  Future<void> callbackCodeVerified(String phoneNumber) async {

    UserAccount? acc = await userService.getUserAccountById(phoneNumber: phoneNumber);

    // save user account
    await userService.createUserAccount(authService.currentUser.value!, AuthChannel.sms);

    // add auth record in Firestore and security storage
    await userService.recordAuthActivity(authService.currentUser.value!, AuthChannel.sms);

    // Create user data if not exists
    if (authService.userData.value == null || authService.userData.value!.uid != authService.userAccount.value!.sid) {
      UserData? ud = await userService.getUserDataById(userId: authService.userAccount.value!.sid!);
      if (ud == null) {
        UserData data = UserData.create(
          uid: authService.userAccount.value!.sid!,
        );
        await userService.createUserData(data);
      } else {
        authService.userData.value = ud;
      }
    }
  }

  Future<void> alter() async {
    timer?.cancel();
    buttonController.isInProgress.value = false;
    await resetState();
    Get.to(()=> LogInEmailPage());
  }

  Future<void> resetState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.phone'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    passwordInputController.textFieldFocusNode.unfocus();

    // Reset states
    isPhoneNumberValid.value = false;
    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    isVerificationCodeSent.value = false;
    isVerificationCodeValid.value = false;

    // Button
    buttonController.text = 'auth.button.send.code'.t18.obs;
    buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }
}

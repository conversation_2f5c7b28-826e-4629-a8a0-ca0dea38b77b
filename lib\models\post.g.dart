// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Post _$PostFromJson(Map<String, dynamic> json) => Post(
      uid: json['uid'] as String,
      type: $enumDecode(_$PostTypeEnumMap, json['type']),
      contentType: $enumDecode(_$PostContentTypeEnumMap, json['contentType']),
      pets: (json['pets'] as List<dynamic>?)?.map((e) => e as String).toList(),
      visibility: $enumDecode(_$PublishVisibilityEnumMap, json['visibility']),
      title: json['title'] as String?,
      content: json['content'] as String,
      location: JsonUtil.geoFirePointFromJson(
          json['location'] as Map<String, dynamic>?),
      altitude: (json['altitude'] as num?)?.toDouble(),
      keywords: (json['keywords'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      placeId: json['placeId'] as String?,
      displayName: json['displayName'] as String?,
      formattedAddress: json['formattedAddress'] as String?,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PostToJson(Post instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'type': _$PostTypeEnumMap[instance.type]!,
      'contentType': _$PostContentTypeEnumMap[instance.contentType]!,
      'pets': instance.pets,
      'visibility': _$PublishVisibilityEnumMap[instance.visibility]!,
      'title': instance.title,
      'content': instance.content,
      'location': JsonUtil.geoFirePointToJson(instance.location),
      'altitude': instance.altitude,
      'keywords': instance.keywords,
      'placeId': instance.placeId,
      'displayName': instance.displayName,
      'formattedAddress': instance.formattedAddress,
    };

const _$PostTypeEnumMap = {
  PostType.moment: 'MOM',
  PostType.reHome: 'REH',
  PostType.help: 'HELP',
  PostType.event: 'EVENT',
};

const _$PostContentTypeEnumMap = {
  PostContentType.onlyText: 'TXT',
  PostContentType.withImage: 'IMG',
  PostContentType.withVideo: 'VID',
};

const _$PublishVisibilityEnumMap = {
  PublishVisibility.public: 'PUB',
  PublishVisibility.private: 'PVT',
  PublishVisibility.friend: 'FRD',
};

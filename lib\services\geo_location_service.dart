import 'dart:convert';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/api/api_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/models/vo/vo_i.dart';

class GeoLocationService {
  final GeoLocationApi geoApi = GeoLocationApi();
  final GeoLocationDao geoDao = GeoLocationDao();

  static Future<LocationPermission> requestPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }
    return permission;
  }

  static Future<Position?> getCurrentLocation() async {
    LocationPermission permission = await requestPermission();

    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      logger.e('Location permission denied');
      return null;
    }

    return await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    ));
  }

  static CameraPosition buildCameraPosition(LatLng target) {
    return CameraPosition(
      target: target,
      zoom: 14.4746,
      tilt: GeoLocationConst.defaultTilt,
      bearing: GeoLocationConst.defaultBearing,
    );
  }

  // Search for pets places
  static Future<List<Map<String, dynamic>>> searchPetFriendlyPlaces(
      LatLng location, String type,
      {int radius = 5000, String? apiKey}) async {
    try {
      //search query
      String searchQuery = '';

      switch (type) {
        case GeoLocationConst.dogParkType:
          searchQuery = 'dog+park+pet+friendly';
          break;
        case GeoLocationConst.offLeashTrailType:
          searchQuery = 'off+leash+trail+pet+friendly';
          break;
        case GeoLocationConst.petFriendlyParkType:
          searchQuery = 'pet+friendly+park';
          break;
        default:
          searchQuery = 'dog+park+pet+friendly';
      }

      // calling Google Places API
      final response = await http.get(
        Uri.parse('https://maps.googleapis.com/maps/api/place/textsearch/json?'
            'query=$searchQuery'
            '&location=${location.latitude},${location.longitude}'
            '&radius=$radius'
            '&key=${apiKey ?? ApiConst.gmApiKey}'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          return List<Map<String, dynamic>>.from(data['results']);
        } else {
          logger.e('Places API error: ${data['status']}');
          return [];
        }
      } else {
        logger.e('Failed to fetch places: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      logger.e('Error searching pet friendly places: $e');
      return [];
    }
  }

  // places To Markers
  static Set<Marker> placesToMarkers(
      List<Map<String, dynamic>> places,
      String type,
      Function(Map<String, dynamic>) onTap,
      BitmapDescriptor? icon) {
    return places.map((place) {
      final lat = place['geometry']['location']['lat'];
      final lng = place['geometry']['location']['lng'];
      final name = place['name'];

      return Marker(
        markerId: MarkerId(place['place_id']),
        position: LatLng(lat, lng),
        infoWindow: InfoWindow(
          title: name,
          snippet: place['formatted_address'] ?? '',
        ),
        icon: icon ?? BitmapDescriptor.defaultMarker,
        onTap: () => onTap(place),
      );
    }).toSet();
  }

  // Markers
  static Future<BitmapDescriptor> getCustomMarkerIcon(String type) async {
    String assetPath;

    switch (type) {
      case GeoLocationConst.dogParkType:
        assetPath = ImageAssetConst.mapDogParkIcon;
        break;
      case GeoLocationConst.offLeashTrailType:
        assetPath = ImageAssetConst.mapTrailIcon;
        break;
      default:
        return BitmapDescriptor.defaultMarker;
    }

    return BitmapDescriptor.fromAssetImage(
      const ImageConfiguration(size: Size(30, 30)),
      assetPath,
    );
  }

  // Search for the places by query
  static Future<List<Map<String, dynamic>>> searchPlacesByQuery(String query,
      {LatLng? location, int radius = 5000, String? apiKey}) async {
    try {
      String url = 'https://maps.googleapis.com/maps/api/place/textsearch/json?'
          'query=$query';

      if (location != null) {
        url +=
            '&location=${location.latitude},${location.longitude}&radius=$radius';
      }

      url += '&key=${apiKey ?? ApiConst.gmApiKey}';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 'OK') {
          return List<Map<String, dynamic>>.from(data['results']);
        } else {
          logger.e('Places API error: ${data['status']}');
          return [];
        }
      } else {
        logger.e('Failed to fetch places: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      logger.e('Error searching places: $e');
      return [];
    }
  }

  Future<List<GeoPlace>?> searchNearBy(
      {required LatLng latlng,
      List<String>? type = ApiConst.gmApiSearchTypePark,
      GeoLocationZoomLevel? zoomLevel = GeoLocationZoomLevel.level_14,
      int? pageSize = 10}) async {
    ApiResponseData response = await geoApi.searchNearBy(
        latlng: latlng, type: type!, zoomLevel: zoomLevel, pageSize: pageSize);

    print("response :  ${response.data}");

    if (response.code! >= 200 && response.code! < 300) {
      final List<dynamic> json = response.data['places'] ?? [];
      return json.map((e) => GeoPlace.fromApiMap(e)).toList();
    } else {
      logger.e('Error fetching nearby places: ${response.data}');
      return null;
    }
  }

  Future<List<GeoPlace>?> searchText(
      {required LatLng low,
      required LatLng high,
      required String keyword,
      List<String>? type,
      LatLng? latlng,
      GeoLocationZoomLevel? zoomLevel = GeoLocationZoomLevel.level_14,
      int? pageSize = 10}) async {
    ApiResponseData response = await geoApi.searchText(
        low: low,
        high: high,
        keyword: keyword,
        latlng: latlng,
        type: type,
        zoomLevel: zoomLevel,
        pageSize: pageSize);

    if (response.code! >= 200 && response.code! < 300) {
      final List<dynamic> json = response.data['places'] ?? [];
      return json.map((e) => GeoPlace.fromApiMap(e)).toList();
    } else {
      logger.e('Error fetching nearby places: ${response.data}');
      return null;
    }
  }

  // Favor places
  Future<void> addGeoFavorPlace(GeoFavorPlace gfp) async {
    await geoDao.addGeoFavorPlace(gfp);
  }

  Future<void> updateGeoFavorPlace(GeoFavorPlace gfp) async {
    await geoDao.updateGeoFavorPlace(gfp);
  }

  Future<void> deleteGeoFavorPlace(String gfpId) async {
    await geoDao.deleteGeoFavorPlace(gfpId);
  }

  Future<List<GeoFavorPlace>?> queryGeoFavorPlaceList(
      {required String uid, GeoPlaceFilterType? type}) async {
    return await geoDao.getGeoFavorPlaceList(uid: uid, type: type);
  }

  Future<GeoFavorPlace?> queryGeoFavorPlace(String id) async {
    return await geoDao.getGeoFavorPlaceById(id);
  }

  // Geo encode

  /// Generates a random point within [radiusMeters] of [centerLat],[centerLng]
  static GeoPoint generateRandomPointInRadius(
    GeoPoint point,
    double radiusMeters,
  ) {
    final random = Random();

    // Convert radius from meters to radians (approximate)
    final radiusInDegrees =
        radiusMeters / GeoLocationConst.earthRadius * (180 / pi);

    // Generate random distance and angle
    final r = radiusInDegrees * sqrt(random.nextDouble());
    final theta = random.nextDouble() * 2 * pi;

    // Calculate new point
    final newLat = point.latitude + r * cos(theta);
    final newLng =
        point.longitude + r * sin(theta) / cos(point.latitude * pi / 180);

    return GeoPoint(newLat, newLng);
  }

  /// Verifies the generated point is within the specified radius
  static bool isWithinRadius(
    GeoPoint center,
    GeoPoint point,
    double radiusMeters,
  ) {
    final distance =
        GeoFirePoint(center).distanceBetweenInKm(geopoint: point) * 1000;
    return distance <= radiusMeters;
  }
}

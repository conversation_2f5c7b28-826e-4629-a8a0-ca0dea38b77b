enum ThemeType {

  oneNataClassic('theme.onenata.classic'),
  ;

  final String t18key; // Stored code in database
  const ThemeType(this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory ThemeType.fromT18Key(String t18key) {
    return ThemeType.values.firstWhere((element) => element.t18key == t18key, orElse: () => throw ArgumentError('Invalid code: $t18key'));
  }
}

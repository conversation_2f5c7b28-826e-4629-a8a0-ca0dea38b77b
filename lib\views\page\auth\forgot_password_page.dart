import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';

class ForgotPasswordPage extends StatefulWidget {

  const ForgotPasswordPage({super.key});

  @override
  ForgotPasswordPageState createState() => ForgotPasswordPageState();
}

class ForgotPasswordPageState extends State<ForgotPasswordPage> {
  late Future<ForgotPasswordPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => ForgotPasswordPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ForgotPasswordPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [
                      // Avatar
                      SizedBox(
                        height: controller.theme.layout.authAvatarHeaderLineHeight
                      ),

                      // Sign up label
                      SizedBox(
                        height: 169.w,
                        child: Column(
                          children: [
                            Expanded(child: SizedBox.shrink()),
                            Obx(()=> AuthWidgetBuilder.buildLabel(
                                controller.theme,
                                title: controller.pageLabelTitle.value,
                                desc: controller.pageLabelDesc.value)
                            ),
                            SizedBox(height: 20.w,),
                          ],
                        )
                      ),

                      // email input box
                      SizedBox(
                          height: 60.w,
                          child: AuthWidgetBuilder.buildInputBoxEmail2(
                            controller.theme, controller.usernameInputController,
                          )
                      ),

                      Expanded(child: SizedBox.shrink()),

                      // submit button
                      SizedBox(
                        height: 60.w,
                        child: AuthWidgetBuilder.buildButton2(
                          controller.theme,
                          controller.buttonController,
                        )
                      ),

                      // bottom padding
                      SizedBox(height: 35.w,),

                    ],
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
                ],
              ),
            );
          }
        });
  }
}

class ForgotPasswordPageController extends GetxController {

  // Services
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;

  final ScrollController scrollController = ScrollController();
  final _usernameKey = GlobalKey(debugLabel: 'usernameKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  var onPressed = Rx<AsyncCallback?>(null);

  var isEmailAddressValid = false.obs;
  var isEmailAddressVerified = false.obs;

  // Theme plugin
  late ThemePlugin theme;

  Future<ForgotPasswordPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.reset.password'.t18,
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
      'auth.label.reset.password.enter.email'.t18,
      theme.textStyleExtension.authPageDesc,
      width: 320.w,
      maxLines: 3,
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildEmailAddressTextFieldController(
      theme, usernameInputBox.obs,
      globalKey: _usernameKey.obs,
      onCleared: onEmailAddressCleared.obs,
      onChanged: isInputEmailAddressValid.obs,
    );
    // Listen to the focus node
    usernameInputController.textFieldFocusNode.addListener(() {
      usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_usernameKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.reset.password.send'.t18.obs,
      onPressed: sendVerificationEmail.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    usernameInputController.textFieldFocusNode.unfocus();
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && key == _usernameKey && usernameInputController.textFieldFocusNode.hasFocus) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  bool isButtonEnabled() {
    return isEmailAddressValid.isTrue;
  }

  Future<void> onEmailAddressCleared(BuildContext context) async {

    usernameInputBox.text = '';
    isEmailAddressValid.value = false;
    isEmailAddressValid.value = true;
    FocusScope.of(context).unfocus();
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputEmailAddressValid({required BuildContext context, required bool result}) async {
    isEmailAddressValid.value = result;

    if (isEmailAddressValid.isTrue && usernameInputBox.text != '') {
      buttonController.isEnabled.value = true;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationEmail() async {

    if (isEmailAddressValid.isFalse) return;

    String email = usernameInputBox.text;

    if (!RegExp(InputUtil.validEmailAddressRegEx).hasMatch(email)) {
      Get.snackbar('auth.error.title'.t18, 'auth.error.email.invalid'.t18);
    }

    // Send reset password email to user, once done user can login with email and password
    ServiceResponse<String> response = await authService.resetPassword(email);

    if (response.code == 200) {
        // Redirect to verify page
        // Get.back();
        Get.offAll(LogInEmailPage());
        return;
    }
    else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.email.invalid'.t18);
    }
  }
}

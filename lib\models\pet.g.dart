// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Pet _$PetFromJson(Map<String, dynamic> json) => Pet(
      owner: json['owner'] as String,
      regId: json['regId'] as String?,
      breed: json['breed'] as String?,
      gender: $enumDecodeNullable(_$PetGenderEnumMap, json['gender']),
      type: $enumDecodeNullable(_$PetTypeEnumMap, json['type']),
      avatar: json['avatar'] as String?,
      isLive: JsonUtil.boolFromJson(json['isLive']),
      birthday: (json['birthday'] as num?)?.toInt(),
      attachedDevices: (json['attachedDevices'] as List<dynamic>?)
          ?.map((e) => Device.fromJson(e as Map<String, dynamic>))
          .toList(),
      latestLocation: JsonUtil.geoFirePointFromJson(
          json['latestLocation'] as Map<String, dynamic>?),
      latestLocationAt: (json['latestLocationAt'] as num?)?.toInt(),
      latestBatteryLevel: (json['latestBatteryLevel'] as num?)?.toInt(),
      latestBatteryLevelAt: (json['latestBatteryLevelAt'] as num?)?.toInt(),
      latestStandbyTime: (json['latestStandbyTime'] as num?)?.toInt(),
      latestStandbyTimeAt: (json['latestStandbyTimeAt'] as num?)?.toInt(),
      visibility:
          $enumDecodeNullable(_$PetVisibilityEnumMap, json['visibility']) ??
              PetVisibility.friend,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PetToJson(Pet instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'owner': instance.owner,
      'regId': instance.regId,
      'breed': instance.breed,
      'gender': _$PetGenderEnumMap[instance.gender],
      'type': _$PetTypeEnumMap[instance.type],
      'avatar': instance.avatar,
      'isLive': JsonUtil.boolToJson(instance.isLive),
      'birthday': instance.birthday,
      'attachedDevices': instance.attachedDevices,
      'latestLocation': JsonUtil.geoFirePointToJson(instance.latestLocation),
      'latestLocationAt': instance.latestLocationAt,
      'latestBatteryLevel': instance.latestBatteryLevel,
      'latestBatteryLevelAt': instance.latestBatteryLevelAt,
      'latestStandbyTime': instance.latestStandbyTime,
      'latestStandbyTimeAt': instance.latestStandbyTimeAt,
      'visibility': _$PetVisibilityEnumMap[instance.visibility],
    };

const _$PetGenderEnumMap = {
  PetGender.boy: 'M',
  PetGender.girl: 'F',
};

const _$PetTypeEnumMap = {
  PetType.cat: 'CAT',
  PetType.dog: 'DOG',
  PetType.others: 'OTH',
};

const _$PetVisibilityEnumMap = {
  PetVisibility.public: 'PUB',
  PetVisibility.friend: 'FRD',
  PetVisibility.private: 'PVT',
};

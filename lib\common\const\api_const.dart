import 'env.dart';

class ApiConst {
  // Google Map
  static String get gmApiBaseUrl => Env.gmApiBaseUrl;
  static String get gmApiKey => Env.gmApiKey;
  static String get gmStyleId => Env.gmStyleId;
  static const int gmApiConnectTimeout = 10;
  static const int gmApiReceiveTimeout = 30;
  static const int gmApiResponseNumber = 20;
  static const String gmApiContentType = 'application/json';
  static const String gmApiPathPlace = 'places';
  static const List<String> gmApiSearchTypePark = ['dog_park'];
  // static const List<String> gmApiSearchTypePark = ['dog_park', 'garden', 'park', 'playground'];
  static const List<String> gmApiSearchTypeTrail = ['hiking_area'];
  static const List<String> gmApiSearchTypeVet = ['veterinary_care'];
  static const List<String> gmApiSearchTypePetStore = ['pet_store'];
  static const List<String> gmApiSearchTypeDogCafe = ['dog_cafe'];
  static const String gmApiSearchKeywordOffLeashTrails = 'off leash trails';
  static const String gmApiSearchKeywordDogFriendlyRestaurants =
      'dog friendly restaurants';
  static const String gmApiSearchFieldNearbyPlaces =
      'places.name,places.displayName,places.internationalPhoneNumber,places.allowsDogs,places.formattedAddress,places.postalAddress,places.businessStatus,places.rating,places.regularOpeningHours,places.location,places.photos,places.reviews';
}

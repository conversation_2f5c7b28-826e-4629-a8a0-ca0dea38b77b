import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

import '../../component/widgets/common_text.dart';
import '../../theme/theme_plugin.dart';

class PlacesSection extends StatelessWidget {

  final List<Map<String, dynamic>> places = [
    {
      "placeId": "1",
      "placeName": "Marine Dr. park",
      "distance": "1.3 km",
      "rating": 4.7,
      "imageUrl": "https://images.pexels.com/photos/4587993/pexels-photo-4587993.jpeg",
      "updateTime": DateTime(2025, 2, 22, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "2",
      "placeName": "Clayton off leash park",
      "distance": "0.8 km",
      "rating": 4.9,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 19, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "3",
      "placeName": "Point grey dog park",
      "distance": "0.5 km",
      "rating": 4.2,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 15, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "4",
      "placeName": "2038 Powell trail",
      "distance": "1.3 km",
      "rating": 4.7,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 22, 14, 30),
      "openingHours": "13:30",
    },
  ];
  final ThemePlugin theme;
  PlacesSection({required this.theme, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return Padding(
      padding: EdgeInsets.only(top: 8.w),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        itemCount: places.length,
        itemBuilder: (context, index) {
          var place = places[index];
          return _buildPlaceItem(place);
        },
      ),
    );
  }

  Widget _buildPlaceItem(Map<String, dynamic> place) {
    DateTime updateTime = place["updateTime"];
    String formattedDate = DateFormat("dd MMM yyyy").format(updateTime);
    String formattedTime = place["openingHours"];

    return Container(
      margin: EdgeInsets.only(bottom: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: theme.colorExtension.eventPlaceTileShadow1,
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: -2,
          ),
          BoxShadow(
            color: theme.colorExtension.eventPlaceTileShadow2,
            blurRadius: 5,
            offset: const Offset(0, 0),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 69.w,
            height: 69.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.13),
              image: DecorationImage(
                image: NetworkImage(place["imageUrl"]),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 16. w),

          // **右侧信息**
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CommonText(
                place["placeName"],
                theme.textStyleExtension.placeListItemTitle
              ),
              SizedBox(height: 4.w),
              Row(
                children: [
                  Icon(Icons.location_on, size: 10.sp, color: Color(0xFFC6C6C6)),
                  SizedBox(width: 6.w),
                  CommonText(
                    place["distance"],
                    theme.textStyleExtension.placeListItemMileage
                  ),
                  SizedBox(width: 6.w),
                  CommonText("•", theme.textStyleExtension.placeListItemMileage),
                  SizedBox(width: 6.w),
                  Icon(Icons.star, size: 14.sp, color: Colors.amber),
                  SizedBox(width: 6.w),
                  CommonText(
                    place["rating"].toString(),
                    theme.textStyleExtension.placeListItemMileage
                  ),
                ],
              ),
              SizedBox(height: 4.w),
              Row(
                children: [
                  CommonText(
                    formattedDate,
                    theme.textStyleExtension.placeListItemDateTime
                  ),
                  SizedBox(width: 6. w),
                  Container(
                    width: 1.w,
                    height: 16 .w,
                    color: theme.themeData.colorScheme.tertiary, // ✅ 颜色 #C6C6C6
                  ),
                  SizedBox(width: 6 .w),
                  CommonText(
                    formattedTime,
                    theme.textStyleExtension.placeListItemDateTime
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

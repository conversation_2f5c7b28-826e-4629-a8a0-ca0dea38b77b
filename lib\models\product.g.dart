// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      productName: json['productName'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      description: json['description'] as String,
      status: $enumDecode(_$ProductStatusEnumMap, json['status']),
      fragileItems: JsonUtil.boolFromJson(json['fragileItems']),
      sku: json['sku'] as String?,
      brand: json['brand'] as String?,
      attributes: json['attributes'] as Map<String, dynamic>?,
      photoURL: (json['photoURL'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'productName': instance.productName,
      'price': instance.price,
      'currency': instance.currency,
      'description': instance.description,
      'fragileItems': JsonUtil.boolToJson(instance.fragileItems),
      'sku': instance.sku,
      'brand': instance.brand,
      'attributes': instance.attributes,
      'status': _$ProductStatusEnumMap[instance.status]!,
      'photoURL': instance.photoURL,
    };

const _$ProductStatusEnumMap = {
  ProductStatus.active: 'Active',
  ProductStatus.deactived: 'deactived',
};

import 'dart:ui';

class ThemeLayout {

  // Screen size
  static double screenWidth = 403;
  static double screenHeight = 861;

  // Common
  late double tileBorderWidth;
  late double tileBorderRadiusSmall;

  late double inputFieldWidth;
  late double inputFieldHeight;
  late double inputFieldCircular;
  late double inputFieldBorderWidth;

  late double longButtonWidth;
  late double longButtonHeight;
  late double longButtonCircular;

  // Header
  late double leftArrowBlackSize;
  late double leftArrowYellowSize;
  late double leftArrowYellowBgSize;
  late double leftArrowYellowBgCircular;

  // Bottom
  late double bottomNaviBarHeight;
  late double bottomNaviItemBoxWidth;
  late double bottomNaviItemBoxHeight;
  late double bottomNaviItemIconSize;
  late double bottomNaviItemIconSpaceAfter;

  // Auth page
  late double authAvatarHeaderLineHeight;
  late double authAvatarHeaderLineHeight2;
  late double authTitleBarHeight;
  late double authWhiteSectionCircular;
  late Offset authWhiteSectionShadowOffset;
  late double authWhiteSectionShadowBlur;
  late double authWhiteSectionShadowSpread;

  late double authTextFieldWidth;
  late double authTextFieldHeight;
  late double authTextFieldCircular;
  late double authTextFieldBorderWidth;
  late double authTextFieldPaddingHorizontal;
  late double authTextFieldPaddingVertical;
  late double authTextFieldClearIconSize;
  late double authTextFieldPasswordVisibilityIconSize;
  late double authButtonWidth;
  late double authButtonHeight;
  late double authButtonCircular;
  late double authAlterButtonWidth;
  late double authAlterButtonHeight;
  late double authLabelTitleWidth;
  late double authLabelDescHeight;
  late double authLabelTitlePaddingLeft;
  late double authLabelLineSpace;

  late double authWelcomeAvatarSize;

  late double authBackButtonIconWidth;
  late double authBackButtonIconHeight;
  late double authBackButtonIconPaddingTop;
  late double authBackButtonIconPaddingLeft;
  late double authBackButtonBgSize;
  late double authBackButtonBgCircular;
  late Offset authBackButtonBgShadowOffset;
  late double authBackButtonBgShadowBlur;
  late double authBackButtonBgShadowSpread;

  late double authAvatarSize;
  late Offset authAvatarShadowOffset;
  late double authAvatarShadowBlur;
  late double authAvatarShadowSpread;
  late double authAvatarTitleMarginTop;
  late double authAvatarPaddingTop;
  late double authAvatarPaddingLeft;

  late double authBackgroundImageWidth;
  late double authBackgroundImageHeight;
  late double authBackgroundImagePaddingLeft;
  late double authBackgroundImagePaddingTop;

  late double authPetBreedBgSize;
  late double authPetBreedBgCircular;
  late double authPetBreedVectorWidth;
  late double authPetBreedVectorHeight;
  late double authPetBreedBallLargeBgSize;
  late double authPetBreedBallLargeSize;
  late double authPetBreedBallSmallBgSize;
  late double authPetBreedBallSmallSize;
  late double authPetBreedDogSize;
  late double authPetBreedCatSize;
  late double authPetBreedOtherSize;
  late double authPetAddAvatarBgLarge1Size;
  late double authPetAddAvatarBgLarge1BorderWidth;
  late double authPetAddAvatarBgLarge2Size;
  late double authPetAddAvatarBgLarge2BorderWidth;
  late double authPetAddAvatarBgSize;
  late double authPetAddAvatarIconSize;
  late double authPetAddAvatarImageSize;

  late double authPetAddAvatarCameraBgSize;
  late double authPetAddAvatarCameraBgCircular;
  late Offset authPetAddAvatarCameraBgShadowOffset;
  late double authPetAddAvatarCameraBgShadowBlur;
  late double authPetAddAvatarCameraBgShadowSpread;

  late double authPetAddInfoBgLarge1Size;
  late double authPetAddInfoBgLarge1BorderWidth;
  late double authPetAddInfoBgLarge2Size;
  late double authPetAddInfoBgLarge2BorderWidth;
  late double authPetAddInfoBgSize;
  late double authPetAddInfoIconSize;
  late double authPetAddInfoImageSize;

  late double authPetAddInfoCameraBgSize;
  late double authPetAddInfoCameraBgCircular;
  late Offset authPetAddInfoCameraBgShadowOffset;
  late double authPetAddInfoCameraBgShadowBlur;
  late double authPetAddInfoCameraBgShadowSpread;

  // Profile page
  late double profileAvatarLargeSize;
  late double profileAvatarMediumSize;
  late double profileAvatarMediumBorderWidth;
  late double profileAvatarSmallSize;
  late double profileAvatarCameraBarWidth;
  late double profileAvatarCameraBarHeight;
  late double profileAvatarCameraBarCircular;
  late Offset profileAvatarCameraBarShadowOffset;
  late double profileAvatarCameraBarShadowBlur;
  late double profileAvatarCameraBarShadowSpread;
  late double profileAvatarCameraIconWidth;
  late double profileAvatarCameraIconHeight;
  late double profileAddPetIconSize; // + icon
  late double profileSettingListIconSize;
  late double profileSettingListIconBgSize;
  late double profileSettingListIconBgBorderWidth;
  late double profileSettingRightArrowSize;
  late double profileContactInfoIconSize;
  late double profileAddPetAvatarLargeSize;
  late double profileAddPetAvatarSize;
  late double profilePetAvatarSize;

  // Event page
  late double eventAvatarLargeSize;
  late double eventAvatarLargeBgSize;
  late double eventAvatarSize;
  late double eventAvatarBgSize;

  late double eventPetProfileEditIconSize;
  late double eventPetProfileEditIconBgSize;
  late double eventPetProfileEditIconBgBorderWidth;
  late double eventPetProfileListIconSize;
  late double eventPetProfileListIconBgSize;
  late double eventPetProfileListIconBgBorderWidth;

  late double eventPostGridWidth;
  late double eventPostTileWidth;
  late double eventPostTileHeight;
  late double eventPostTilePadding;
  late double eventPostTileGap;
  late double eventPostImageWidth;
  late double eventPostImageHeight;
  late double eventPostImageCircular;

  late double eventEventTileWidth;
  late double eventEventTileHeight;
  late double eventEventTileCircular;
  late double eventEventTilePadding;
  late double eventEventTileBorderWidth;
  late double eventEventImageSize;
  late double eventEventSubTileWidth;
  late double eventEventSubTileHeightDate;
  late double eventEventSubTileHeightDetail;
  late double eventEventDateIconWidth;
  late double eventEventDateIconHeight;
  late double eventEventPlaceIconSize;

  late double eventPlaceTileWidth;
  late double eventPlaceTileHeight;
  late double eventPlaceTileCircular;
  late double eventPlaceTilePadding;
  late double eventPlaceTileGap;
  late double eventPlaceImageSize;
  late double eventPlaceImageCircular;
  late double eventPlaceSubTileWidth;
  late double eventPlaceSubTileHeightTitle;
  late double eventPlaceSubTileHeightInfo;
  late double eventPlaceSubTileHeightDate;
  late Offset eventPlaceTileShadow1Offset;
  late double eventPlaceTileShadow1Blur;
  late double eventPlaceTileShadow1Spread;
  late Offset eventPlaceTileShadow2Offset;
  late double eventPlaceTileShadow2Blur;
  late double eventPlaceTileShadow2Spread;

  // Geo location page
  late double geoMyLocationIconSize;
  late double geoMyLocationIconMarginRight;
  late double geoMyLocationIconMarginBottom;
  late double geoCommonLocationDotSize;
  late double geoPetLocationAvatarSize;
  late double geoPetLocationIconWidth;
  late double geoPetLocationIconHeight;
  late double geoPlaceIconColorSize;
  late double geoPlaceIconWhiteSize;
  late double geoPlaceIconParkWidth;
  late double geoPlaceIconParkHeight;
  late double geoPlaceIconTrailWidth;
  late double geoPlaceIconTrailHeight;
  late double geoFilterButtonWidthOutdoor;
  late double geoFilterButtonWidthPark;
  late double geoFilterButtonWidthTrail;
  late double geoFilterButtonWidthVet;
  late double geoFilterButtonWidthPetStore;
  late double geoFilterButtonWidthDogFriendlyRestaurant;
  late double geoFilterButtonWidthFavor;
  late double geoFilterButtonHeight;
  late double geoFilterButtonBorderRadius;
  late double geoFilterButtonSelectedBorderWidth;
  late double geoPlaceInfoListHeightLong;
  late double geoPlaceInfoListHeightShort;
  late double geoPlaceInfoCardWidth;
  late double geoPlaceInfoCardHeight;
  late double geoPlaceInfoCardImageWidth;
  late double geoPlaceInfoCardImageHeight;
  late double geoPlaceInfoCardImageBorderRadius;
  late double geoPlaceInfoCardTextPanelWidth;
  late double geoPlaceInfoCardTextPanelHeight;
  late double geoPlaceInfoCardTitleHeight;
  late double geoPlaceInfoCardGeoInfoHeight;
  late double geoPlaceInfoCardStatusHeight;
  late double geoPlaceInfoCardTagHeight;
  late double geoPlaceInfoCardCheckInButtonWidth;
  late double geoPlaceInfoCardCheckInButtonHeight;
  late double geoPlaceInfoCardCheckInButtonBorderRadius;
  late double geoPlaceInfoCardFavorIconWidth;
  late double geoPlaceInfoCardFavorIconHeight;
  late double geoPlaceInfoCardFavorButtonSize;
  late double geoPlaceInfoCardFavorButtonBorderRadius;

  late double geoSearchTextButtonBgSize;
  late double geoSearchTextButtonIconSize;
  late double geoSearchTextFieldWidth;
  late double geoSearchTextFieldHeight;
  late double geoSearchTextFieldCircular;
  late double geoSearchTextFieldPaddingHorizontal;
  late double geoSearchTextFieldPaddingVertical;

}

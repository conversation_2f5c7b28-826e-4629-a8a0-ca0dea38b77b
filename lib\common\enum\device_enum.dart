import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum DeviceMessageType {

  moProvision('MO_00', 'device.message.type.mo.provision'),
  moStatus('MO_01', 'device.message.type.mo.status'),
  moAlert('MO_91', 'device.message.type.mo.alert'),
  moGeoLocation('MO_10', 'device.message.type.mo.record.geo'),
  moFeeding('MO_11', 'device.message.type.mo.record.feeding'),
  ;

  final String code;
  final String t18Key;
  const DeviceMessageType(this.code, this.t18Key);

  // Factory constructors to create an object
  factory DeviceMessageType.fromCode(String code) {
    return DeviceMessageType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
  factory DeviceMessageType.fromT18Key(String t18Key) {
    return DeviceMessageType.values.firstWhere((element) => element.t18Key == t18Key, orElse: () => throw ArgumentError('Invalid t18 key: $t18Key'));
  }
}

@JsonEnum(valueField: 'code')
enum DeviceAlertMessageType {

  moAlertLowBattery('MO91_01', 'device.message.alert.battery.low'),
  moAlertNoGps('MO91_02', 'device.message.alert.gps.not.available'),
  ;

  final String code;
  final String t18Key;
  const DeviceAlertMessageType(this.code, this.t18Key);

  // Factory constructors to create an object
  factory DeviceAlertMessageType.fromCode(String code) {
    return DeviceAlertMessageType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
  factory DeviceAlertMessageType.fromT18Key(String t18Key) {
    return DeviceAlertMessageType.values.firstWhere((element) => element.t18Key == t18Key, orElse: () => throw ArgumentError('Invalid t18 key: $t18Key'));
  }
}

@JsonEnum(valueField: 'code')
enum DeviceModel {

  piFI('P01', 'device.model.pifi.01'),
  locator('L01', 'device.model.locator.01'),
  ;

  final String code;
  final String t18Key;
  const DeviceModel(this.code, this.t18Key);

  // Factory constructors to create an object
  factory DeviceModel.fromCode(String code) {
    return DeviceModel.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
  factory DeviceModel.fromT18Key(String t18Key) {
    return DeviceModel.values.firstWhere((element) => element.t18Key == t18Key, orElse: () => throw ArgumentError('Invalid t18 key: $t18Key'));
  }
}

@JsonEnum(valueField: 'code')
enum DeviceType {

  feedingMachine('FEEDING_MACHINE', 'device.type.feeding.machine'),
  locator('LOCATOR', 'device.type.locator'),
  ;

  final String code;
  final String t18Key;
  const DeviceType(this.code, this.t18Key);

  // Factory constructors to create an object
  factory DeviceType.fromCode(String code) {
    return DeviceType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
  factory DeviceType.fromT18Key(String t18Key) {
    return DeviceType.values.firstWhere((element) => element.t18Key == t18Key, orElse: () => throw ArgumentError('Invalid t18 key: $t18Key'));
  }
}

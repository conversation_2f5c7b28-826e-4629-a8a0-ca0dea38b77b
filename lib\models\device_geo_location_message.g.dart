// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_geo_location_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceGeoLocationMessage _$DeviceGeoLocationMessageFromJson(
        Map<String, dynamic> json) =>
    DeviceGeoLocationMessage(
      sid: json['sid'] as String?,
      pid: json['pid'] as String?,
      deviceId: json['deviceId'] as String,
      generateDate: (json['generateDate'] as num?)?.toInt(),
      sendDate: (json['sendDate'] as num?)?.toInt(),
      receiveDate: (json['receiveDate'] as num?)?.toInt(),
      location: JsonUtil.geoFirePointFromJson(
          json['location'] as Map<String, dynamic>?),
      altitude: (json['altitude'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DeviceGeoLocationMessageToJson(
        DeviceGeoLocationMessage instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'pid': instance.pid,
      'deviceId': instance.deviceId,
      'generateDate': instance.generateDate,
      'sendDate': instance.sendDate,
      'receiveDate': instance.receiveDate,
      'location': JsonUtil.geoFirePointToJson(instance.location),
      'altitude': instance.altitude,
    };

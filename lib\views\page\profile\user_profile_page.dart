import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

import '../../../models/pet.dart';
import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserProfilePage extends StatefulWidget {
  const UserProfilePage({super.key});

  @override
  UserProfilePageState createState() => UserProfilePageState();
}

class UserProfilePageState extends State<UserProfilePage> {
  late Future<UserProfilePageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserProfilePageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserProfilePageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Column(
                children: [
                  _buildWhiteSection(controller),
                  _buildPetSection(controller),
                  _buildInformationSection(controller),
                ],
              ),
            );
          }
        });
  }

  Widget _buildWhiteSection(UserProfilePageController controller) {
    return SizedBox(
      width: 403.w,
      height: 125.w,
      child: Container(
        decoration: BoxDecoration(
          color: controller.theme.themeData.colorScheme.onPrimary,
          borderRadius: BorderRadius.only(
            bottomRight: Radius.circular(65.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              offset: Offset(0, 0),
              blurRadius: 18,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Stack(
          children: [
            _buildTopSection(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSection(UserProfilePageController controller) {
    return Padding(
      padding: EdgeInsets.only(top: 38.w, left: 38.w, right: 38.w),
      child: SizedBox(
        width: 375.w,
        height: 88.w,
        child: Row(
          children: [
            Container(
              width: 42.w,
              height: 42.w,
              child: AuthWidgetBuilder.buildAvatar(
                controller.theme,
                avatar: controller.pageHeaderUserAvatar.value,
              ),
            ),
            SizedBox(width: 10.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CommonText(
                  "hello, ".t18,
                  controller.theme.textStyleExtension.userAvatarLarge,
                ),
                CommonText(
                  controller.userData?.name?.isNotEmpty == true
                      ? controller.userData!.name!
                      : "add.profile".t18,
                  controller.userData?.name?.isNotEmpty == true
                      ? controller.theme.textStyleExtension.petAvatar
                      : controller.theme.textStyleExtension.userAvatarLargeTip,
                ),
              ],
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                Get.toNamed("/userSetting");
              },
              icon: Icon(
                Icons.menu,
                size: 24.sp,
                color: controller.theme.themeData.colorScheme.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPetSection(UserProfilePageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 30.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                "your.pets".t18,
                controller.theme.textStyleExtension.userProfileBody1,
              ),
              SizedBox(height: 12.w),
              SizedBox(
                height: 110.w,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: (controller.petList?.isEmpty ?? true)
                      ? 1 // 只有 Add 按钮
                      : controller.petList!.length + 1, // 有宠物 + Add
                  separatorBuilder: (_, __) => SizedBox(width: 12.w),
                  itemBuilder: (context, index) {
                    // 空列表只显示 Add 按钮
                    if (controller.petList?.isEmpty ?? true) {
                      return _buildAddPetButton(controller);
                    }
                    // 有宠物时，正常构建 pet + Add
                    if (index < controller.petList!.length) {
                      final pet = controller.petList![index];
                      return _buildPetAvatarWithFuture(
                        controller.theme,
                        controller._authService.userAccount.value!,
                        pet,
                        controller,
                      );
                    } else {
                      return _buildAddPetButton(controller);
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPetAvatarWithFuture(
    ThemePlugin theme,
    UserAccount account,
    Pet pet,
    UserProfilePageController controller,
  ) {
    return GestureDetector(
      onTap: () {
        Get.toNamed("/petProfile", arguments: {"selectedPet": pet});
      },
      child: FutureBuilder<Widget?>(
        future: ProfileWidgetBuilder.buildPetAvatar(
          theme,
          userId: account.sid!,
          petId: pet.sid!,
          avatar: pet.avatar,
          size: 74.52,
        ),
        builder: (context, snapshot) {
          Widget avatarWidget;
          if (snapshot.connectionState == ConnectionState.waiting) {
            avatarWidget = Container(
              width: 74.52.w,
              height: 74.52.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey.shade300,
              ),
            );
          } else if (snapshot.hasError || snapshot.data == null) {
            avatarWidget = Container(
              width: 74.52.w,
              height: 74.52.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.shade100,
              ),
              child: Icon(Icons.error, color: Colors.red),
            );
          } else {
            avatarWidget = Container(
              width: 74.52.w,
              height: 74.52.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: controller.theme.themeData.colorScheme.secondary,
                  width: 2.w,
                ),
              ),
              child: ClipOval(
                child: snapshot.data!,
              ),
            );
          }
          return Column(
            children: [
              avatarWidget,
              SizedBox(height: 4.w),
              SizedBox(
                width: 74.52.w,
                child: Center(
                  child: CommonText(
                    pet.name ?? "Unnamed",
                    controller.theme.textStyleExtension.petAvatar,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPetAvatar(
      String imagePath, String name, UserProfilePageController controller) {
    return Column(
      children: [
        Container(
          width: 74.52.w,
          height: 74.52.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
                color: controller.theme.themeData.colorScheme.secondary,
                width: 2.w),
          ),
          child: ClipOval(
            child: Image.asset(imagePath, fit: BoxFit.cover),
          ),
        ),
        SizedBox(height: 4.w),
        CommonText(
          name,
          controller.theme.textStyleExtension.petAvatar,
        ),
      ],
    );
  }

  Widget _buildAddPetButton(UserProfilePageController controller) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            Get.toNamed("/selectPet");
          },
          child: Container(
            width: 74.52.w,
            height: 74.52.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: controller.theme.themeData.colorScheme.primary,
                width: 2.w,
              ),
            ),
            child: Center(
              child: Icon(
                Icons.add,
                color: controller.theme.themeData.colorScheme.primary,
                size: 32.sp,
              ),
            ),
          ),
        ),
        SizedBox(height: 4.w),
        CommonText(
          "add.new".t18,
          controller.theme.textStyleExtension.petAvatar,
        ),
      ],
    );
  }

  Widget _buildInformationSection(UserProfilePageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 30.w), // 原来的 top
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                "contact.info".t18,
                controller.theme.textStyleExtension.userProfileBody1,
              ),
              SizedBox(height: 12.w),
              // _buildInfoRow(
              //     Icons.email,
              //     controller.userAccount.value!.email ?? 'Add your email',
              //     controller),
              // SizedBox(height: 8.w),
              // _buildInfoRow(
              //     Icons.phone,
              //     controller.userAccount.value!.phoneNumber ?? 'Add your phone',
              //     controller),
              // SizedBox(height: 8.w),
              // _buildInfoRow(Icons.location_on,
              //     controller.getFormattedLocation(), controller),
              _buildInfoRow(
                Icons.email,
                controller.userAccount.value!.email ?? 'Add your email',
                controller,
                placeholder: 'Add your email',
                routeIfPlaceholder: '/linkEmail',
              ),

              SizedBox(height: 8.w),
              _buildInfoRow(
                Icons.phone,
                controller.userAccount.value!.phoneNumber ?? 'Add your phone',
                controller,
                placeholder: 'Add your phone',
                routeIfPlaceholder: '/userSettingPhone',
              ),

              SizedBox(height: 8.w),
              _buildInfoRow(
                Icons.location_on,
                controller.getFormattedLocation(),
                controller,
                placeholder: 'Add your address',
                routeIfPlaceholder: '/userSettingAddress',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String text,
    UserProfilePageController controller, {
    String? placeholder,
    String? routeIfPlaceholder,
  }) {
    final isPlaceholder = placeholder != null && text == placeholder;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (isPlaceholder && routeIfPlaceholder != null) {
          Get.toNamed(routeIfPlaceholder);
        }
      },
      child: Row(
        children: [
          Icon(
            icon,
            color: controller.theme.themeData.colorScheme.primary,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          CommonText(
            text,
            controller.theme.textStyleExtension.petAvatarMore,
          ),
        ],
      ),
    );
  }

// Widget _buildInfoRow(
  //     IconData icon, String text, UserProfilePageController controller) {
  //   return Row(
  //     children: [
  //       Icon(
  //         icon,
  //         color: controller.theme.themeData.colorScheme.primary,
  //         size: 20.sp,
  //       ),
  //       SizedBox(width: 8.w),
  //       CommonText(text, controller.theme.textStyleExtension.petAvatarMore),
  //     ],
  //   );
  // }
}

class UserProfilePageController extends GetxController {
  // Theme plugin
  final AuthService _authService = AuthService.instance;
  var userAccount = Rx<UserAccount?>(null);
  final UserService _userService = UserService();
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  UserData? userData;
  final PetService _petService = PetService();
  List<Pet>? petList;
  late ThemePlugin theme;

  Future<UserProfilePageController> init() async {
    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());

    userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(theme,
        userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);

    userAccount.value = _authService.userAccount.value;
    //userData = _authService.userData.value;

    // Pet pet = Pet.create(
    //   owner: _authService.userAccount.value!.sid!,
    //   name: 'Pet Name 1',
    // );
    // await _petService.addOwnedPet(pet);
    //
    // Pet pet1 = Pet.create(
    //   owner: _authService.userAccount.value!.sid!,
    //   name: 'Pet Name 2',
    // );
    // await _petService.addOwnedPet(pet1);
    //
    // Pet pet2 = Pet.create(
    //   owner: _authService.userAccount.value!.sid!,
    //   name: 'Pet Name 3',
    // );
    // await _petService.addOwnedPet(pet2);

    petList = await _petService.getOwnedPets(userAccount.value!.sid!);
    // print(petList);
    print(userData);
    // UserAccount? account = _authService.userAccount.value;
    // print(account?.phoneNumber);
    // User? user = _authService.currentUser.value;
    // print(user?.phoneNumber);
    // ServiceResponse<String?> response = await _authService.updateUserPassword(oldPassword, newPassword1);
    //print(userAccount.value);

    // account = UserAccount.copyFrom(_authService.userAccount.value!);
    // account?.phoneNumber = '**********';
    // await _userService.updateUserAccount(account!);

    // pageHeaderUserName.value = CommonText(
    //   'auth.label.reset.password.hello'.t19({
    //     'name': userAccount!.name ?? 'auth.label.reset.password.default.name'.t18,
    //   }),
    //   theme.textStyleExtension.userAvatar,
    // );
    // await userService.createUserAccount(User: acc, data: data);

    return this;
  }

  String getFormattedLocation() {
    final city =
        userData?.location?.locality ?? userData?.location?.subLocality;
    final state = userData?.location?.administrativeArea;
    final country = userData?.location?.region;

    final displayCity = city?.isNotEmpty == true ? city : "userCity";
    final displayState = state?.isNotEmpty == true ? state : "userState";
    final displayCountry =
        country?.isNotEmpty == true ? country : "userCountry";

    final isAllDefault = displayCity == "userCity" &&
        displayState == "userState" &&
        displayCountry == "userCountry";

    return isAllDefault
        ? "Add your address"
        : "$displayCity, $displayState, $displayCountry";
  }
}

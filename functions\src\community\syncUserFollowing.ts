import {onDocumentCreated} from "firebase-functions/v2/firestore";
import {logger} from "firebase-functions/v2";
import {getFirestore} from "firebase-admin/firestore";
import {v4 as uuidv4} from "uuid";

// Initialize Firebase
// initializeApp();

// Triggered by a new following document creation
// {followingId} is the userId of the followee (data.otherId = {followingId})
export const syncUserFollowing = onDocumentCreated(
  "user/{userId}/following/{followingId}",
  async (event) => {
    try {
      // Get the document data
      const snapshot = event.data;
      if (!snapshot) {
        logger.error("No data associated with the event");
        return;
      }

      const userCol = "user_data";
      const followingCol = "following";
      const followerCol = "follower";
      const friendCol = "friend";
      const userIdField = "uid";
      const otherIdField = "other_id";
      const data = snapshot.data();
      const userId = event.params.userId;
      const followingId = event.params.followingId;

      // Validate required fields
      if (!data.uid) {
        logger.log("No user ID found in the message", {userId, followingId});
        return;
      }
      if (!data.otherId) {
        // eslint-disable-next-line max-len
        logger.log("No following ID found in the message", {
          userId,
          followingId,
        });
        return;
      }

      // Get Firestore instance
      const firestore = getFirestore();

      // Check if follower and followee documents exist
      const followerDoc = firestore.doc(`${userCol}/${data.uid}`);
      const followeeDoc = firestore.doc(`${userCol}/${data.otherId}`);

      const followerSnap = await followerDoc.get();
      const followeeSnap = await followeeDoc.get();

      if (!followeeSnap.exists || !followerSnap.exists) {
        // eslint-disable-next-line max-len
        logger.warn("User document missed", {
          follower: data.uid,
          followee: data.otherId,
        });
        return;
      }

      // Check if follower is in followee's following, follower and friend
      // data.uid is the sub collection id
      const followeeFollowingQuery = followeeDoc.collection(followingCol).doc(data.uid);
      const followeeFollowerQuery = followeeDoc.collection(followerCol).doc(data.uid);
      const followeeFriendQuery = followeeDoc.collection(friendCol).doc(data.uid);
      const followeeFollowingSnap = await followeeFollowingQuery.get();
      const followeeFollowerSnap = await followeeFollowerQuery.get();
      const followeeFriendSnap = await followeeFriendQuery.get();

      // Get follower's follower and friend
      const followerFollowerDoc = followerDoc.collection(followerCol).doc(data.otherId);
      const followerFriendDoc = followerDoc.collection(friendCol).doc(data.otherId);
      const followerFollowerSnap = await followerFollowerDoc.get();
      const followerFriendSnap = await followerFriendDoc.get();

      // Transaction
      // 1. add follower to followee, if following exist, convert to friend
      // 2. update follower count of other
      // 3. update following count of user
      await firestore.runTransaction(async (transaction) => {

        /// Follower operations

        // Update follower's following count
        transaction.update(followerDoc, {
          following_count: (followerSnap.data()?.following_count ?? 0) + 1,
        });

        // Followee's follower not exist => 添加 follower 到 followee 的 followers 子集合
        if (followerFollowerSnap.exists && !followerFriendSnap.exists) {
          const followerFriendDoc = followerDoc.collection(friendCol).doc(data.other_id);
          transaction.set(followerFriendDoc, {
            is_valid: 1,
            create_date: Date.now(),
            uid: data.uid,
            otherId: data.other_id,
          });

          // Update follower's friend count
          transaction.update(followerDoc, {
            friends_count: (followerSnap.data()?.friends_count ?? 0) + 1,
          });
        }

        /// Followee operations

        // Followee's follower not exist => 添加 follower 到 followee 的 followers 子集合
        if (!followeeFollowerSnap.exists) {
          const followeeFollowerDoc = followeeDoc.collection(followerCol).doc(data.uid);
          transaction.set(followeeFollowerDoc, {
            is_valid: 1,
            create_date: Date.now(),
            uid: data.otherId,
            otherId: data.uid,
          });

          // Update followee's follower count
          transaction.update(followeeDoc, {
            follower_count: (followeeSnap.data()?.follower_count ?? 0) + 1,
          });
        }

        // Followee's following exist => if friend not exist, 添加 follower 到 followee 的 friend 子集合
        if (followeeFollowingSnap.exists && !followeeFriendSnap.exists) {
          const followeeFriendDoc = followeeDoc.collection(friendCol).doc(data.uid);
          transaction.set(followeeFriendDoc, {
            is_valid: 1,
            create_date: Date.now(),
            uid: data.otherId,
            otherId: data.uid,
          });

          // Update followee's friend count
          transaction.update(followeeDoc, {
            friends_count: (followeeSnap.data()?.friends_count ?? 0) + 1,
          });
        }

      });

      logger.log("Successfully synced user following", {
        petId: data.pid,
      });
    } catch (error) {
      logger.error("Failed to sync user following", {
        error: error instanceof Error ? error.message : String(error),
        params: event.params,
      });
      //       throw error; // Re-throw to mark function as failed
    }
  },
);

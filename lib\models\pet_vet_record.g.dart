// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_vet_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetVetRecord _$PetVetRecordFromJson(Map<String, dynamic> json) => PetVetRecord(
      sid: json['sid'] as String,
      time: (json['time'] as num?)?.toInt(),
      location: json['location'] as String?,
      description: json['description'] as String?,
      treat: json['treat'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PetVetRecordToJson(PetVetRecord instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'time': instance.time,
      'location': instance.location,
      'description': instance.description,
      'treat': instance.treat,
      'images': instance.images,
      'notes': instance.notes,
    };

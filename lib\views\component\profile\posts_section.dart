import 'package:flutter/material.dart';

class PostsSection extends StatelessWidget {
  final List<Map<String, dynamic>> posts = [
    {
      "postId": "1",
      "userId": "user123",
      "contentText": "Friends day!!!",
      "mediaUrls": [
        "https://images.pexels.com/photos/4587993/pexels-photo-4587993.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 12,
      "commentCount": 5,
    },
    {
      "postId": "2",
      "userId": "user456",
      "contentText": "<PERSON> was having fun~",
      "mediaUrls": [
        "https://unsplash.com/photos/people-sitting-at-the-table-2pPw5Glro5I"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 25,
      "commentCount": 8,
    },
    {
      "postId": "3",
      "userId": "user789",
      "contentText": "Golden sunset walk",
      "mediaUrls": [
        "https://images.pexels.com/photos/1108098/pexels-photo-1108098.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 18,
      "commentCount": 3,
    },
    {
      "postId": "4",
      "userId": "user987",
      "contentText": "Playtime in the snow",
      "mediaUrls": [
        "https://images.pexels.com/photos/1108100/pexels-photo-1108100.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 30,
      "commentCount": 12,
    },
    {
      "postId": "5",
      "userId": "user987",
      "contentText": "Playtime in the snow",
      "mediaUrls": [
        "https://images.pexels.com/photos/1108100/pexels-photo-1108100.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 30,
      "commentCount": 12,
    },
    {
      "postId": "4",
      "userId": "user987",
      "contentText": "Playtime in the snow",
      "mediaUrls": [
        "https://images.pexels.com/photos/1108100/pexels-photo-1108100.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 30,
      "commentCount": 12,
    },
    {
      "postId": "5",
      "userId": "user987",
      "contentText": "Playtime in the snow",
      "mediaUrls": [
        "https://images.pexels.com/photos/1108100/pexels-photo-1108100.jpeg"
      ],
      "createdAt": DateTime.now(),
      "likeCount": 30,
      "commentCount": 12,
    },
  ];

  @override
  Widget build(BuildContext context) {
    double w = MediaQuery.of(context).size.width / 403;
    double h = MediaQuery.of(context).size.height / 822;

    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: 38 * w, vertical: 8 * h),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // ✅ 每行两个 `Post`
        crossAxisSpacing: 8 * w, // ✅ `Post` 之间间隔 8px
        mainAxisSpacing: 8 * h, // ✅ 行间隔 8px
        //childAspectRatio: 178 / 169, // ✅ 控制 `Post` 宽高比
      ),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        var post = posts[index];
        return _buildPostItem(post, w, h);
      },
    );
  }

  Widget _buildPostItem(Map<String, dynamic> post, double w, double h) {
    return Container(
      //width: 178 * w,
      //height: 169 * h,
      padding: EdgeInsets.symmetric(horizontal: 8*w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0x14162233), // ✅ `#162233` 透明度 8%
            offset: const Offset(0, 8), // ✅ `y: 8px`
            blurRadius: 16, // ✅ `blur: 16px`
            spreadRadius: -4, // ✅ `spread: -4px`
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(2), // ✅ `border-radius: 2px`
            child: Image.network(
              post["mediaUrls"][0],
              width: 162 * w,
              height: 115 * h,
              //fit: BoxFit.cover,
            ),
          ),
          Text(
            post["contentText"],
            style: const TextStyle(
              fontFamily: 'Manrope',
              fontWeight: FontWeight.w500, // ✅ `font-weight: 500`
              fontSize: 12, // ✅ `font-size: 12px`
              color: Color(0xFF262626),
            ),
          ),
          //SizedBox(height: 4 * h),

          // 📄 **副标题**
          const Text(
            "Lily was having fun~",
            style: TextStyle(
              fontFamily: 'Manrope',
              fontWeight: FontWeight.w400, // ✅ `font-weight: 400`
              fontSize: 10, // ✅ `font-size: 10px`
              color: Color(0xFFC6C6C6),
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'device_message_header.dart';
import 'geo_location.dart';

part 'device_feeding_message.g.dart';

@JsonSerializable()
class DeviceFeedingMessage extends DeviceMessageHeader {

  List<String>? foodTypes;
  String? amount;
  List<String>? images;
  String? notes;

  DeviceFeedingMessage({
    super.sid,
    super.pid,
    required super.deviceId,
    super.generateDate,
    super.sendDate,
    super.receiveDate,
    this.foodTypes,
    this.amount,
    this.images,
    this.notes,
  });

  factory DeviceFeedingMessage.fromJson(Map<String, dynamic> json) => _$DeviceFeedingMessageFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$DeviceFeedingMessageToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory DeviceFeedingMessage.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return DeviceFeedingMessage.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory DeviceFeedingMessage.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return DeviceFeedingMessage.fromJson(jsonData);
  }

  factory DeviceFeedingMessage.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return DeviceFeedingMessage.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(DeviceFeedingMessage? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? sid,
    String? pid,
    required String deviceId,
    int? generateDate,
    int? sendDate,
    int? receiveDate,
    List<String>? foodTypes,
    String? amount,
    List<String>? images,
    String? notes,

  }) {

    return DeviceFeedingMessage(
      sid: sid?? uuid.v4(),
      pid: pid,
      deviceId: deviceId,
      generateDate: generateDate,
      sendDate: sendDate,
      receiveDate: receiveDate,
      foodTypes: foodTypes,
      amount: amount,
      images: images,
      notes: notes,

    );
  }

  static DeviceFeedingMessage? copyFrom(DeviceFeedingMessage? other) {
    return other == null ? null : DeviceFeedingMessage(
      sid: other.sid,
      pid: other.pid,
      deviceId: other.deviceId,
      generateDate: other.generateDate,
      sendDate: other.sendDate,
      receiveDate: other.receiveDate,
      foodTypes: other.foodTypes,
      amount: other.amount,
      images: other.images,
      notes: other.notes,
    );
  }

  static String get collection => 'device_feeding_message';

  @override
  String toString() {
    return jsonEncode(this);
  }

}

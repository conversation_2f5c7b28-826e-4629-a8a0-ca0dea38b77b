import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'colors/color_extension.dart';
import 'text_schemes/text_style_extension.dart';
import 'layouts/theme_layout.dart';
import 'theme_service.dart';

class ThemePlugin {

  // Theme and layout
  late ThemeData themeData;
  late ThemeLayout layout;
  late ColorExtension colorExtension;
  late TextStyleExtension textStyleExtension;

  // Async initialization
  Future<ThemePlugin> init() async {

    // Init theme and layout
    ThemeService themeService = ThemeService.instance;
    themeData = themeService.themeData;
    layout = themeService.themeLayout;
    colorExtension = themeService.themeData.extensions.entries.first.value as ColorExtension;
    textStyleExtension = themeService.textStyleExtension;

    return this;
  }
}

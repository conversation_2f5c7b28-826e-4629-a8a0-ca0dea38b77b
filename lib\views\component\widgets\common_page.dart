import 'package:flutter/material.dart';

import 'package:onenata_app/views/theme/layouts/theme_layout_i.dart';
import 'package:onenata_app/views/theme/text_schemes/text_schemes_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

class CommonPage extends StatelessWidget {

  final ThemePlugin theme;
  final String? title;
  final PreferredSizeWidget? titleBar;
  final Widget body;
  final Widget? bottomBar;
  final Color? backgroundColor;

  const CommonPage({
    super.key,
    required this.theme,
    this.title,
    this.titleBar,
    required this.body,
    this.bottomBar,
    this.backgroundColor,
  })
  ;

  @override
  Widget build(BuildContext context) {

    return Theme(
      data: theme.themeData,
      child: Scaffold(
        resizeToAvoidBottomInset: false, // To avoid resize when keyboard displayed
        appBar: titleBar?? (
          (title == null || title!.isEmpty) ? null : null
        ),
        body: body,
        backgroundColor: backgroundColor?? theme.themeData.colorScheme.surface,
        bottomNavigationBar: bottomBar,
      ),
    );
  }
}

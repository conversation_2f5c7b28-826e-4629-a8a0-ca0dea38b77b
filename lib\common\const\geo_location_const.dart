import 'package:google_maps_flutter/google_maps_flutter.dart';

class GeoLocationConst {
  // Default configs
  static const LatLng defaultLatLng = LatLng(49.1885626, -123.1272558);
  static const double defaultZoom = 14.0; // street level, with some buildings
  static const double defaultTilt = 0.0; // 2D view
  static const double defaultBearing = 0.0; // North is up
  static const CameraPosition defaultLocation = CameraPosition(
    target: defaultLatLng,
    zoom: defaultZoom,
    tilt: defaultTilt,
    bearing: defaultBearing,
  );

  // Pet-friendly place types
  static const String dogParkType = 'dog_park';
  static const String offLeashTrailType = 'off_leash_trail';
  static const String petFriendlyParkType = 'pet_friendly_park';

  // Static values
  static const earthRadius = 6378137.0; // Earth's radius in meters
}

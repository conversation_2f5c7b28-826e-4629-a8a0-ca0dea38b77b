// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_period.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoPeriod _$GeoPeriodFromJson(Map<String, dynamic> json) => GeoPeriod(
      open: json['open'] == null
          ? null
          : GeoDatePoint.fromJson(json['open'] as Map<String, dynamic>),
      close: json['close'] == null
          ? null
          : GeoDatePoint.fromJson(json['close'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GeoPeriodToJson(GeoPeriod instance) => <String, dynamic>{
      'open': instance.open,
      'close': instance.close,
    };

import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/appointment_enum.dart';
import 'base_full_model.dart';

part 'bookable_service.g.dart';

/// Bookable Service - 可预约服务
/// 存储在 Firestore collection "bookable-service"
@JsonSerializable()
class BookableService extends BaseFullModel {
  String storeId;                    // 店铺ID
  String serviceId;                  // 服务唯一ID
  String serviceName;                // 服务名称
  ServiceCategory serviceCategory;   // 服务类别
  ServiceBreed serviceBreed;         // 服务对象
  String? description;               // 服务描述
  StoreServiceStatus status;         // 服务状态
  List<String> staffIds;             // 提供此服务的员工ID列表
  int minDuration;                   // 最短服务时间（分钟，必须是15的倍数）
  int maxDuration;                   // 最长服务时间（分钟，必须是15的倍数）
  int defaultDuration;               // 默认服务时间（分钟，必须是15的倍数）
  double basePrice;                  // 基础价格
  Currency currency;                 // 货币单位
  int maxCapacityPerSlot;            // 每个时间段最大接待数量
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isOnlineBookingEnabled;      // 是否开启在线预约
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? requiresApproval;            // 是否需要审核
  String? cancellationPolicy;        // 取消政策
  List<String>? servicePhotos;       // 服务图片
  int totalBookings;                 // 总预约数
  int completedBookings;             // 完成的预约数

  BookableService({
    required this.storeId,
    required this.serviceId,
    required this.serviceName,
    required this.serviceCategory,
    required this.serviceBreed,
    this.description,
    required this.status,
    required this.staffIds,
    required this.minDuration,
    required this.maxDuration,
    required this.defaultDuration,
    required this.basePrice,
    required this.currency,
    required this.maxCapacityPerSlot,
    required this.isOnlineBookingEnabled,
    required this.requiresApproval,
    this.cancellationPolicy,
    this.servicePhotos,
    required this.totalBookings,
    required this.completedBookings,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory BookableService.fromJson(Map<String, dynamic> json) =>
      _$BookableServiceFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$BookableServiceToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  static String get collection => 'bookable-service';

  bool isActive() {
    return status == StoreServiceStatus.active;
  }

  bool isBookable() {
    return isActive() && (isOnlineBookingEnabled ?? false) && staffIds.isNotEmpty;
  }

  bool validateDuration(int duration) {
    return duration >= minDuration && 
           duration <= maxDuration && 
           duration % 15 == 0;
  }
}

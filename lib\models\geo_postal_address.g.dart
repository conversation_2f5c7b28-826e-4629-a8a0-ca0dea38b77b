// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_postal_address.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoPostalAddress _$GeoPostalAddressFromJson(Map<String, dynamic> json) =>
    GeoPostalAddress(
      revision: json['revision'] as String?,
      regionCode: json['regionCode'] as String?,
      languageCode: json['languageCode'] as String?,
      postalCode: json['postalCode'] as String?,
      sortingCode: json['sortingCode'] as String?,
      administrativeArea: json['administrativeArea'] as String?,
      locality: json['locality'] as String?,
      subLocality: json['subLocality'] as String?,
      addressLines: (json['addressLines'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      recipients: (json['recipients'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      organization: json['organization'] as String?,
    )..region = json['region'] as String?;

Map<String, dynamic> _$GeoPostalAddressToJson(GeoPostalAddress instance) =>
    <String, dynamic>{
      'revision': instance.revision,
      'regionCode': instance.regionCode,
      'languageCode': instance.languageCode,
      'postalCode': instance.postalCode,
      'sortingCode': instance.sortingCode,
      'region': instance.region,
      'administrativeArea': instance.administrativeArea,
      'locality': instance.locality,
      'subLocality': instance.subLocality,
      'addressLines': instance.addressLines,
      'recipients': instance.recipients,
      'organization': instance.organization,
    };

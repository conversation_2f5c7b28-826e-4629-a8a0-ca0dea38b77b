import 'package:json_annotation/json_annotation.dart';

part 'device_message_header.g.dart';

@JsonSerializable()
class DeviceMessageHeader {

  String? sid;
  String? pid;
  String deviceId;
  int? generateDate;
  int? sendDate;
  int? receiveDate;

  DeviceMessageHeader({
    this.sid,
    this.pid,
    required this.deviceId,
    this.generateDate,
    this.sendDate,
    this.receiveDate,
  });

  factory DeviceMessageHeader.fromJson(Map<String, dynamic> json) => _$DeviceMessageHeaderFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceMessageHeaderToJson(this);
}

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/services/services_i.dart';
import '../../dao/user_dao.dart';
import 'root_page.dart';
import 'auth/auth_pages_i.dart';
import 'profile/profile_pages_i.dart';

class PageInterceptor {

  static Future<bool> isUserEmailSkipped() async {

    Storage storage = Storage.instance;

    // Load user address skipped status
    bool hasUserDataEmailSkipped = storage.hasData(StorageKeys.userDataEmailSkipped);
    bool isSkipped = false;

    // Load user account from secure storage if exists
    if (hasUserDataEmailSkipped) {
      isSkipped = await storage.read(StorageKeys.userDataEmailSkipped);
    }

    return isSkipped;
  }

  static Future<bool> isUserNameSkipped() async {

    Storage storage = Storage.instance;

    // Load user address skipped status
    bool hasUserDataNameSkipped = storage.hasData(StorageKeys.userDataNameSkipped);
    bool isSkipped = false;

    // Load user account from secure storage if exists
    if (hasUserDataNameSkipped) {
      isSkipped = await storage.read(StorageKeys.userDataNameSkipped);
    }

    return isSkipped;
  }

  static Future<bool> isUserAddressSkipped() async {

    Storage storage = Storage.instance;

    // Load user address skipped status
    bool hasUserDataAddressSkipped = storage.hasData(StorageKeys.userDataAddressSkipped);
    bool isSkipped = false;

    // Load user account from secure storage if exists
    if (hasUserDataAddressSkipped) {
      isSkipped = await storage.read(StorageKeys.userDataAddressSkipped);
    }

    return isSkipped;
  }

  static Future<bool> isUserPetListSkipped() async {

    Storage storage = Storage.instance;

    // Load user address skipped status
    bool hasUserDataPetListSkipped = storage.hasData(StorageKeys.userDataPetListSkipped);
    bool isSkipped = false;

    // Load user account from secure storage if exists
    if (hasUserDataPetListSkipped) {
      isSkipped = await storage.read(StorageKeys.userDataPetListSkipped);
    }

    return isSkipped;
  }

  // Check authentication state for function pages
  static Future<void> pageAuthCheck() async {

    // Validate auth state
    AuthService authService = AuthService.instance;

    if (authService.currentUser.value == null) {

      if (authService.userAccount.value != null) {
        if (authService.userAuthRecord.value != null && authService.userAuthRecord.value!.authChannel == AuthChannel.emailPassword) {
          // Go to login via email page, then verify
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.offAll(LogInEmailPage());
          });
          return;
        }
        else {
          // Go to login via email page, then verify
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.offAll(LogInPage());
          });
          return;
        }
      }
      else {
        // Go to login via email page, then verify
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(WelcomePage());
        });
        return;
      }
    }

    // If no user email set
    if ((userDataEmailPopUpEveryTime && (authService.currentUser.value == null || authService.currentUser.value?.email == null)) ||
        (!userDataEmailPopUpEveryTime && (!userDataEmailSkippedThisTime && (authService.currentUser.value == null || authService.currentUser.value?.email == null)))
    ) {
      // User name setting
      if (Get.currentRoute != '/LinkEmailPage') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(()=> LinkEmailPage());
        });
        return;
      }
    }

    // If no user name set
    else if ((userDataNamePopUpEveryTime && (authService.userData.value == null || (
        (authService.userData.value!.firstName == null || authService.userData.value!.firstName == '')  &&
            (authService.userData.value!.lastName == null || authService.userData.value!.lastName == ''))))  ||
        (!userDataNamePopUpEveryTime && (!userDataNameSkippedThisTime && (authService.userData.value == null || (
            (authService.userData.value!.firstName == null || authService.userData.value!.firstName == '')  &&
                (authService.userData.value!.lastName == null || authService.userData.value!.lastName == '')))))
    ) {
      // User name setting
      if (Get.currentRoute != '/UserSettingNamePage') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(()=> UserSettingNamePage());
        });
        return;
      }
    }

    // If no address
    else if ((userDataAddressPopUpEveryTime && (authService.userData.value!.location == null ||
        ((authService.userData.value!.location!.addressLines == null || authService.userData.value!.location!.addressLines!.isEmpty) &&
            (authService.userData.value!.location!.administrativeArea == null || authService.userData.value!.location!.administrativeArea == '') &&
            (authService.userData.value!.location!.locality == null || authService.userData.value!.location!.locality == '') &&
            (authService.userData.value!.location!.subLocality == null || authService.userData.value!.location!.subLocality == '') &&
            (authService.userData.value!.location!.postalCode == null || authService.userData.value!.location!.postalCode == '')))) ||
        (!userDataAddressPopUpEveryTime && !await isUserAddressSkipped() && (!userDataAddressSkippedThisTime && (authService.userData.value!.location == null ||
            ((authService.userData.value!.location!.addressLines == null || authService.userData.value!.location!.addressLines!.isEmpty) &&
                (authService.userData.value!.location!.administrativeArea == null || authService.userData.value!.location!.administrativeArea == '') &&
                (authService.userData.value!.location!.locality == null || authService.userData.value!.location!.locality == '') &&
                (authService.userData.value!.location!.subLocality == null || authService.userData.value!.location!.subLocality == '') &&
                (authService.userData.value!.location!.postalCode == null || authService.userData.value!.location!.postalCode == '')))))
    ) {
      // User name setting
      if (Get.currentRoute != '/UserSettingAddressPage') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(()=> UserSettingAddressPage());
        });
        return;
      }
    }

    // If no pet data
    else if ((userDataPetListPopUpEveryTime && (authService.ownedPets.value == null || authService.ownedPets.value!.isEmpty)) ||
        (!userDataPetListPopUpEveryTime && (!userDataPetListSkippedThisTime && (authService.ownedPets.value == null || authService.ownedPets.value!.isEmpty)))
    ) {
      // User name setting
      if (Get.currentRoute != '/SelectPetPage') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(()=> const SelectPetPage());
        });
        return;
      }
    }

  }

  // Check authentication state for entry point
  static Future<void> rootAuthCheck({Widget? page}) async {

    // Validate auth state
    AuthService authService = AuthService.instance;

    // There's logged in user
    if (authService.currentUser.value != null) {

      // UserAccount? latestAccount = authService.userAccount.value;
      // if (latestAccount != null) {
      //   authService.userAccount.value = latestAccount;
      //   print("✅ UserAccount refreshed from Firestore: ${latestAccount.toJson()}");
      // }

      User? user = authService.currentUser.value;

      if (user!.providerData.length == 1) {

        if (user.providerData.first.providerId == 'password') {

          // Register via email password but not verified OR no user auth record
          if (user.emailVerified == false || authService.userAuthRecord.value == null) {
            // Go to login via email page, then verify
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Get.offAll(LogInEmailPage());
            });
            return;
          }
          else {
            // Direct to function pages
            await direct(page: page);
          }
        }
        else if (user.providerData.first.providerId == 'phone') {

          // Register via phone number but no user auth record
          if (authService.userAuthRecord.value == null) {
            // Go to login via email page, then verify
            WidgetsBinding.instance.addPostFrameCallback((_) {
              Get.offAll(LogInPage());
            });
            return;
          }
          else {
            // Direct to function pages
            await direct(page: page);
          }
        }
      }
      // Register via both phone number and email password
      else if (user.providerData.length == 2) {

        // User auth record not exists
        if (authService.userAuthRecord.value == null) {
          // Go to login via email page, then verify
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.offAll(LogInEmailPage());
          });
          return;
        }
        // There's user auth record
        else {

          // if last login is email password and email not verified
          if (authService.userAuthRecord.value!.authChannel == AuthChannel.emailPassword) {

            if (user.emailVerified == false) {
              // Direct to function pages
              WidgetsBinding.instance.addPostFrameCallback((_) {
                Get.offAll(LogInEmailPage());
              });
              return;
            }
            // Direct to function pages
            await direct(page: page);
          }
          // if last login is phone number
          else {
            // Direct to function pages
            await direct(page: page);
          }
        }
      }
    }
    // There's previous authentication info
    else if (authService.userAccount.value != null) {

      if (authService.userAuthRecord.value != null && authService.userAuthRecord.value!.authChannel == AuthChannel.emailPassword) {
        // Go to login via email page, then verify
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(LogInEmailPage());
        });
        return;
      }
      else {
        // Go to login via email page, then verify
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(LogInPage());
        });
        return;
      }
    }
    // No authentication info
    else {

      // Go to welcome page
      WidgetsBinding.instance.addPostFrameCallback((_) {
        //Get.offAll(WelcomePage());
        Get.offAll(WelcomePage());
      });
      return;
    }
  }

  /// Direct to function pages
  static direct({Widget? page}) async {

    // Validate auth state
    AuthService authService = AuthService.instance;
    Storage storage = Storage.instance;

    // If phone number not set
    if (authService.currentUser.value?.phoneNumber == null || authService.currentUser.value!.phoneNumber == '') {
      // User name setting
      if (Get.currentRoute != '/LinkPhoneNumberPage') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(()=> LinkPhoneNumberPage());
        });
        return;
      }
    }

    // Direct to function pages
    if (page != null) {

      // Go to target page
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAll(page);
      });
      return;
    }
    else {
      if (Get.currentRoute != '/root') {
        // Go to root page if no target page
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.offAll(RootPage());
        });
      }
      return;
    }
  }
}

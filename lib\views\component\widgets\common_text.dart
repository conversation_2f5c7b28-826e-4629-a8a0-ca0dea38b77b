import 'package:flutter/cupertino.dart';

class CommonText extends StatelessWidget {
  final String text;
  final double? width;
  final double? height;
  final TextStyle style;
  final bool softWrap; // 是否自动换行
  final int? maxLines;
  final TextAlign? textAlign;
  final bool? isEllipsis;

  const CommonText(
    this.text,
    this.style, {
    super.key,
    this.width,
    this.height,
    this.softWrap = true,
    this.maxLines = 1,
    this.textAlign,
    this.isEllipsis = true,
  });

  @override
  Widget build(BuildContext context) {
    return Baseline(
      baseline: style.fontSize!, // 调整此值以垂直定位文本
      baselineType: TextBaseline.alphabetic,
      child: SizedBox(
        width: width,
        height: height,
        child: Text(
        text,
        maxLines: maxLines,
        overflow: isEllipsis! ? TextOverflow.ellipsis : TextOverflow.visible, // 使用省略号表示溢出的文本
        softWrap: softWrap,
        style: style,
        textAlign: textAlign ?? TextAlign.start,
        textHeightBehavior: const TextHeightBehavior(
          applyHeightToFirstAscent: false,
          applyHeightToLastDescent: false,
        ),
      )),
    );
  }
}

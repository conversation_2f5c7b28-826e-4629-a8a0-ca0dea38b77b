import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class LoginController extends GetxController {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController codeController = TextEditingController();

  final RxBool isPhoneEntered = false.obs;
  final RxBool isPhoneValid = false.obs; // ✅ 仅当输入 10 位数字时才为 true
  final RxBool isCodeEntered = false.obs;
  final RxBool isCodeSent = false.obs;
  final RxBool isCodeValid = true.obs;
  final RxBool canResend = false.obs;
  final RxBool enableButton = false.obs;
  final RxString buttonText = 'Send verification code'.obs;
  final RxBool codeFieldEnabled = false.obs;
  final RxInt countdown = 30.obs;
  Timer? timer;

  final String validCode = '123';

  @override
  void onInit() {
    super.onInit();
    resetState();
  }

  void resetState() {
    phoneController.clear();
    codeController.clear();
    isPhoneEntered.value = false;
    isPhoneValid.value = false; // ✅ 重置手机号校验状态
    isCodeEntered.value = false;
    isCodeSent.value = false;
    isCodeValid.value = true;
    canResend.value = false;
    enableButton.value = false;
    buttonText.value = 'Send verification code';
    codeFieldEnabled.value = false;
    countdown.value = 30;
    timer?.cancel();
  }

  @override
  void onClose() {
    phoneController.dispose();
    codeController.dispose();
    timer?.cancel();
    super.onClose();
  }

  void startCountdown() {
    countdown.value = 30;
    canResend.value = false;
    isCodeValid.value = true;
    enableButton.value = false;
    codeFieldEnabled.value = true;
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value == 0) {
        canResend.value = true;
        timer.cancel();
      } else {
        countdown.value--;
      }
    });
  }

  void updateButtonState() {
    isPhoneValid.value = phoneController.text.length == 10; // ✅ 仅当输入 10 位时，认为有效
    if (!isCodeSent.value) {
      enableButton.value = isPhoneValid.value;
      buttonText.value = 'Send verification code';
    } else {
      enableButton.value = isPhoneValid.value && isCodeEntered.value && isCodeValid.value;
      buttonText.value = 'Login';
    }
  }

  void onSubmit() {
    if (!isCodeSent.value) {
      isCodeSent.value = true;
      startCountdown();
      updateButtonState();
    } else {
      if (codeController.text == validCode) {
        resetState();
        Get.toNamed('/selectPet');
      } else {
        isCodeValid.value = false;
        enableButton.value = false;
        updateButtonState();
      }
    }
  }
}

import 'package:flutter/material.dart';

class TextStyleExtension {

  // Common widgets
  late TextStyle buttonLargeFilled;
  late TextStyle buttonLargeOutlined;
  late TextStyle buttonMedium;
  late TextStyle buttonMedium2;
  late TextStyle inputLarge;
  late TextStyle inputLargeHint;
  late TextStyle inputLargeError;
  late TextStyle mapSearchHeader;
  late TextStyle mapSearchHeaderHint;
  late TextStyle dropdownItem;
  late TextStyle dropdownItemSelected;
  late TextStyle radioItem;
  late TextStyle radioItemSelected;
  late TextStyle bottomItem;
  late TextStyle bottomItemSelected;

  // Auth page
  late TextStyle appAvatarLarge;
  late TextStyle appAvatarLarge2;
  late TextStyle appAvatarMedium;
  late TextStyle appAvatarSelect;
  late TextStyle appAvatarSelect2;
  late TextStyle authPageTitle;
  late TextStyle authPageDesc;
  late TextStyle authPageNotification;
  late TextStyle authPageWarning;
  late TextStyle authPageButton;
  late TextStyle authPageDisabledButton;
  late TextStyle authPageAlterButton;
  late TextStyle authPagePassTipTitle;
  late TextStyle authPagePassTipBody;
  late TextStyle authPageResend;
  late TextStyle authPageResendDisabled;
  late TextStyle authPageInvalid;
  late TextStyle authPageChangeLogin;
  late TextStyle authPageForgotPassword;
  late TextStyle authPageCelebration;
  late TextStyle authPageHintText;
  late TextStyle authPageInputText;
  late TextStyle authPageInvalidText;
  late TextStyle userProfileSettingBody;
  late TextStyle userProfileSettingTitle;
  late TextStyle userProfileSettingNotification;

  // Function page
  late TextStyle userAvatarLarge;
  late TextStyle userAvatarLargeTip;
  late TextStyle userAvatar;
  late TextStyle postStatisticTitle;
  late TextStyle postStatisticBody;
  late TextStyle contactTitle;
  late TextStyle contactInfo;
  late TextStyle petAvatar;
  late TextStyle petAvatarMore;
  late TextStyle petAvatarLargeName;
  late TextStyle petAvatarLargeIntro;
  late TextStyle petInfoTitle;
  late TextStyle petInfoBody;
  late TextStyle petInfoListTitle;
  late TextStyle petInfoListBody;
  late TextStyle petInfoListRemark;
  late TextStyle postGridItemTitle;
  late TextStyle postGridItemBody;
  late TextStyle eventListItemDateTime;
  late TextStyle eventListItemBody;
  late TextStyle placeListItemTitle;
  late TextStyle placeListItemMileage;
  late TextStyle placeListItemDateTime;
  late TextStyle userProfileBody1;

  // Geo page
  late TextStyle geoFilterButtonText;
  late TextStyle geoPlaceInfoCardTitle;
  late TextStyle geoPlaceInfoCardMileage;
  late TextStyle geoPlaceInfoCardRating;
  late TextStyle geoPlaceInfoCardBusinessStatus;
  late TextStyle geoPlaceInfoCardBusinessTime;
  late TextStyle geoPlaceInfoCardTag;
  late TextStyle geoPlaceInfoCardCheckInButtonText;
  late TextStyle geoSearchHintText;
  late TextStyle geoSearchInputText;
  late TextStyle geoSearchInvalidText;
  late TextStyle recordWeek;
  late TextStyle recordMonth;
  late TextStyle recordNextMonth;
  late TextStyle recordMonthSelect;
  late TextStyle recordMonthUnselect;
  late TextStyle recordDayDisplay;
  late TextStyle recordAllTime;
  late TextStyle recordAllType;
  late TextStyle recordAllTimes;
  late TextStyle recordDelete;
  late TextStyle recordDeleteCancel;
  late TextStyle recordListTime;
  late TextStyle recordListDescription;
  late TextStyle recordTimePicker;
}

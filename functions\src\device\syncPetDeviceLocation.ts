import {onDocumentCreated} from "firebase-functions/v2/firestore";
import {logger} from "firebase-functions/v2";
import {getFirestore} from "firebase-admin/firestore";

// Initialize Firebase
// initializeApp();

export const syncPetDeviceLocation = onDocumentCreated(
  "device/{deviceId}/device_geo_location_message/{messageId}",
  async (event) => {
    try {
      // Get the document data
      const snapshot = event.data;
      if (!snapshot) {
        logger.error("No data associated with the event");
        return;
      }

      const data = snapshot.data();
      const deviceId = event.params.deviceId;
      const messageId = event.params.messageId;

      // Validate required fields
      if (!data.pid) {
        logger.log("No pet ID found in the message", {deviceId, messageId});
        return;
      }

      // Get Firestore instance
      const firestore = getFirestore();

      // logger.log("Updating pet with data:", {
      //   pid: data.pid,
      //   data: data,
      // });

      // Update pet document
      await firestore.collection("pet").doc(data.pid).update({
        latest_location: data.location ?? null,
        latest_location_at: data.generate_date ?? null,
        //         lastUpdated: new Date(),  // Add timestamp of update
      });

      logger.log("Successfully synced device location", {
        petId: data.pid,
        deviceId,
        messageId,
      });
    } catch (error) {
      logger.error("Failed to sync device location", {
        error: error instanceof Error ? error.message : String(error),
        params: event.params,
      });
      //       throw error; // Re-throw to mark function as failed
    }
  }
);

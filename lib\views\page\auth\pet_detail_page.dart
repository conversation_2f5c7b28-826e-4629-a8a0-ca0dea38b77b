import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

class PetDetailPage2 extends StatefulWidget {
  const PetDetailPage2({super.key});

  @override
  PetDetailPage2State createState() => PetDetailPage2State();
}

class PetDetailPage2State extends State<PetDetailPage2> {
  late Future<PetDetailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => PetDetailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PetDetailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  _buildWhiteSection1(),
                  _buildBackButton(controller),
                  _buildTopSection(controller),
                  _buildStepIndicator(controller),
                  _buildProgressIndicator(controller),
                  _buildContent(controller),
                  _buildWhiteSection2(),
                  _buildSubmitButton(controller),
                  _buildSkipText(controller),
                  //_buildSearchButton(widthRatio, heightRatio),
                ],
              ),
            );
          }
        });
  }

  Widget _buildWhiteSection1() {
    return const CommonWhiteSection(
      width: 403,
      height: 149,
      top: 0,
      borderRadius: 0,
    );
  }

  Widget _buildBackButton(PetDetailPageController controller) {
    return CommonBackButton(
      onPressed: () {
        controller.clearStepInput(controller.currentStep.value);
        if (controller.currentStep.value > 1) {
          controller.currentStep.value -= 1;
        } else {
          Get.back();
        }
      },
    );
  }

  Widget _buildTopSection(PetDetailPageController controller) {
    return Positioned(
      width: 403.w,
      height: 80.w,
      top: 60.w,
      child: Column(
        children: [
          Text('add.pet'.t18,
              style: controller.theme.textStyleExtension.appAvatarSelect2),
          SizedBox(height: 8.w),
          Obx(() {
            String stepText = '';
            switch (controller.currentStep.value) {
              case 1:
                stepText = 'breed'.t18;
                break;
              case 2:
                stepText = 'name'.t18;
                break;
              case 3:
                stepText = 'weight'.t18;
                break;
              case 4:
                stepText = 'birthday'.t18;
                break;
              case 5:
                stepText = 'gender'.t18;
                break;
              case 6:
                stepText = 'country'.t18;
                break;
              default:
                stepText = ''; // 防止意外值
            }
            return Text(stepText,
                style: controller.theme.textStyleExtension.userAvatarLarge);
          }),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(PetDetailPageController controller) {
    return Positioned(
      right: 38.w,
      top: 65.w,
      child: Obx(() {
        String stepText = 'step'.t18;
        String currentStepText = '${controller.currentStep.value}';
        String totalStepText = '/6';

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              stepText,
              style: controller.theme.textStyleExtension.authPagePassTipBody,
            ),
            SizedBox(width: 5.w),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: currentStepText,
                    style: controller.theme.textStyleExtension.buttonMedium2,
                  ),
                  TextSpan(
                    text: totalStepText,
                    style:
                    controller.theme.textStyleExtension.authPagePassTipBody,
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildProgressIndicator(PetDetailPageController controller) {
    return Positioned(
      width: 327.w,
      height: 4.w,
      top: 120.w,
      left: 38.w,
      child: Obx(() => Stack(
            children: [
              Container(
                width: 327.w,
                height: 4.w,
                color: controller.theme.themeData.colorScheme.tertiary,
              ),
              Container(
                width:
                    (327 * (controller.currentStep.value / 6)).w,
                height: 4.w,
                color: controller.theme.themeData.colorScheme.secondary,
              ),
            ],
          )),
    );
  }

  Widget _buildContent(PetDetailPageController controller) {
    return Obx(() {
      if (controller.currentStep.value == 1) {
        return _buildPetBreedInput(controller);
      } else if (controller.currentStep.value == 2) {
        return _buildPetNameInput(controller);
      } else if (controller.currentStep.value == 3) {
        return _buildPetWeightInput(controller);
      } else if (controller.currentStep.value == 4) {
        return _buildPetBirthdayInput(controller);
      } else if (controller.currentStep.value == 5) {
        return _buildGenderSelection(controller);
      } else if (controller.currentStep.value == 6) {
        return _buildPetCountryInput(controller);
      } else {
        return Container();
      }
    });
  }

  Widget _buildPetBreedInput(PetDetailPageController controller) {
    return Positioned(
      width: 375.w,
      top: 260.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 186.w,
                height: 186.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: controller.theme.themeData.colorScheme.tertiary,
                ),
                child: Icon(
                  Icons.image,
                  size: 50,
                  color: controller.theme.themeData.colorScheme.secondary,
                ),
              ),
            ],
          ),
          // **宠物品种提示**
          SizedBox(height: 36.w),
          Text(
            "breed.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          SizedBox(height: 24.w),
          CommonTextField(
            controller: controller.petBreedController,
            hintText: "breed.hind".t18,
            keyboardType: TextInputType.text,
            isEnabled: true,
            textColor: controller.theme.themeData.colorScheme.tertiary, // 设置文本颜色
            borderColor: controller.theme.themeData.colorScheme.tertiary, // 设置边框颜色
            iconColor: controller.theme.themeData.colorScheme.tertiary, // 设置图标颜色
            onChanged: (value) {
              controller.petBreed.value = value;
              controller.validateInput();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPetNameInput(PetDetailPageController controller) {
    return Positioned(
      width: 375.w,
      top: 260.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 186.w,
                height: 186.w,
                child: ClipOval(
                  child: Image.asset(
                    "assets/images/pet_placeholder.png", // 默认图片
                    width: 180.w,
                    height: 180.w,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Positioned(
                top: 150.w,
                child: GestureDetector(
                  onTap: () {},
                  child: Container(
                    width: 36.w,
                    height: 36.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                    child: Icon(Icons.camera_alt,
                        color: controller.theme.themeData.colorScheme.secondary, size: 24.sp),
                  ),
                ),
              ),
            ],
          ),

          // **宠物名称提示**
          SizedBox(height: 36.w),
          Text(
            "name.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          // **宠物名称输入框**
          SizedBox(height: 24.w),
          CommonTextField(
            controller: controller.petNameController,
            hintText: "name.hind".t18,
            keyboardType: TextInputType.text,
            isEnabled: true,
            textColor: controller.theme.themeData.colorScheme.tertiary, // 设置文本颜色
            borderColor: controller.theme.themeData.colorScheme.tertiary, // 设置边框颜色
            iconColor: controller.theme.themeData.colorScheme.tertiary, // 设置图标颜色
            onChanged: (value) {
              controller.petName.value = value;
              controller.validateInput();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPetWeightInput(PetDetailPageController controller) {
    return Positioned(
      width: 375.w,
      top: 240.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 125.w,
                height: 125.w,
                child: ClipOval(
                  child: Image.asset(
                    "assets/images/pet_placeholder.png", // 默认图片
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
          // **宠物名称提示**
          SizedBox(height: 36.w),
          Text(
            "weight.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          // **宠物名称输入框**
          SizedBox(height: 24.w),
          CommonTextField(
            controller: controller.petWeightController,
            hintText: "weight.hind".t18,
            keyboardType: TextInputType.text,
            isEnabled: true,
            textColor: controller.theme.themeData.colorScheme.tertiary, // 设置文本颜色
            borderColor: controller.theme.themeData.colorScheme.tertiary, // 设置边框颜色
            iconColor: controller.theme.themeData.colorScheme.tertiary, // 设置图标颜色
            onChanged: (value) {
              controller.petWeight.value = value;
              controller.validateInput();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPetBirthdayInput(PetDetailPageController controller) {
    return Positioned(
      width: 375.w,
      top: 240.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 125.w,
                height: 125.w,
                child: ClipOval(
                  child: Image.asset(
                    "assets/images/pet_placeholder.png", // 默认图片
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
          // **宠物名称提示**
          SizedBox(height: 36.w),
          Text(
            "birthday.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          // **宠物名称输入框**
          SizedBox(height: 24.w),
          CommonTextField(
            controller: controller.petBirthdayController,
            hintText: "birthday.hind".t18,
            keyboardType: TextInputType.text,
            isEnabled: true,
            textColor: controller.theme.themeData.colorScheme.tertiary, // 设置文本颜色
            borderColor: controller.theme.themeData.colorScheme.tertiary, // 设置边框颜色
            iconColor: controller.theme.themeData.colorScheme.tertiary, // 设置图标颜色
            onChanged: (value) {
              controller.petBirthday.value = value;
              controller.validateInput();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGenderSelection(PetDetailPageController controller) {
    return Positioned(
      width: 375.w,
      top: 240.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 125.w,
                height: 125.w,
                child: ClipOval(
                  child: Image.asset(
                    "assets/images/pet_placeholder.png", // 默认图片
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 36.w),
          Text(
            "gender.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          SizedBox(height: 24.w),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildGenderButton("male".t18, Icons.male,controller),
              SizedBox(width: 16.w),
              _buildGenderButton("Female".t18, Icons.female,controller),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGenderButton(String gender, IconData icon, PetDetailPageController controller) {
    return Obx(() {
      bool isSelected = controller.petGender.value == gender;
      return GestureDetector(
        onTap: () {
          controller.petGender.value = gender;
        },
        child: Container(
          width: 126.5.w,
          height: 50.w,
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: isSelected
                  ? controller.theme.themeData.colorScheme.secondary
                  : controller.theme.themeData.colorScheme.tertiary,
              width: 1,
            ),
            color: controller.theme.themeData.colorScheme.onPrimary,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 16.w,
                height: 16.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? controller.theme.themeData.colorScheme.secondary
                        : controller.theme.themeData.colorScheme.tertiary,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 8.w,
                          height: 8.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: controller.theme.themeData.colorScheme.secondary,
                          ),
                        ),
                      )
                    : null,
              ),
              SizedBox(width: 8.w),
              Text(
                gender,
                style: isSelected
                    ? controller.theme.textStyleExtension.dropdownItemSelected
                    : controller.theme.textStyleExtension.dropdownItem,
              ),
              SizedBox(width: 8.w),
              Icon(
                icon,
                color: gender == "male".t18 ? Colors.blue : Colors.pink,
                size: 20.sp,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildPetCountryInput(PetDetailPageController controller) {
    final List<Map<String, String>> countries = [
      {"name": "Canada", "flag": "🇨🇦"},
      {"name": "U.S.", "flag": "🇺🇸"},
      {"name": "China", "flag": "🇨🇳"},
    ];

    return Positioned(
      width: 375.w,
      top: 240.w,
      left: 14.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 125.w,
                height: 125.w,
                child: ClipOval(
                  child: Image.asset(
                    "assets/images/pet_placeholder.png", // 默认图片
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 36.w),
          Text(
            "country.text".t18,
            style: controller.theme.textStyleExtension.appAvatarSelect2,
          ),
          SizedBox(height: 24.w),
          // **Dropdown Button for Country Selection**
          Obx(() => Container(
                width: 327.w,
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                      color: controller.theme.themeData.colorScheme.secondary, width: 1.w),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: controller.selectedCountry.value.isNotEmpty
                        ? controller.selectedCountry.value
                        : null, // **默认值**
                    hint: Row(
                      children: [
                        SizedBox(width: 10.w),
                        Text(
                          "country.hind".t18,
                          style:
                          controller.theme.textStyleExtension.dropdownItem,
                        ),
                      ],
                    ),
                    isExpanded: true,
                    icon: Icon(Icons.keyboard_arrow_down,
                        color: controller.theme.themeData.colorScheme.secondary),
                    items: countries.map((country) {
                      return DropdownMenuItem<String>(
                        value: country["name"],
                        child: Row(
                          children: [
                            Text(
                              country["flag"]!,
                              style: TextStyle(fontSize: 20.sp),
                            ),
                            SizedBox(width: 10.w),
                            Text(
                              country["name"]!,
                              style: controller.selectedCountry.value ==
                                      country["name"]
                                  ? controller.theme.textStyleExtension
                                      .dropdownItemSelected
                                  : controller.theme.textStyleExtension
                                      .dropdownItem,
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        controller.selectedCountry.value = newValue;
                      }
                    },
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildWhiteSection2() {
    return const CommonWhiteSection(
      width: 403,
      height: 211,
      top: 697,
      borderRadius: 26,
    );
  }

  Widget _buildSubmitButton(PetDetailPageController controller) {
    return Positioned(
      top: 721.w,
      left: 38.w,
      child: Obx(
        () => CommonButton(
          text: 'continue'.t18,
          onPressed: controller.isContinueEnabled.value
              ? () {
                  controller.clearStepInput(controller.currentStep.value);
                  if (controller.currentStep.value < 6) {
                    //_clearStepInput(currentStep.value);
                    controller.currentStep.value++;
                  } else {
                    Get.toNamed('/celebration');
                  }
                }
              : null,
          backgroundColor: controller.isContinueEnabled.value
              ? controller.theme.themeData.colorScheme.secondary
              : controller.theme.themeData.colorScheme.tertiary,
          textColor: controller.theme.themeData.colorScheme.onPrimary,
          borderColor: Colors.transparent,
          width: 327.w,
          height: 60.w,
        ),
      ),
    );
  }

  Widget _buildSkipText(PetDetailPageController controller) {
    return Positioned(
      top: 800.w,
      left: 154.w,
      child: GestureDetector(
        onTap: () {},
        child: Text(
          'skip'.t18,
          textAlign: TextAlign.center,
          style: controller.theme.textStyleExtension.authPageChangeLogin,
        ),
      ),
    );
  }
}

class PetDetailPageController extends GetxController {
  final RxInt currentStep = 1.obs; // 当前步骤 (1-6)
  final RxString petBreed = ''.obs;
  final RxString petName = ''.obs;
  final RxString petWeight = ''.obs;
  final RxString petBirthday = ''.obs;
  final RxString petGender = "".obs;
  final RxString selectedCountry = ''.obs;
  final RxBool isContinueEnabled = false.obs;

  final List<Map<String, String>> dogBreeds = [
    {"name": "Mixed Breed", "image": "assets/images/breed.png"},
    {"name": "Afghan Hound", "image": "assets/images/breed.png"},
    {"name": "Akita", "image": "assets/images/breed.png"},
    {"name": "Beagle", "image": "assets/images/breed.png"},
    {"name": "Bichon Frise", "image": "assets/images/breed.png"},
    {"name": "Not sure", "image": "assets/images/breed.png"},
    {"name": "Not sure", "image": "assets/images/breed.png"},
    {"name": "Not sure", "image": "assets/images/breed.png"},
  ];

  final RegExp nameRegex = RegExp(r'^[a-zA-Z0-9]+$');
  final RegExp weightRegex = RegExp(r'^\d+(\.\d{1,2})?$');

  final TextEditingController petBreedController = TextEditingController();
  final TextEditingController petNameController = TextEditingController();
  final TextEditingController petWeightController = TextEditingController();
  final TextEditingController petBirthdayController = TextEditingController();

  // Theme plugin
  late ThemePlugin theme;

  Future<PetDetailPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    return this;
  }

  void validateInput() {
    switch (currentStep.value) {
      case 1:
        isContinueEnabled.value = petBreed.value.isNotEmpty;
        break;
      case 2:
        isContinueEnabled.value = nameRegex.hasMatch(petName.value);
        break;
      case 3:
        isContinueEnabled.value = weightRegex.hasMatch(petWeight.value);
        break;
      case 4:
        try {
          DateFormat format = DateFormat('dd/MM/yy');
          DateTime enteredDate = format.parseStrict(petBirthday.value);
          DateTime today = DateTime.now();
          isContinueEnabled.value = enteredDate.isBefore(today);
        } catch (e) {
          isContinueEnabled.value = false;
        }
        break;
      case 5:
        isContinueEnabled.value = petGender.value.isNotEmpty;
        break;
      case 6:
        isContinueEnabled.value = selectedCountry.value.isNotEmpty;
        break;
    }
  }

  void clearStepInput(int step) {
    switch (step) {
      case 1:
        petBreedController.text = "";
        petBreed.value = "";
        break;
      case 2:
        petNameController.text = "";
        petName.value = "";
        break;
      case 3:
        petWeightController.text = "";
        petWeight.value = "";
        break;
      case 4:
        petBirthdayController.text = "";
        petBirthday.value = "";
        break;
      case 5:
        petGender.value = "";
        break;
      case 6:
        selectedCountry.value = "";
        break;
    }
  }
}

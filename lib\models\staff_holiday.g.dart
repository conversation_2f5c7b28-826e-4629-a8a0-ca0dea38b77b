// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'staff_holiday.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StaffHoliday _$StaffHolidayFromJson(Map<String, dynamic> json) => StaffHoliday(
      storeId: json['storeId'] as String,
      staffId: json['staffId'] as String,
      holidayId: json['holidayId'] as String,
      title: json['title'] as String,
      startDate: json['startDate'] as String,
      endDate: json['endDate'] as String,
      isFullDay: JsonUtil.boolFromJson(json['isFullDay']),
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      reason: json['reason'] as String?,
      isRecurring: JsonUtil.boolFromJson(json['isRecurring']),
      recurringPattern: json['recurringPattern'] as String?,
      isApproved: JsonUtil.boolFromJson(json['isApproved']),
      approvedBy: json['approvedBy'] as String?,
      approvedAt: (json['approvedAt'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$StaffHolidayToJson(StaffHoliday instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'storeId': instance.storeId,
      'staffId': instance.staffId,
      'holidayId': instance.holidayId,
      'title': instance.title,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'isFullDay': JsonUtil.boolToJson(instance.isFullDay),
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'reason': instance.reason,
      'isRecurring': JsonUtil.boolToJson(instance.isRecurring),
      'recurringPattern': instance.recurringPattern,
      'isApproved': JsonUtil.boolToJson(instance.isApproved),
      'approvedBy': instance.approvedBy,
      'approvedAt': instance.approvedAt,
    };

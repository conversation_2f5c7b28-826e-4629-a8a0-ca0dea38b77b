import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';

class FilterButton extends StatelessWidget {

  final FilterButtonController controller;
  const FilterButton({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        controller.toggleSelection();
        if (controller.onPressed.value != null) {
          controller.onPressed.value;
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: controller.isSelected.isTrue
              ? controller.colorSelected.value!.withAlpha(0.1.colorAlpha)
              : Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(0.1.colorAlpha),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
          border: controller.isSelected.isTrue ? Border.all(color: controller.determineColor(), width: 1) : null,
        ),
        child: Row(
          children: [
            Icon(
              getIconData(),
              size: 18,
              color: controller.determineColor(),
            ),
            const SizedBox(width: 6),
            CommonText(
              controller.determineLabel(),
              controller.theme.value!.textStyleExtension.geoFilterButtonText
            ),
          ],
        ),
      ),
    );
  }

  IconData getIconData() {
    if (controller.filterType.value == GeoPlaceFilterType.park) {
      return Icons.pets;
    } else if (controller.filterType.value == GeoPlaceFilterType.trail) {
      return Icons.directions_walk;
    } else if (controller.filterType.value == GeoPlaceFilterType.favor) {
      return Icons.favorite;
    }
    return Icons.place;
  }
}

class FilterButtonController {
  var theme = Rx<ThemePlugin?>(null);
  RxBool isSelected = false.obs;
  var colorSelected = Rx<Color?>(null);
  var colorUnSelected = Rx<Color?>(null);
  var filterType = Rx<GeoPlaceFilterType?>(null);
  var onPressed = Rx<AsyncVoidCallback?>(null);

  FilterButtonController({
    required Rx<ThemePlugin?> theme,
    RxBool? isSelected,
    required Rx<Color?> colorSelected,
    Rx<Color?>? colorUnSelected,
    Rx<GeoPlaceFilterType?>? filterType,
    Rx<AsyncVoidCallback?>? onPressed,
  }): isSelected = isSelected?? RxBool(false),
      colorUnSelected = colorUnSelected?? Colors.white.obs,
      filterType = filterType?? GeoPlaceFilterType.park.obs,
      onPressed = onPressed?? Rx<AsyncVoidCallback?>(null)
  ;

  void toggleSelection() {
    isSelected.value = !isSelected.value;
  }

  Color determineColor() {
    if (filterType.value == GeoPlaceFilterType.park) {
      return theme.value!.colorExtension.geoPark;
    } else if (filterType.value == GeoPlaceFilterType.trail) {
      return theme.value!.colorExtension.geoTrail;
    }
    return theme.value!.colorExtension.geoPark;
  }

  String determineLabel() {
    if (filterType.value == GeoPlaceFilterType.park) {
      return GeoPlaceFilterType.park.t18key.t18;
    } else if (filterType.value == GeoPlaceFilterType.trail) {
      return GeoPlaceFilterType.trail.t18key.t18;
    } else if (filterType.value == GeoPlaceFilterType.favor) {
      return GeoPlaceFilterType.favor.t18key.t18;
    }
    return GeoPlaceFilterType.park.t18key.t18;
  }
}
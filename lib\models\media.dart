import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'media.g.dart';

@JsonSerializable()
class Media extends BaseFullModel {

  String uid;
  MediaType type;
  String? pid;
  String? postId;
  String? previousId;
  String? nextId;

  Media({
    required this.uid,
    required this.type,
    this.pid,
    this.postId,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$MediaToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Media.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Media.fromJson(jsonData);
  }

  factory Media.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Media.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'media';

  static create ({
    required String uid,
    required MediaType type,
    required String pid,
    String? postId,
  }) {

    return Media(
      sid: uuid.v4(),
      uid: uid,
      type: type,
      pid: pid,
      postId: postId,
      isValid: true,
    );
  }

  static copyFrom(Media other) {
    return Media(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      type: other.type,
      pid: other.pid,
      postId: other.postId,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

# OneNata 设备管理系统技术文档

## 目录
- [1. 系统概述](#1-系统概述)
- [2. 架构设计](#2-架构设计)
- [3. 核心组件详细设计](#3-核心组件详细设计)
- [4. 数据模型设计](#4-数据模型设计)
- [5. API设计规范](#5-api设计规范)
- [6. 数据流程](#6-数据流程)
- [7. 安全设计](#7-安全设计)
- [8. 性能优化](#8-性能优化)
- [9. 错误处理](#9-错误处理)
- [10. 测试策略](#10-测试策略)
- [11. 部署配置](#11-部署配置)
- [12. 监控告警](#12-监控告警)
- [13. 扩展性设计](#13-扩展性设计)
- [14. 版本管理](#14-版本管理)
- [15. 总结](#15-总结)

---

## 1. 系统概述

### 1.1 项目简介
OneNata 设备管理系统是一个集成式的宠物设备管理平台，支持多厂商设备的统一管理、实时监控和远程控制。系统采用 Flutter + Firebase + 多厂商API 的架构设计，提供完整的设备生命周期管理功能。

### 1.2 核心功能
- **设备管理**: 添加、删除、更新设备信息
- **实时监控**: 设备状态实时同步和展示
- **远程控制**: 通过厂商API发送控制指令
- **数据分析**: 设备使用历史和统计分析
- **多厂商支持**: 统一接口管理不同厂商设备
- **离线支持**: 本地缓存和离线操作

### 1.3 技术栈
- **前端**: Flutter 3.x + GetX 状态管理
- **后端**: Firebase Firestore + Cloud Functions
- **第三方集成**: 设备厂商REST API
- **实时通信**: Firebase实时数据库 + WebSocket
- **认证**: Firebase Authentication

---

## 2. 架构设计

### 2.1 整体架构图

```text
┌─────────────────────────────────────────────────────────────┐
│ Flutter App │
├─────────────────────────────────────────────────────────────┤
│ UI Layer (Pages/Widgets) │
│ ├── DeviceHomePage │
│ ├── DeviceAddPage │
│ └── DeviceDetailPage │
├─────────────────────────────────────────────────────────────┤
│ Controller Layer (GetX) │
│ └── DeviceController │
├─────────────────────────────────────────────────────────────┤
│ Service Layer │
│ ├── DeviceService (Firestore) │
│ ├── DeviceApiService (Third-party APIs) │
│ └── CacheService (Local Storage) │
├─────────────────────────────────────────────────────────────┤
│ Model Layer │
│ ├── Device Model │
│ ├── DeviceStatus Model │
│ └── ApiResponse Model │
└─────────────────────────────────────────────────────────────┘
│
├── Firebase Firestore ←→ Cloud Functions ←→ 厂商API
│
└── Real-time Sync & Webhooks
```

### 2.2 分层架构说明

#### 2.2.1 UI层 (Presentation Layer)
- **职责**: 用户界面展示和交互处理
- **组件**: Flutter Pages、Widgets、自定义组件
- **特点**: 响应式设计，支持多语言，Material Design风格

#### 2.2.2 控制器层 (Controller Layer)
- **职责**: 业务逻辑处理和状态管理
- **技术**: GetX状态管理框架
- **功能**: 数据流控制、UI状态管理、用户交互处理

#### 2.2.3 服务层 (Service Layer)
- **职责**: 数据访问和外部API集成
- **组件**: 
  - `DeviceService`: Firestore数据操作
  - `DeviceApiService`: 第三方API交互
  - `CacheService`: 本地缓存管理

#### 2.2.4 模型层 (Model Layer)
- **职责**: 数据结构定义和序列化
- **功能**: JSON序列化、数据验证、类型安全

---

## 3. 核心组件详细设计

### 3.1 DeviceController

```dart
class DeviceController extends GetxController {
  // 状态管理
  final RxList<Device> devices = <Device>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;
  
  // 核心功能
  Future<void> loadUserDevices();      // 加载设备
  Future<bool> addDevice(Device);      // 添加设备
  Future<bool> updateDevice(Device);   // 更新设备
  Future<bool> deleteDevice(String);   // 删除设备
  Future<void> refreshDevices();       // 刷新设备
  
  // 高级功能
  Future<bool> sendDeviceCommand();    // 发送指令
  Future<List> getDeviceHistory();     // 获取历史
  void startRealTimeSync();            // 实时同步
}
```

**设计原则**:
- **单一职责**: 只处理设备相关的业务逻辑
- **响应式**: 使用Observable对象自动更新UI
- **错误处理**: 统一的错误处理和用户提示
- **生命周期**: 自动资源管理和内存释放

### 3.2 DeviceService

```dart
class DeviceService {
  // Firestore操作
  Future<ServiceResponse<List<Device>>> getUserDevices();
  Future<ServiceResponse<Device>> addDevice(Device);
  Future<ServiceResponse<Device>> updateDevice(Device);
  Future<ServiceResponse<bool>> deleteDevice(String);
  
  // 实时监听
  void listenToDeviceChanges(Function callback);
  
  // 状态同步
  Future<ServiceResponse<bool>> updateDeviceStatus(String, Map);
}
```

**技术特点**:
- Firebase Firestore集成
- 实时数据监听
- 事务支持
- 离线缓存
- 自动重试机制

### 3.3 DeviceApiService

```dart
class DeviceApiService {
  // 设备验证和注册
  Future<ServiceResponse> validateDevice(Device);
  Future<ServiceResponse> registerDeviceMonitoring(Device);
  
  // 状态和控制
  Future<ServiceResponse> getDeviceStatus(Device);
  Future<ServiceResponse> sendCommand(Device, String, Map);
  
  // 数据获取
  Future<ServiceResponse> getDeviceHistory(Device, DateTime?, DateTime?);
  
  // 多厂商支持
  String _getApiUrl(String manufacturer);
  Map<String, String> _getHeaders(String manufacturer);
}
```

**设计特点**:
- 多厂商API统一接口
- 自动重试和超时处理
- 请求/响应日志记录
- API密钥管理
- 错误码标准化

---

## 4. 数据模型设计

### 4.1 Device模型

```dart
class Device {
  String? sid;                    // 系统ID
  String? name;                   // 设备名称
  DeviceType? deviceType;         // 设备类型
  DeviceModel? deviceModel;       // 设备型号
  String? manufacturer;           // 制造商
  String? manufacturerSerialNumber; // 序列号
  String? firmwareVersion;        // 固件版本
  String? hardwareVersion;        // 硬件版本
  String? uid;                    // 用户ID
  List<String>? pets;             // 关联宠物
  bool? isValid;                  // 设备有效性
  bool? isSynced;                 // 同步状态
  Map<String, dynamic>? status;   // 设备状态
  DateTime? lastSyncAt;           // 最后同步时间
  DateTime? createdAt;            // 创建时间
  DateTime? updatedAt;            // 更新时间
}
```

### 4.2 DeviceStatus模型

```dart
class DeviceStatus {
  bool isOnline;              // 在线状态
  int batteryLevel;           // 电池电量
  DateTime lastReportTime;    // 最后上报时间
  Map<String, dynamic> data;  // 设备特定数据
  List<String> alerts;        // 告警信息
}
```

### 4.3 ApiResponse模型

```dart
class ServiceResponse<T> {
  int code;         // 响应码
  String msg;       // 响应消息
  T? data;          // 响应数据
  bool get isSuccess => code >= 200 && code < 300;
}
```

---

## 5. API设计规范

### 5.1 厂商API集成标准

#### 5.1.1 OneNata API
```http
POST /v1/devices/validate
Authorization: Bearer {api_key}
Content-Type: application/json

{
  "serialNumber": "ON-SF-001",
  "deviceType": "feedingMachine",
  "deviceModel": "piFI"
}
```

#### 5.1.2 PiFI API
```http
POST /v1/devices/register
Authorization: Bearer {api_key}
X-Client-ID: {client_id}

{
  "deviceId": "device_001",
  "serialNumber": "PF-001",
  "userId": "user_123",
  "webhookUrl": "https://app.onenata.com/webhook"
}
```

### 5.2 Webhook接口

```http
POST /webhook/device-status
Content-Type: application/json

{
  "deviceId": "device_001",
  "timestamp": 1640995200000,
  "status": {
    "isOnline": true,
    "batteryLevel": 75,
    "data": {
      "temperature": 25.5,
      "feedCount": 3
    }
  }
}
```

---

## 6. 数据流程

### 6.1 设备添加流程

```mermaid
sequenceDiagram
    participant U as User
    participant UI as DeviceHomePage
    participant C as DeviceController
    participant AS as DeviceApiService
    participant DS as DeviceService
    participant FB as Firestore
    
    U->>UI: 点击添加设备
    UI->>C: addDevice(device)
    C->>AS: validateDevice(device)
    AS-->>C: 验证结果
    alt 验证成功
        C->>DS: addDevice(device)
        DS->>FB: 保存设备信息
        FB-->>DS: 保存成功
        DS-->>C: 返回设备对象
        C->>AS: registerDeviceMonitoring(device)
        C->>UI: 更新UI状态
        UI->>U: 显示成功提示
    else 验证失败
        C->>UI: 显示错误信息
        UI->>U: 提示验证失败
    end
```

### 6.2 实时状态同步

```mermaid
sequenceDiagram
    participant D as Device
    participant API as Vendor API
    participant WH as Webhook
    participant CF as Cloud Function
    participant FB as Firestore
    participant APP as Flutter App
    
    D->>API: 上报状态数据
    API->>WH: 发送Webhook
    WH->>CF: 触发云函数
    CF->>FB: 更新设备状态
    FB->>APP: 实时数据同步
    APP->>APP: 更新UI显示
```

---

## 7. 安全设计

### 7.1 认证授权

```dart
// Firebase用户认证
class AuthService {
  Future<bool> isUserAuthenticated();
  Future<String> getCurrentUserId();
  Future<bool> hasDevicePermission(String deviceId);
}

// API密钥管理
class ApiKeyManager {
  String getApiKey(String manufacturer);
  Map<String, String> getHeaders(String manufacturer);
  bool validateApiResponse(ApiResponse response);
}
```

### 7.2 数据安全

- **传输加密**: 所有API调用使用HTTPS/TLS 1.3
- **数据加密**: 敏感信息在Firestore中加密存储
- **访问控制**: Firestore安全规则限制数据访问
- **API密钥**: 环境变量管理，定期轮换

### 7.3 Firestore安全规则

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用户设备访问控制
    match /users/{userId}/devices/{deviceId} {
      allow read, write: if request.auth != null 
        && request.auth.uid == userId;
    }
    
    // 设备状态更新（仅Cloud Function）
    match /devices/{deviceId}/status/{statusId} {
      allow write: if request.auth.token.admin == true;
    }
  }
}
```

---

## 8. 性能优化

### 8.1 缓存策略

```dart
class CacheService {
  // 设备列表缓存
  static const String DEVICES_CACHE_KEY = 'user_devices';
  static const Duration CACHE_DURATION = Duration(hours: 1);
  
  Future<List<Device>?> getCachedDevices();
  Future<void> cacheDevices(List<Device> devices);
  Future<void> clearCache();
}
```

### 8.2 分页加载

```dart
class DevicePagination {
  static const int PAGE_SIZE = 20;
  
  Future<List<Device>> loadDevices({
    int page = 0,
    int limit = PAGE_SIZE,
    String? lastDocumentId,
  });
}
```

### 8.3 网络优化

- **请求合并**: 批量API调用减少网络请求
- **连接池**: HTTP连接复用
- **压缩**: 请求/响应数据压缩
- **CDN**: 静态资源CDN加速

---

## 9. 错误处理

### 9.1 错误分类

```dart
enum DeviceErrorType {
  networkError,      // 网络错误
  authError,         // 认证错误
  validationError,   // 验证错误
  apiError,          // API错误
  storageError,      // 存储错误
  unknownError,      // 未知错误
}

class DeviceException implements Exception {
  final DeviceErrorType type;
  final String message;
  final dynamic originalError;
  
  DeviceException(this.type, this.message, [this.originalError]);
}
```

### 9.2 错误处理策略

```dart
class ErrorHandler {
  static void handleError(DeviceException error) {
    switch (error.type) {
      case DeviceErrorType.networkError:
        _showRetryDialog();
        break;
      case DeviceErrorType.authError:
        _redirectToLogin();
        break;
      case DeviceErrorType.validationError:
        _showValidationMessage(error.message);
        break;
      default:
        _showGenericError();
    }
  }
}
```

---

## 10. 测试策略

### 10.1 单元测试

```dart
// DeviceController测试
class DeviceControllerTest {
  testAddDevice();
  testUpdateDevice();
  testDeleteDevice();
  testErrorHandling();
}

// DeviceService测试
class DeviceServiceTest {
  testFirestoreOperations();
  testRealtimeSync();
  testErrorScenarios();
}
```

### 10.2 集成测试

```dart
// 端到端测试
class DeviceE2ETest {
  testCompleteDeviceFlow();
  testMultiDeviceScenario();
  testOfflineScenario();
}
```

### 10.3 Mock服务

```dart
class MockDeviceApiService extends DeviceApiService {
  @override
  Future<ServiceResponse> validateDevice(Device device) async {
    // 模拟API响应
    return ServiceResponse(code: 200, msg: 'Success', data: {});
  }
}
```

---

## 11. 部署配置

### 11.1 环境配置

```yaml
# config/development.yaml
api:
  onenata:
    baseUrl: https://dev-api.onenata.com
    apiKey: ${ONENATA_DEV_API_KEY}
  pifi:
    baseUrl: https://dev-api.pifi.pet
    apiKey: ${PIFI_DEV_API_KEY}

firebase:
  projectId: onenata-dev
  apiKey: ${FIREBASE_DEV_API_KEY}

# config/production.yaml
api:
  onenata:
    baseUrl: https://api.onenata.com
    apiKey: ${ONENATA_PROD_API_KEY}
  pifi:
    baseUrl: https://api.pifi.pet
    apiKey: ${PIFI_PROD_API_KEY}
```

### 11.2 CI/CD流程

```yaml
# .github/workflows/deploy.yml
name: Deploy App
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: flutter test
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build APK
        run: flutter build apk --release
      
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Firebase App Distribution
        run: firebase appdistribution:distribute
```

---

## 12. 监控告警

### 12.1 应用监控

```dart
// 性能监控
class PerformanceMonitor {
  static void startTrace(String name);
  static void stopTrace(String name);
  static void recordMetric(String name, double value);
}

// 崩溃监控
class CrashReporter {
  static void reportError(dynamic error, StackTrace stackTrace);
  static void log(String message);
}
```

### 12.2 业务监控

- **设备连接率**: 设备在线/离线统计
- **API成功率**: 厂商API调用成功率
- **用户活跃度**: 设备操作频率统计
- **错误率**: 应用错误和崩溃率

---

## 13. 扩展性设计

### 13.1 新厂商集成

```dart
// 厂商适配器接口
abstract class DeviceVendorAdapter {
  Future<ServiceResponse> validateDevice(Device device);
  Future<ServiceResponse> getDeviceStatus(Device device);
  Future<ServiceResponse> sendCommand(Device device, String command, Map params);
}

// 具体厂商实现
class OneNataAdapter extends DeviceVendorAdapter {
  // OneNata特定实现
}

class PiFiAdapter extends DeviceVendorAdapter {
  // PiFi特定实现
}

// 适配器工厂
class VendorAdapterFactory {
  static DeviceVendorAdapter getAdapter(String manufacturer) {
    switch (manufacturer) {
      case 'onenata': return OneNataAdapter();
      case 'pifi': return PiFiAdapter();
      default: throw UnsupportedError('Unsupported vendor');
    }
  }
}
```

### 13.2 插件化架构

```dart
// 设备功能插件
abstract class DevicePlugin {
  String get name;
  List<DeviceType> get supportedTypes;
  Widget buildSettingsWidget(Device device);
  Future<void> handleCommand(Device device, String command);
}

// 插件管理器
class PluginManager {
  static final List<DevicePlugin> _plugins = [];
  
  static void registerPlugin(DevicePlugin plugin);
  static List<DevicePlugin> getPluginsForDevice(Device device);
}
```

---

## 14. 版本管理

### 14.1 API版本控制

```dart
class ApiVersionManager {
  static const String CURRENT_VERSION = 'v1';
  
  static String getVersionedUrl(String baseUrl, String endpoint) {
    return '$baseUrl/$CURRENT_VERSION/$endpoint';
  }
  
  static Map<String, String> getVersionHeaders() {
    return {
      'API-Version': CURRENT_VERSION,
      'Accept-Version': CURRENT_VERSION,
    };
  }
}
```

### 14.2 数据库迁移

```dart
class MigrationManager {
  static Future<void> migrateDeviceData() async {
    final version = await getDataVersion();
    
    if (version < 2) {
      await migrateToV2();
    }
    if (version < 3) {
      await migrateToV3();
    }
  }
}
```

---

## 15. 总结

OneNata设备管理系统采用现代化的架构设计，具备以下优势：

### ✅ 技术优势
- **模块化设计**: 高内聚低耦合的架构
- **扩展性强**: 支持新厂商和新设备类型
- **性能优化**: 缓存、分页、实时同步
- **安全可靠**: 多层安全防护和错误处理

### ✅ 业务优势
- **统一管理**: 多厂商设备统一界面
- **实时监控**: 设备状态实时同步
- **远程控制**: 便捷的设备操作
- **用户友好**: 直观的用户界面

### ✅ 维护优势
- **代码规范**: 清晰的代码结构和注释
- **测试覆盖**: 完整的测试用例
- **文档完善**: 详细的技术文档
- **监控完备**: 全面的监控告警

该系统为OneNata宠物生态提供了强大的设备管理能力，支持业务快速发展和技术演进。

---

## 附录

### A. 术语表

| 术语       | 含义                           |
| ---------- | ------------------------------ |
| Device     | 设备实体，包含所有设备信息     |
| Controller | GetX控制器，管理业务逻辑和状态 |
| Service    | 服务层，处理数据访问和API调用  |
| Firestore  | Firebase的NoSQL数据库          |
| Webhook    | HTTP回调机制                   |
| API        | 应用程序编程接口               |

### B. 常见问题

#### Q1: 如何添加新的设备厂商？
A1: 实现 `DeviceVendorAdapter` 接口，并在 `VendorAdapterFactory` 中注册新的适配器。

#### Q2: 设备离线时如何处理？
A2: 系统会缓存最后已知状态，并在设备重新连接时同步最新数据。

#### Q3: 如何扩展设备功能？
A3: 通过实现 `DevicePlugin` 接口来添加新的设备功能模块。

### C. 参考资料

- [Flutter官方文档](https://flutter.dev/docs)
- [GetX状态管理](https://github.com/jonataslaw/getx)
- [Firebase文档](https://firebase.google.com/docs)
- [OneNata API文档](https://api.onenata.com/docs)

---

**文档版本**: v1.0  
**最后更新**: 2024年1月15日  
**作者**: OneNata技术团队  
**审核**: 技术架构师  

---

*本文档包含OneNata设备管理系统的完整技术设计，如有疑问请联系技术团队。*
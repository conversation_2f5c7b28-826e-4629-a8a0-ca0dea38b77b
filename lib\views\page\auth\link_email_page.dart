import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/page/page_interceptor.dart';

import '../root_page.dart';
import '../start_page.dart';
import 'log_in_page.dart';

class LinkEmailPage extends StatefulWidget {
  const LinkEmailPage({super.key});

  @override
  LinkEmailPageState createState() => LinkEmailPageState();
}

class LinkEmailPageState extends State<LinkEmailPage> {

  // Declare your controller here
  late Future<LinkEmailPageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => LinkEmailPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<LinkEmailPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Indicator for loading
          return const Center(
            child: LoadingWidget(),
          );
        } else if (snapshot.hasError) {
          // Error handling
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          // Page functions
          final controller = snapshot.data!;

          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.primary,
            body: Stack(
              children: [
                AuthWidgetBuilder.buildWhiteSection(controller.theme),
                Column(
                  children: [
                    // Header
                    SizedBox(
                      height: controller.theme.layout.authAvatarHeaderLineHeight
                    ),

                    // Label
                    SizedBox(
                        height: 149.w,
                        child: Column(
                          children: [
                            Expanded(child: SizedBox.shrink()),
                            Obx(()=> AuthWidgetBuilder.buildLabel(
                                controller.theme,
                                title: controller.pageLabelTitle.value,
                                desc: controller.pageLabelDesc.value)
                            ),
                            SizedBox(height: 20.w,),
                          ],
                        )
                    ),

                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        controller: controller.scrollController,
                        child: Column(
                          children: [
                            // email input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxEmail2(
                                  controller.theme, controller.usernameInputController,
                                )),
                            SizedBox(height: 10.w,),

                            // password input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.passwordInputController,
                                )),
                            SizedBox(height: 10.w,),

                            // password input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.confirmInputController,
                                )),
                            SizedBox(height: 10.w,),

                            SizedBox(
                                height: screenSize.height
                                    - controller.theme.layout.authAvatarHeaderLineHeight
                                    - 149.w
                                    - 210.w
                                    - 160.w
                                    - ((Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) ? 0 : 59.w),
                                child: Obx(()=> (controller.passwordInputController.isFocused.isTrue ||
                                    controller.confirmInputController.isFocused.isTrue) ?
                                SingleChildScrollView(
                                    child: AuthWidgetBuilder.buildPasswordRequirementPanel(controller.theme)) :
                                SizedBox.shrink(),
                                )
                            ),

                            // submit button
                            SizedBox(
                              height: 60.w,
                              child: AuthWidgetBuilder.buildButton2(
                                controller.theme,
                                controller.buttonController,
                              )),
                            SizedBox(height: 20.w,),

                            // Change to email
                            SizedBox(
                              height: 50.w,
                              child: AuthWidgetBuilder.buildButton2(
                                controller.theme,
                                controller.alterButtonController,
                              )),
                            SizedBox(height: 30.w,),
                          ],
                        )
                      ),
                    ),
                  ],
                ),
                AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
              ],
            ),
          );
        }
      }
    );
  }
}

class LinkEmailPageController extends GetxController {

  // Services
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;
  final TextEditingController confirmInputBox = TextEditingController();
  late CommonTextField3Controller confirmInputController;

  final ScrollController scrollController = ScrollController();
  final _usernameKey = GlobalKey(debugLabel: 'usernameKey${DateTime.now().millisecondsSinceEpoch}');
  final _passwordKey = GlobalKey(debugLabel: 'passwordKey${DateTime.now().millisecondsSinceEpoch}');
  final _confirmKey = GlobalKey(debugLabel: 'confirmKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  // final CommonButtonController buttonController = Get.put(CommonButtonController(), tag: 'sign-up-email-submit');
  // final CommonButtonController alterButtonController = Get.put(CommonButtonController(), tag: 'sign-up-email-alter');

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  var isEmailAddressVerified = false.obs;
  var isPasswordValid = false.obs;
  var isPasswordConfirmed = false.obs;
  var verificationId = ''.obs;
  var verificationCode = ''.obs;
  var isEmailAddressValid = false.obs;
  var remainedTime = 30.obs;

  // var loggedInUser = Rx<User?>(null);

  Timer? timer;

  final String validCode = '123';

  // Theme plugin
  late ThemePlugin theme;

  Future<LinkEmailPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.link.email'.t18,
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
        'auth.label.link.email.desc'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildEmailAddressTextFieldController(
      theme, usernameInputBox.obs,
      globalKey: _usernameKey.obs,
      onCleared: onEmailAddressCleared.obs,
      onChanged: isInputEmailAddressValid.obs,
    );
    // Listen to the focus node
    usernameInputController.textFieldFocusNode.addListener(() {
      usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_usernameKey);
    });

    // build password input controller
    passwordInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, passwordInputBox.obs,
      globalKey: _passwordKey.obs,
      hint: 'auth.hint.password'.t18.obs,
      onCleared: onPasswordCleared.obs,
      onChanged: isInputPasswordValid.obs,
    );
    // Listen to the focus node
    passwordInputController.textFieldFocusNode.addListener(() {
      passwordInputController.isFocused.value = passwordInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_passwordKey);
    });

    // build confirm password input controller
    confirmInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, confirmInputBox.obs,
      globalKey: _confirmKey.obs,
      hint: 'auth.hint.re-enter.password'.t18.obs,
      onCleared: onConfirmCleared.obs,
      onChanged: isInputConfirmValid.obs,
    );
    // Listen to the focus node
    confirmInputController.textFieldFocusNode.addListener(() {
      confirmInputController.isFocused.value = confirmInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_confirmKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.send.link'.t18.obs,
      onPressed: sendVerificationEmail.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'common.button.skip'.t18.obs,
      onPressed: skip.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    timer?.cancel();
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    confirmInputController.textFieldFocusNode.unfocus();
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && (key == _usernameKey && usernameInputController.textFieldFocusNode.hasFocus ||
        key == _passwordKey && passwordInputController.textFieldFocusNode.hasFocus ||
        key == _confirmKey && confirmInputController.textFieldFocusNode.hasFocus)) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  bool checkPasswordMistake() {
    bool hasMistake = isEmailAddressValid.isTrue && isPasswordValid.isFalse && passwordInputBox.text != '';
    return hasMistake;
  }

  bool checkConfirmMistake() {
    return isEmailAddressValid.isTrue && isPasswordValid.isTrue && isPasswordConfirmed.isFalse && confirmInputBox.text != '';
  }

  Future<void> onEmailAddressCleared(BuildContext context) async {
    await resetEmailAddressState();
  }

  Future<void> onPasswordCleared(BuildContext context) async {

    timer?.cancel();
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    isPasswordValid.value = false;
    isPasswordConfirmed.value = false;

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> onConfirmCleared(BuildContext context) async {

    timer?.cancel();
    confirmInputBox.text = '';
    confirmInputController.isEnabled.value = true;
    confirmInputController.hasMistake.value = false;
    isPasswordConfirmed.value = false;

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputEmailAddressValid({required BuildContext context, required bool result}) async {
    isEmailAddressValid.value = result;
    if (isEmailAddressValid.isFalse && usernameInputBox.text.length > 10) {
      usernameInputController.hasMistake.value = true;
    }

    if (isEmailAddressValid.isTrue && isPasswordValid.isTrue && isPasswordConfirmed.isTrue) {
      buttonController.isEnabled.value = true;
    }
    else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputPasswordValid({required BuildContext context, required bool result}) async {
    isPasswordValid.value = result;
    passwordInputController.hasMistake.value = isPasswordValid.isFalse && passwordInputBox.text != '';

    if (isEmailAddressValid.isTrue && isPasswordValid.isTrue && isPasswordConfirmed.isTrue) {
      buttonController.isEnabled.value = true;
    }
    else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputConfirmValid({required BuildContext context, required bool result}) async {
    isPasswordConfirmed.value = result;
    confirmInputController.hasMistake.value = isPasswordConfirmed.isFalse && confirmInputBox.text != '';

    if (isEmailAddressValid.isTrue && isPasswordValid.isTrue && isPasswordConfirmed.isTrue) {
      buttonController.isEnabled.value = true;
    }
    else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationEmail() async {

    if (isEmailAddressValid.isFalse) return;
    if (isPasswordValid.isFalse) return;
    if (isPasswordConfirmed.isFalse) return;

    if (passwordInputBox.text != confirmInputBox.text) {

      pageLabelDesc.value = CommonText(
        'auth.error.password.not.match'.t18,
        theme.textStyleExtension.authPageWarning,
      );
      return;
    }

    if (!RegExp(InputUtil.validEmailAddressRegEx).hasMatch(usernameInputBox.text)) {
      pageLabelDesc.value = CommonText(
        'auth.error.email.invalid'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
      return;
    }

    String email = usernameInputBox.text;
    String password = passwordInputBox.text;

    // Check existing
    UserAccount? userAccount = await userDao.getUserAccountById(email: email);
    if (userAccount != null) {
      pageLabelDesc.value = CommonText(
        'auth.error.email.exists'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
      return;
    }

    ServiceResponse<String> response = await authService.linkEmailAddress(email, password);

    if (response.code == 200) {

      String salt = CredentialUtil.generateHashSalt();
      String hashedPassword = CredentialUtil.hashPasswordWithSalt(password, salt);

      // Persist the user account and auth record in Firestore and secure storage
      await userService.createUserAccount(authService.currentUser.value!, AuthChannel.emailPassword, salt: salt, hashedPassword: hashedPassword);

      // Redirect to verify page
      await resetState();
      Get.to(PendingVerifyPage());
      return;
    }
    else if (response.code == 400) {
      // loggedInUser.value = null;
      pageLabelDesc.value = CommonText(
        'email linked but not verified'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
    }
    else if (response.code == 403) {
      // loggedInUser.value = null;
      pageLabelDesc.value = CommonText(
        'not permitted'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
    }
    else if (response.code == 409) {
      // loggedInUser.value = null;
      pageLabelDesc.value = CommonText(
        'auth.error.email.exists'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
    }
    else {
      // loggedInUser.value = null;
      pageLabelDesc.value = CommonText(
        'auth.error.desc'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
    }
  }

  Future<void> resetState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.email'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    confirmInputBox.text = '';
    confirmInputController.isEnabled.value = true;
    confirmInputController.hasMistake.value = false;

    // Reset states
    isEmailAddressValid.value = false;
    isPasswordValid.value = false;
    isPasswordConfirmed.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;

    // Button
    buttonController.text = 'auth.button.login.2'.t18.obs;
    buttonController.onPressed.value = sendVerificationEmail;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> resetEmailAddressState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.email'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;

    // Reset states
    isEmailAddressValid.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;

    // Button
    buttonController.text = 'auth.button.login.2'.t18.obs;
    buttonController.onPressed.value = sendVerificationEmail;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> skip() async {

    await resetState();

    // Skip user address setting
    // await Storage.instance.write(StorageKeys.userDataEmailSkipped, true);
    userDataEmailSkippedThisTime = true;

    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const UserProfilePage());
    } else {
      Get.back();
    }
  }
}

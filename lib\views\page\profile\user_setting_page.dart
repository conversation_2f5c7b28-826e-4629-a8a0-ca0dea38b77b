import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingPage extends StatefulWidget {
  const UserSettingPage({super.key});

  @override
  UserSettingPageState createState() => UserSettingPageState();
}

class UserSettingPageState extends State<UserSettingPage> {
  late Future<UserSettingPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserSettingPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  //_buildTopSection(controller),
                  ProfileWidgetBuilder.buildUserSettingTopSection(
                    theme: controller.theme,
                    onBack: () {
                      Get.back();
                    },
                    topic: "Account",
                    avatar: controller.pageHeaderUserAvatar.value,
                  ),
                  ProfileWidgetBuilder.buildText(controller.theme, 128,'Account'),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 172,
                    title: 'Profile',
                    showLeftIcon: true,
                    icon: Icons.person_outline,
                    onRightIconTap: () {
                      Get.toNamed("/userSettingName");
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 234,
                    title: 'Address',
                    showLeftIcon: true,
                    icon: Icons.home_outlined,
                    onRightIconTap: () {
                      Get.toNamed("/userSettingAddress");
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 296,
                    title: 'Email',
                    showLeftIcon: true,
                    icon: Icons.mail_outline,
                    onRightIconTap: () {
                      Get.toNamed("/linkEmail");
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 358,
                    title: 'Password',
                    showLeftIcon: true,
                    icon: Icons.lock_outline,
                    onRightIconTap: () {
                      Get.to(() => const ResetPasswordPage());
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 420,
                    title: 'Notification',
                    showLeftIcon: true,
                    icon: Icons.notifications_outlined,
                    onRightIconTap: () {
                      Get.toNamed("/userSettingNotification");
                    },
                  ),

                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 482,
                    title: 'Privacy',
                    showLeftIcon: true,
                    icon: Icons.privacy_tip_outlined,
                    onRightIconTap: () {
                      Get.toNamed("/userSettingPrivacy");
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 596,
                    title: 'Contact us',
                    showLeftIcon: true,
                    icon: Icons.phone_outlined,
                    onRightIconTap: () {
                      Get.toNamed("/userSettingContact");
                    },
                  ),
                  ProfileWidgetBuilder.buildUserSettingProfileSection(
                    theme: controller.theme,
                    top: 658,
                    title: 'Log out',
                    showLeftIcon: true,
                    icon: Icons.logout_outlined,
                    onRightIconTap: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: false,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
                        ),
                        backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
                        builder: (BuildContext context) {
                          return Padding(
                            padding: EdgeInsets.symmetric(vertical:24.w,horizontal: 38.w),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  "Do you want to continue logging out?",
                                  style: controller.theme.textStyleExtension.contactInfo,
                                ),
                                SizedBox(height: 20.w),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop(); // Close sheet
                                    controller._authService.signOut();
                                    Get.offAllNamed("/logIn");
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsets.symmetric(vertical: 16.w),
                                    decoration: BoxDecoration(
                                      color: controller.theme.themeData.colorScheme.secondary,
                                      borderRadius: BorderRadius.circular(30.5.r),
                                    ),
                                    child: Center(
                                      child: Text(
                                        "Continue",
                                        style: controller.theme.textStyleExtension.buttonLargeFilled,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 20.w),
                                GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop(); // Close sheet
                                  },
                                  child: Text(
                                    "Cancel",
                                    style: controller.theme.textStyleExtension.authPageAlterButton,
                                  ),
                                ),
                                //SizedBox(height: 8.w),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                  ProfileWidgetBuilder.buildText(controller.theme, 552,'help'.t18),
                ],
              ),
            );
          }
        });
  }
}

class UserSettingPageController extends GetxController {

  // Theme plugin
  final AuthService _authService = AuthService.instance;
  //var userAccount = Rx<UserAccount?>(null);
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  late ThemePlugin theme;

  Future<UserSettingPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    //userAccount.value = _authService.userAccount.value;

    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
        theme, userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);
    return this;
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/services/auth_service.dart';
import 'package:onenata_app/services/pet_service.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/component/profile/profile_widget_builder.dart';
import 'package:onenata_app/views/theme/theme_plugin.dart';
import 'package:onenata_app/views/component/appointment/service_selection_bottom_sheet.dart';

class PetSelectionBottomSheet extends StatefulWidget {
  final VoidCallback? onAppointmentMade;

  const PetSelectionBottomSheet({
    Key? key,
    this.onAppointmentMade,
  }) : super(key: key);

  @override
  State<PetSelectionBottomSheet> createState() => _PetSelectionBottomSheetState();
}

class _PetSelectionBottomSheetState extends State<PetSelectionBottomSheet> {
  final AuthService _authService = Get.find<AuthService>();
  final PetService _petService = Get.find<PetService>();
  
  List<Pet> userPets = [];
  Pet? selectedPet;
  bool isLoading = true;
  ThemePlugin? theme;

  @override
  void initState() {
    super.initState();
    _initTheme();
    _loadUserPets();
  }

  Future<void> _initTheme() async {
    theme = await Get.putAsync(() => ThemePlugin().init());
  }

  Future<void> _loadUserPets() async {
    try {
      // 首先尝试从缓存获取
      if (_authService.ownedPets.value != null && _authService.ownedPets.value!.isNotEmpty) {
        setState(() {
          userPets = _authService.ownedPets.value!;
          isLoading = false;
        });
      } else {
        // 从数据库获取
        final pets = await _petService.getOwnedPets(_authService.userAccount.value!.sid!);
        setState(() {
          userPets = pets ?? [];
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('Error loading pets: $e');
    }
  }

  void _selectPet(Pet pet) {
    setState(() {
      selectedPet = pet;
    });
  }

  void _makeAppointment() {
    if (selectedPet != null) {
      // 关闭当前弹窗并打开服务选择弹窗
      Get.back();
      ServiceSelectionBottomSheetHelper.show(
        selectedPet: selectedPet!,
        onServiceSelected: () {
          widget.onAppointmentMade?.call();
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 250.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: 36.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),

          // 标题
          Padding(
            padding: EdgeInsets.only(top: 20.w, left: 20.w, right: 20.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Please select the pet you are booking to',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF262626),
                ),
              ),
            ),
          ),
          
          // 宠物列表
          Expanded(
            child: isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: OneNataClassicColors.veronica,
                    ),
                  )
                : userPets.isEmpty
                    ? Center(
                        child: Text(
                          'No pets found',
                          style: TextStyle(
                            fontFamily: 'Manrope',
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            color: Color(0xFF999999),
                          ),
                        ),
                      )
                    : Container(
                        height: 100.w,
                        margin: EdgeInsets.symmetric(vertical: 20.w),
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          itemCount: userPets.length,
                          itemBuilder: (context, index) {
                            final pet = userPets[index];
                            final isSelected = selectedPet?.sid == pet.sid;

                            return GestureDetector(
                              onTap: () => _selectPet(pet),
                              child: Container(
                                margin: EdgeInsets.only(
                                  right: index < userPets.length - 1 ? 20.w : 0,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // 宠物头像
                                    Stack(
                                      children: [
                                        // 头像
                                        SizedBox(
                                          width: 60.w,
                                          height: 60.w,
                                          child: ClipOval(
                                            child: _buildPetAvatar(pet),
                                          ),
                                        ),
                                        // 选中状态的边框
                                        if (isSelected)
                                          Container(
                                            width: 60.w,
                                            height: 60.w,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: OneNataClassicColors.veronica,
                                                width: 3.w,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),

                                    // 宠物名字
                                    SizedBox(height: 8.w),
                                    Text(
                                      pet.name ?? 'Unknown',
                                      style: TextStyle(
                                        fontFamily: 'Manrope',
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.w500,
                                        color: Color(0xFF262626),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
          ),
          
          // Make appointment 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 20.w, right: 20.w),
            width: double.infinity,
            height: 44.w,
            child: ElevatedButton(
              onPressed: selectedPet != null ? _makeAppointment : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedPet != null 
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Make appointment',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPetAvatar(Pet pet) {
    if (theme == null) {
      return _buildDefaultAvatar();
    }

    return FutureBuilder<Widget?>(
      future: ProfileWidgetBuilder.buildPetAvatar(
        theme!,
        userId: _authService.userAccount.value!.sid!,
        petId: pet.sid!,
        avatar: pet.avatar,
        size: 54.w, // 稍微小一点，为边框留出空间
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: 54.w,
            height: 54.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey.shade200,
            ),
            child: Center(
              child: SizedBox(
                width: 16.w,
                height: 16.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: OneNataClassicColors.veronica,
                ),
              ),
            ),
          );
        } else if (snapshot.hasError || snapshot.data == null) {
          return _buildDefaultAvatar();
        } else {
          return snapshot.data!;
        }
      },
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: 54.w,
      height: 54.w,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Color(0xFFF0F0F0),
      ),
      child: Icon(
        Icons.pets,
        size: 24.w,
        color: Color(0xFF999999),
      ),
    );
  }
}

/// 显示宠物选择底部弹窗的静态方法
class PetSelectionBottomSheetHelper {
  static void show({VoidCallback? onAppointmentMade}) {
    Get.bottomSheet(
      PetSelectionBottomSheet(onAppointmentMade: onAppointmentMade),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}

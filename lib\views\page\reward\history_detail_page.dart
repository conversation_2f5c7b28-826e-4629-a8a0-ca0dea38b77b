import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import 'history_detail_page.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';

import '../page_interceptor.dart';
import '../root_page.dart';


class HistoryDetailPage extends StatefulWidget {
  final String itemTitle;
  final String itemSubtitle;
  final String itemPoints;

  const HistoryDetailPage({
    super.key,
    required this.itemTitle,
    required this.itemSubtitle,
    required this.itemPoints,
  });

  @override
  HistoryDetailPageState createState() => HistoryDetailPageState();
}

class HistoryDetailPageState extends State<HistoryDetailPage> {
  late Future<HistoryDetailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => HistoryDetailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<HistoryDetailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  // Top section - positioned at the top
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Redeem history",
                      showAvatar: false,
                    ),
                  ),
                  // Content section - positioned below top section
                  Positioned(
                    top: 80.w, // Adjust based on top section height
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Status message
                          _buildStatusMessage(),
                          SizedBox(height: 24.w),
                          // Product info
                          _buildProductInfo(),
                          SizedBox(height: 24.w),
                          // Delivery info
                          _buildDeliveryInfo(),
                          SizedBox(height: 24.w),
                          // Tracking info
                          _buildTrackingInfo(),
                          SizedBox(height: 24.w),
                          // Estimated delivery
                          _buildEstimatedDelivery(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
        });
  }

  // Build status message
  Widget _buildStatusMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'We\'re preparing your order...',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w700, // Bold
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          'We\'ve received your redeem request and are preparing your shipment.',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
      ],
    );
  }

  // Build product info
  Widget _buildProductInfo() {
    return Row(
      children: [
        // Product image
        Container(
          width: 60.w,
          height: 60.w,
          decoration: BoxDecoration(
            color: Color(0xFFF5F1E8),
            borderRadius: BorderRadius.circular(8.w),
          ),
          child: Center(
            child: Icon(
              Icons.image,
              size: 30.w,
              color: Color(0xFFE0E0E0),
            ),
          ),
        ),
        SizedBox(width: 16.w),
        // Product details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.itemTitle,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500, // Medium
                  color: Color(0xFF262626),
                  height: 1.6,
                ),
              ),
              SizedBox(height: 4.w),
              Text(
                'Color: Coral',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500, // Medium
                  color: Color(0xFF262626).withValues(alpha: 0.5), // 50% opacity
                  height: 1.6,
                ),
              ),
              SizedBox(height: 4.w),
              Text(
                'Quantity: 1',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500, // Medium
                  color: Color(0xFF262626).withValues(alpha: 0.5), // 50% opacity
                  height: 1.6,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build delivery info
  Widget _buildDeliveryInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Info:',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w700, // Bold
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          '312b ave, Rd#23, Surrey, BC, V5P 1W3',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          'Anthony Chen',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          '778 903 5688',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
      ],
    );
  }

  // Build tracking info
  Widget _buildTrackingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tracking number:',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w700, // Bold
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          'ON#30897765456B',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
      ],
    );
  }

  // Build estimated delivery
  Widget _buildEstimatedDelivery() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Estimated delivery:',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w700, // Bold
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
        SizedBox(height: 8.w),
        Text(
          'Date will be available once it gets shipped',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 16.sp,
            fontWeight: FontWeight.w500, // Medium
            color: Color(0xFF262626),
            height: 1.6,
          ),
        ),
      ],
    );
  }
}

class HistoryDetailPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  late RxString displayNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  // Rewards page specific variables
  final RxInt selectedTabIndex = 0.obs; // 0: Tasks, 1: Redeem, 2: History
  final RxBool isPointsCardExpanded = false.obs;
  final RxInt availablePoints = 1590.obs;
  final RxInt weeklyProgress = 40.obs;
  final RxInt weeklyGoal = 50.obs;

  Future<HistoryDetailPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    return this;
  }

}

import 'package:logger/logger.dart' as real_logger;

class Logger {
  final _logger = real_logger.Logger(
    printer: real_logger.PrettyPrinter(
      // methodCount: 0,
      // errorMethodCount: 5,
      // lineLength: 100,
      // colors: true,
      // printEmojis: true,
      // printTime: true,
    ),
  );

  void d(String message) => _logger.d('${DateTime.now().toString()} >>> $message');
  void dd(String message) {assert(() {
    _logger.d('${DateTime.now().toString()} >>> $message');
      return true;
    }());
  }
  void i(String message) => _logger.i('${DateTime.now().toString()} >>> $message');
  void w(String message) => _logger.w('${DateTime.now().toString()} >>> $message');
  void e(String message) => _logger.e('${DateTime.now().toString()} >>> $message');
  void f(String message) => _logger.f('${DateTime.now().toString()} >>> $message');
}

final logger = Logger();

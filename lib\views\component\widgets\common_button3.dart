import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import '../../../common/plugin/logger/logger.dart';
import 'common_bar.dart';

class CommonButton3 extends StatelessWidget {

  final CommonButton3Controller controller;

  const CommonButton3({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {

    // logger.i('Object ID: ${identityHashCode(controller)}');

    // Bg bar
    Widget bgBar = Obx (()=> CommonBar(

      width: controller.width.value,
      height: controller.height.value,
      circular: controller.circular.value,
      color: controller.isEnabled.value ? controller.color.value : controller.disabledColor.value,

      hasBorder: controller.hasBorder.value,
      borderColor: controller.borderColor.value,
      borderWidth: controller.borderWidth.value,

      hasShadow: controller.hasShadow.value,
      shadowColor: controller.shadowColor.value,
      shadowOffset: controller.shadowOffset.value,
      shadowBlurRadius: controller.shadowBlurRadius.value,
      shadowSpreadRadius: controller.shadowSpreadRadius.value,
    ));

    // Text widget
    Widget? textWidget = Container(
      alignment: controller.shownWidgetMainAlignment.value,
      width: controller.width.value,
      height: controller.height.value,
      child: Obx (()=> controller.shownWidget.value ?? CommonText(
        controller.text.value,
        controller.isEnabled.value ? controller.textStyle.value : controller.disabledTextStyle.value,
      )),
    );

    return Obx(()=> GestureDetector(

      onTap: () async {
        // close keyboard
        FocusScope.of(context).unfocus();

        if (controller.isInProgress.value) {
          return;
        }

        if (controller.isEnabled.value) {
          await controller.setLoadingStatus(true);
          if (controller.onPressed.value != null) await controller.onPressed.value!();
          await Future.delayed(const Duration(milliseconds: 200));
          await controller.setLoadingStatus(false);
        }
      },
      child: Stack(
        alignment: controller.shownWidgetMainAlignment.value,
        children: [
          bgBar,
          controller.isInProgress.isFalse
              ? textWidget
              : controller.loadingWidget.value != null
                ? controller.loadingWidget.value!
                : SpinKitThreeBounce(size: controller.height / 3, color: controller.textStyle.value.color,),
        ],
      )
    ));
  }
}

class CommonButton3Controller {

  var isEnabled = true.obs;
  var width = 327.w.obs;
  var height = 60.w.obs;
  var color = Colors.white.obs;
  var disabledColor = Colors.black12.obs;
  var shownWidgetMainAlignment = Alignment.center.obs;
  var shownWidgetMainPadding = Rx<Padding?>(null);
  var shownWidget = Rx<Widget?>(null);
  var text = ''.obs;
  var textStyle = TextStyle().obs;
  var disabledTextStyle = TextStyle().obs;

  var hasRadius = true.obs;
  var circular = 30.w.obs;

  var hasBorder = false.obs;
  var borderColor = Colors.black12.obs;
  var borderWidth = 1.w.obs;

  var hasShadow = false.obs;
  var shadowColor = Colors.black12.obs;
  var shadowOffset = Offset(0, 1.w).obs;
  var shadowBlurRadius = 1.w.obs;
  var shadowSpreadRadius = 0.0.obs;

  var isInProgress = false.obs;
  var loadingWidget = Rx<Widget?>(null);
  var onPressed = Rx<AsyncVoidCallback?>(null);
  //
  // CommonButton3Controller({
  //
  // isEnabled = true.obs;
  // var width = 327.w.obs;
  // var height = 60.w.obs;
  // var color = Colors.white.obs;
  // var disabledColor = Colors.black12.obs;
  // var text = ''.obs;
  // var textStyle = TextStyle().obs;
  // var disabledTextStyle = TextStyle().obs;
  //
  // var hasRadius = true.obs;
  // var circular = 30.w.obs;
  //
  // var hasBorder = false.obs;
  // var borderColor = Colors.black12.obs;
  // var borderWidth = 1.w.obs;
  //
  // var hasShadow = false.obs;
  // var shadowColor = Colors.black12.obs;
  // var shadowOffset = Offset(0, 1.w).obs;
  // var shadowBlurRadius = 1.w.obs;
  // var shadowSpreadRadius = 0.0.obs;
  //
  // var isInProgress = false.obs;
  // var loadingWidget = Rx<Widget?>(null);
  // var onPressed = Rx<AsyncVoidCallback?>(null);
  //
  //
  //
  //   this.width,
  //   this.height = 60,
  //   this.color,
  //   this.disabledColor,
  //   this.text = '',
  //   this.textStyle,
  //   this.disabledTextStyle,
  //
  //   this.hasRadius = true,
  //   this.circular = 30,
  //
  //   this.hasBorder = false,
  //   this.borderColor = Colors.black12,
  //   this.borderWidth = 1,
  //
  //   this.hasShadow = false,
  //   this.shadowColor = Colors.black12,
  //   this.shadowOffset,
  //   this.shadowBlurRadius,
  //   this.shadowSpreadRadius,
  //   this.onPressed,
  //   this.loadingWidget,
  // });

  CommonButton3Controller({
    Rx<GlobalKey?>? globalKey,
    RxBool? isEnabled,
    RxDouble? width,
    RxDouble? height,
    Rx<Color>? color,
    Rx<Color>? disabledColor,
    Rx<Alignment>? shownWidgetAlignment,
    Rx<Padding>? shownWidgetPadding,
    Rx<Widget?>? shownWidget,
    RxString? text,
    Rx<TextStyle>? textStyle,
    Rx<TextStyle>? disabledTextStyle,
    RxBool? hasRadius,
    RxDouble? circular,
    RxBool? hasBorder,
    Rx<Color>? borderColor,
    RxDouble? borderWidth,
    RxBool? hasShadow,
    Rx<Color>? shadowColor,
    Rx<Offset>? shadowOffset,
    RxDouble? shadowBlurRadius,
    RxDouble? shadowSpreadRadius,
    RxBool? isInProgress,
    Rx<Widget?>? loadingWidget,
    Rx<AsyncVoidCallback?>? onPressed,
  }): isEnabled = isEnabled?? true.obs,
      width = width?? 327.w.obs,
      height = height?? 60.w.obs,
      color = color?? Colors.white.obs,
      disabledColor = disabledColor?? Colors.black12.obs,
      shownWidgetMainAlignment = shownWidgetAlignment?? Alignment.center.obs,
      shownWidgetMainPadding = shownWidgetPadding?? Rx<Padding?>(null),
      shownWidget = shownWidget?? Rx<Widget?>(null),
      text = text?? ''.obs,
      textStyle = textStyle?? TextStyle().obs,
      disabledTextStyle = disabledTextStyle?? TextStyle().obs,
      hasRadius = hasRadius?? true.obs,
      circular = circular?? 30.w.obs,
      hasBorder = hasBorder?? false.obs,
      borderColor = borderColor?? Colors.black12.obs,
      borderWidth = borderWidth?? 1.w.obs,
      hasShadow = hasShadow?? false.obs,
      shadowColor = shadowColor?? Colors.black12.obs,
      shadowOffset = shadowOffset?? Offset(0, 1.w).obs,
      shadowBlurRadius = shadowBlurRadius?? 1.w.obs,
      shadowSpreadRadius = shadowSpreadRadius?? 0.0.obs,
      isInProgress = isInProgress?? false.obs,
      loadingWidget = loadingWidget?? Rx<Widget?>(null),
      onPressed = onPressed?? Rx<AsyncVoidCallback?>(null)
  ;

  Future<void> setLoadingStatus(bool status) async {
    isInProgress.value = status;
  }

  void toggleEnabled() {
    isEnabled.value = !isEnabled.value;
  }
}
import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum MediaType {

  userAvatar('UAV'),
  petAvatar('PAV'),
  image('IMG'),
  video('VID'),
  ;

  final String code; // Stored code in database
  const MediaType(this.code);

  // Factory constructor to create a TestType object based on the code
  factory MediaType.fromCode(String code) {
    return MediaType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

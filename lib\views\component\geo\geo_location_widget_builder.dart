import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/appointment/pet_selection_bottom_sheet.dart';

class GeoLocationWidgetBuilder {
  static Widget buildMyLocationIcon(ThemePlugin theme) {
    // no Background

    // Image
    CommonImageAsset avatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.geoMyLocationIconSize,
      height: theme.layout.geoMyLocationIconSize,
    );

    return CommonAvatar(
      hasContent: true,
      content: avatar,
    );
  }

  static Widget buildCommonLocationIcon2(ThemePlugin theme, {Widget? avatar}) {
    // Bg image
    Widget bgImg = CommonImageAsset(
      path: ImageAssetConst.mapPlacePetActiveIcon,
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
    );

    // Avatar bg
    Widget avatarBg = CommonBar(
      shape: BoxShape.circle,
      color: theme.colorExtension.geoCommonPointBg,
      radius: theme.layout.geoPetLocationAvatarSize,
    );

    // Avatar bg
    Widget avatarDot = CommonBar(
      shape: BoxShape.circle,
      color: Colors.white,
      radius: theme.layout.geoCommonLocationDotSize,
    );

    return SizedBox(
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          bgImg,
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 7.w,
              ),
              SizedBox(
                width: theme.layout.geoPetLocationAvatarSize,
                height: theme.layout.geoPetLocationAvatarSize,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    avatarBg,
                    avatarDot,
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget buildCommonLocationIcon(ThemePlugin theme, {Widget? avatar}) {
    return Icon(
      Icons.location_pin,
      size: theme.layout.geoCommonLocationDotSize,
      color: theme.colorExtension.geoCommonPointBg,
    );
  }

  static Widget buildPetActiveLocationIcon(ThemePlugin theme,
      {Widget? avatar}) {
    // Bg image
    Widget bgImg = CommonImageAsset(
      path: ImageAssetConst.mapPlacePetActiveIcon,
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
    );

    // Avatar bg
    Widget avatarBg = CommonBar(
      shape: BoxShape.circle,
      color: Colors.white,
      radius: theme.layout.geoPetLocationAvatarSize,
    );

    // Default avatar image
    CommonImageAsset defaultAvatar = CommonImageAsset(
      path: ImageAssetConst.defaultPetAvatar,
      isCircle: true,
      width: theme.layout.geoPetLocationAvatarSize - 4.w,
      height: theme.layout.geoPetLocationAvatarSize - 4.w,
    );

    return SizedBox(
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          bgImg,
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 7.w,
              ),
              SizedBox(
                width: theme.layout.geoPetLocationAvatarSize,
                height: theme.layout.geoPetLocationAvatarSize,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    avatarBg,
                    avatar ?? defaultAvatar,
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget buildPetInActiveLocationIcon(ThemePlugin theme,
      {Widget? avatar}) {
    // Bg image
    Widget bgImg = CommonImageAsset(
      path: ImageAssetConst.mapPlacePetInActiveIcon,
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
    );

    // Default avatar image
    CommonImageAsset defaultAvatar = CommonImageAsset(
      path: ImageAssetConst.defaultPetAvatar,
      isCircle: true,
      width: theme.layout.geoPetLocationAvatarSize,
      height: theme.layout.geoPetLocationAvatarSize,
    );

    return SizedBox(
      width: theme.layout.geoPetLocationIconWidth,
      height: theme.layout.geoPetLocationIconHeight,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          bgImg,
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: 7.w,
              ),
              avatar ?? defaultAvatar,
            ],
          ),
        ],
      ),
    );
  }

  static Widget buildGeoPlaceIcon(ThemePlugin theme, GeoPlace place,
      {GeoPlaceFilterType? type = GeoPlaceFilterType.park,
      bool? isSelected = false}) {
    // Drop icon
    Widget drop = isSelected == true
        ? CommonImageAsset(
            color: theme.colorExtension.geoPointParkSelected,
            path: ImageAssetConst.mapPlacePetActiveIcon,
            width: 30.w,
            height: 42.85.w,
          )
        : ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorExtension.geoPointParkGradient1,
                  theme.colorExtension.geoPointParkGradient2,
                ],
              ).createShader(bounds);
            },
            blendMode: BlendMode
                .srcIn, // Ensures gradient is applied over the image's alpha
            child: CommonImageAsset(
              path: ImageAssetConst.mapPlacePetActiveIcon,
              width: 30.w,
              height: 42.85.w,
            ),
          );

    // White circle
    Widget whiteCircle = CommonBar(
      shape: BoxShape.circle,
      radius: theme.layout.geoPlaceIconWhiteSize / 2,
      color: Colors.white,
    );

    // Color circle
    Widget greenCircle = CommonBar(
        shape: BoxShape.circle,
        radius: theme.layout.geoPlaceIconColorSize / 2,
        color: type == GeoPlaceFilterType.park
            ? theme.colorExtension.geoPark
            : theme.colorExtension.geoTrail);

    // Icon
    Widget placeIcon = CommonImageAsset(
      path: type == GeoPlaceFilterType.park
          ? ImageAssetConst.mapPlaceParkIcon
          : ImageAssetConst.mapPlaceTailIcon,
      color: Colors.white,
      width: type == GeoPlaceFilterType.park
          ? theme.layout.geoPlaceIconParkWidth
          : theme.layout.geoPlaceIconTrailWidth,
      height: type == GeoPlaceFilterType.park
          ? theme.layout.geoPlaceIconParkHeight
          : theme.layout.geoPlaceIconTrailHeight,
    );

    // Assemble
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        drop,
        Container(
          margin: EdgeInsets.only(
            top: theme.layout.geoPlaceIconWhiteSize / 2,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              whiteCircle,
              greenCircle,
              placeIcon,
            ],
          ),
        )
      ],
    );
  }

  static Widget buildShownWidgetOfGeoFilterButton(ThemePlugin theme,
      {GeoPlaceFilterType? type = GeoPlaceFilterType.park,
      bool? isSelected = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          width: 12.w,
        ),
        buildGeoFilterButtonIcon(theme, type: type, isSelected: isSelected),
        SizedBox(width: 6.w),
        CommonText(
            type!.t18key.t18, theme.textStyleExtension.geoFilterButtonText),
      ],
    );
  }

  static Widget buildGeoFilterButtonIcon(ThemePlugin theme,
      {GeoPlaceFilterType? type = GeoPlaceFilterType.park,
      bool? isSelected = false}) {
    if (type == GeoPlaceFilterType.outdoor) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlaceOutdoorIcon,
        color: theme.colorExtension.geoOutdoor,
        width: 17.w,
        height: 17.w,
      );
    } else if (type == GeoPlaceFilterType.park) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlaceParkIcon,
        color: theme.colorExtension.geoPark,
        width: 17.w,
        height: 17.w,
      );
    } else if (type == GeoPlaceFilterType.trail) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlaceTailIcon,
        color: theme.colorExtension.geoTrail,
        width: 17.w,
        height: 14.7.w,
      );
    } else if (type == GeoPlaceFilterType.vet) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlaceVetIcon,
        color: theme.colorExtension.geoVet,
        width: 15.85.w,
        height: 15.w,
      );
    } else if (type == GeoPlaceFilterType.petStore) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlacePetStoreIcon,
        color: theme.colorExtension.geoPetStore,
        width: 14.w,
        height: 14.w,
      );
    } else if (type == GeoPlaceFilterType.dogFriendlyRestaurant) {
      return CommonImageAsset(
        path: ImageAssetConst.mapPlaceDogFriendlyRestaurantIcon,
        color: theme.colorExtension.geoDogFriendlyRestaurant,
        width: 17.w,
        height: 15.w,
      );
    } else if (type == GeoPlaceFilterType.favor) {
      return CommonImageAsset(
        path: isSelected!
            ? ImageAssetConst.mapPlaceFavorIconSelected
            : ImageAssetConst.mapPlaceFavorIcon,
        color: theme.colorExtension.geoFavor,
        width: 16.w,
        height: 14.1.w,
      );
    }

    return CommonImageAsset(
      path: ImageAssetConst.mapPlaceParkIcon,
      color: theme.colorExtension.geoPark,
      width: 17.w,
      height: 17.w,
    );
  }

  static Widget buildGeoPlaceInfoCard(ThemePlugin theme,
      {required GeoPlace place,
      CommonButton3Controller? checkInController,
      CommonButton3Controller? favorController,
      CommonButton3Controller? reportHoursController,
      VoidCallback? onReportHoursTapped,
      GeoPlaceFilterType? type = GeoPlaceFilterType.park}) {
    // 如果没有提供onReportHoursTapped回调，使用默认实现
    onReportHoursTapped ??= () {
      showReportCorrectionDialog(Get.context!, place);
    };



    return GestureDetector(
      onTap: () {
        // Show place detail bottom sheet
        _showPlaceDetailBottomSheet(place);
      },
      child: Stack(
      alignment: Alignment.topCenter,
      children: [
        CommonBar(
          shape: BoxShape.rectangle,
          color: Colors.white,
          width: theme.layout.geoPlaceInfoCardWidth,
          height: theme.layout.geoPlaceInfoCardHeight,
          hasShadow: true,
          shadowColor: theme.colorExtension.eventPostTileShadow,
          shadowOffset: Offset(0, 8.w),
          shadowBlurRadius: 16.w,
          shadowSpreadRadius: -4.w,
        ),
        Container(
          padding: EdgeInsets.only(top: 8.w, left: 12.w, bottom: 8.w),
          margin: EdgeInsets.only(bottom: 4.w),
          child: Column(
            children: [
              // Image list
              SizedBox(
                height: theme.layout.geoPlaceInfoCardImageHeight,
                child: place.photos != null && place.photos!.isNotEmpty
                    ? SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: place.photos!.map((photo) {
                            //Google Map APi
                            final String imageUrl = photo.name != null
                                ? 'https://places.googleapis.com/v1/${photo.name}/media?maxHeightPx=600&maxWidthPx=800&key=${ApiConst.gmApiKey}'
                                : '';

                            return Row(
                              children: [
                                SizedBox(
                                  width:
                                      theme.layout.geoPlaceInfoCardImageWidth,
                                  child: imageUrl.isNotEmpty
                                      ? CachedNetworkImage(
                                          imageUrl: imageUrl,
                                          width: theme.layout
                                              .geoPlaceInfoCardImageWidth,
                                          height: theme.layout
                                              .geoPlaceInfoCardImageHeight,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Container(
                                            width: theme.layout
                                                .geoPlaceInfoCardImageWidth,
                                            height: theme.layout
                                                .geoPlaceInfoCardImageHeight,
                                            color: Colors.grey[100],
                                            child: Center(
                                              child: SizedBox(
                                                width: 20.w,
                                                height: 20.w,
                                                child:
                                                    CircularProgressIndicator(
                                                  strokeWidth: 2.0,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                          Color>(
                                                    theme.themeData.colorScheme
                                                        .primary,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          errorWidget: (context, url, error) =>
                                              Container(
                                            width: theme.layout
                                                .geoPlaceInfoCardImageWidth,
                                            height: theme.layout
                                                .geoPlaceInfoCardImageHeight,
                                            color: Colors.grey[200],
                                            child: CommonImageAsset(
                                              path:
                                                  '<EMAIL>',
                                              width: theme.layout
                                                  .geoPlaceInfoCardImageWidth,
                                              height: theme.layout
                                                  .geoPlaceInfoCardImageHeight,
                                            ),
                                          ),
                                          // 缓存配置
                                          cacheKey: photo.name,
                                          memCacheWidth: (theme.layout
                                                      .geoPlaceInfoCardImageWidth *
                                                  2)
                                              .toInt(),
                                          memCacheHeight: (theme.layout
                                                      .geoPlaceInfoCardImageHeight *
                                                  2)
                                              .toInt(),
                                        )
                                      : CommonImageAsset(
                                          path:
                                              '<EMAIL>',
                                          width: theme.layout
                                              .geoPlaceInfoCardImageWidth,
                                          height: theme.layout
                                              .geoPlaceInfoCardImageHeight,
                                        ),
                                ),
                                SizedBox(width: 5.w),
                              ],
                            );
                          }).toList(),
                        ),
                      )
                    : SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            SizedBox(
                              width: theme.layout.geoPlaceInfoCardImageWidth,
                              child: CommonImageAsset(
                                path: '<EMAIL>',
                                width: theme.layout.geoPlaceInfoCardImageWidth,
                                height:
                                    theme.layout.geoPlaceInfoCardImageHeight,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
              SizedBox(
                height: 0.5.w,
              ),
              // Info row
              Row(
                children: [
                  // Text panel
                  SizedBox(
                    width: theme.layout.geoPlaceInfoCardTextPanelWidth,
                    height: theme.layout.geoPlaceInfoCardTextPanelHeight,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // title
                        SizedBox(
                          height: theme.layout.geoPlaceInfoCardTitleHeight,
                          child: CommonText(
                            place.displayName?.text ?? 'place.name',
                            theme.textStyleExtension.geoPlaceInfoCardTitle,
                          ),
                        ),
                        SizedBox(height: 1.w),
                        // mileage & rating
                        SizedBox(
                            height: theme.layout.geoPlaceInfoCardGeoInfoHeight,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                // location icon
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    SizedBox(
                                      width: 14.w,
                                      height: 14.w,
                                    ),
                                    CommonImageAsset(
                                      path: ImageAssetConst
                                          .mapPlaceInfoCardLocation,
                                      width: 9.3.w,
                                      height: 11.3.w,
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                CommonText(
                                  '1.3 KM', // todo place.distance
                                  theme.textStyleExtension
                                      .geoPlaceInfoCardMileage,
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                CommonText(
                                  '•',
                                  theme
                                      .textStyleExtension.geoPlaceInfoCardTitle,
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                // star icon
                                Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    SizedBox(
                                      width: 14.w,
                                      height: 14.w,
                                    ),
                                    CommonImageAsset(
                                      path:
                                          ImageAssetConst.mapPlaceInfoCardStar,
                                      width: 10.w,
                                      height: 9.5.w,
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  width: 5.w,
                                ),
                                CommonText(
                                  place.rating?.toString() ?? '-',
                                  theme.textStyleExtension
                                      .geoPlaceInfoCardMileage,
                                ),
                              ],
                            )),
                        SizedBox(height: 1.w),
                        // Business hours section
                        Flexible(
                          child: _buildBusinessHoursSection(
                              theme, place, onReportHoursTapped),
                        ),
                        // Tags
                        if (place.tags != null && place.tags!.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.only(top: 1.w),
                            child: SizedBox(
                              height: theme.layout.geoPlaceInfoCardStatusHeight,
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: List.generate(
                                    place.tags!.length,
                                    (i) => CommonText(
                                      '${place.tags![i]} ',
                                      theme.textStyleExtension
                                          .geoPlaceInfoCardTag,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        // 新增：Check in按钮和爱心icon
                        Padding(
                          padding: EdgeInsets.only(top: 6.w),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 100.w,
                                height: 35.w,
                                child: ElevatedButton(
                                  onPressed: () {
                                    // 显示宠物选择弹窗
                                    PetSelectionBottomSheetHelper.show(
                                      onAppointmentMade: () {
                                        // TODO: 处理预约成功后的逻辑
                                        print('Appointment made successfully');
                                      },
                                    );
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Color(0xFFF2D3A4),
                                    foregroundColor: Colors.white,
                                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 0),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10.w),
                                    ),
                                    elevation: 0,
                                  ),
                                  child: Text(
                                    'Check in',
                                    style: TextStyle(
                                      fontFamily: 'Manrope',
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Container(
                                width: 35.w,
                                height: 35.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(17.5.w),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    Icons.favorite_border,
                                    color: Color(0xFFF2D3A4),
                                    size: 28.w,
                                  ),
                                  onPressed: () {
                                    // TODO: 收藏功能
                                  },
                                  splashRadius: 20.w,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Check in button
                  Container(
                    alignment: Alignment.bottomCenter,
                    width: theme.layout.geoPlaceInfoCardCheckInButtonWidth,
                    // child: todo CommonButton3(),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  // Favor button
                  Container(
                    alignment: Alignment.bottomCenter,
                    width: theme.layout.geoPlaceInfoCardFavorButtonSize,
                    // child: todo CommonButton3(),
                  )
                ],
              )
            ],
          ),
        ),
      ],
    ),
    );
  }

  /// Business hours section
  static Widget _buildBusinessHoursSection(
      ThemePlugin theme, GeoPlace place, VoidCallback? onReportHoursTapped) {
    final openingHours = place.regularOpeningHours;

    // 如果没有营业时间信息，显示汇报按钮
    if (openingHours == null ||
        (openingHours.weekdayDescriptions == null &&
            openingHours.periods == null)) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: CommonText(
              'geo.business.hours.unknown'.t18,
              theme.textStyleExtension.geoPlaceInfoCardBusinessStatus,
            ),
          ),
          SizedBox(width: 4.w),
          _buildReportButton(theme, onReportHoursTapped, isPrimary: true),
        ],
      );
    }

    // business hours
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: _buildBusinessStatusLine(theme, openingHours),
            ),
            SizedBox(width: 1.w),
            _buildReportButton(theme, onReportHoursTapped, isPrimary: false),
          ],
        ),
        // Today's business hour - only show if there's space
        // if (openingHours.weekdayDescriptions != null &&
        //     openingHours.weekdayDescriptions!.isNotEmpty)
        //   Flexible(
        //     child: Padding(
        //       padding: EdgeInsets.only(top: 1.w),
        //       child: _buildTodayHoursChip(
        //           theme, openingHours.weekdayDescriptions!),
        //     ),
        //   ),
      ],
    );
  }

  /// Business hours status
  static Widget _buildBusinessStatusLine(
      ThemePlugin theme, GeoOpeningHours openingHours) {
    final statusText = openingHours.openNow == null
        ? 'geo.business.status.unknown'.t18
        : openingHours.openNow!
            ? 'geo.business.status.open'.t18
            : 'geo.business.status.closed'.t18;

    final statusColor = openingHours.openNow == null
        ? theme.themeData.colorScheme.outline
        : openingHours.openNow!
            ? Colors.green
            : Colors.red;

    List<Widget> statusComponents = [
      CommonText(
        statusText,
        theme.textStyleExtension.geoPlaceInfoCardBusinessStatus.copyWith(
          color: statusColor,
          fontWeight: FontWeight.w600,
          fontSize: 10.sp,
        ),
      ),
    ];

    if (openingHours.nextOpenTime != null ||
        openingHours.nextCloseTime != null) {
      statusComponents.addAll([
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          child: Container(
            width: 2.w,
            height: 2.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.themeData.colorScheme.outline.withOpacity(0.6),
            ),
          ),
        ),
        Flexible(
          child: CommonText(
            openingHours.openNow ?? false
                ? '${'geo.business.closes'.t18} ${_formatTime(openingHours.nextCloseTime)}'
                : '${'geo.business.opens'.t18} ${_formatTime(openingHours.nextOpenTime)}',
            theme.textStyleExtension.geoPlaceInfoCardBusinessTime.copyWith(
              color: theme.themeData.colorScheme.onSurface.withOpacity(0.8),
              fontSize: 9.sp,
            ),
          ),
        ),
      ]);
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: statusComponents,
    );
  }

  static Widget _buildTodayHoursChip(
      ThemePlugin theme, List<String> weekdayDescriptions) {
    final todayHours = _formatTodayHours(weekdayDescriptions);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 0.5.w),
      decoration: BoxDecoration(
        color: theme.themeData.colorScheme.surfaceContainerHighest
            .withOpacity(0.3),
        borderRadius: BorderRadius.circular(8.w),
        border: Border.all(
          color: theme.themeData.colorScheme.outline.withOpacity(0.2),
          width: 0.5.w,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.schedule,
            size: 9.w,
            color: theme.themeData.colorScheme.primary,
          ),
          SizedBox(width: 2.w),
          Flexible(
            child: CommonText(
              todayHours,
              theme.textStyleExtension.geoPlaceInfoCardMileage.copyWith(
                color: theme.themeData.colorScheme.primary,
                fontWeight: FontWeight.w500,
                fontSize: 9.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Report the business hours
  static Widget _buildReportButton(
      ThemePlugin theme, VoidCallback? onReportHoursTapped,
      {required bool isPrimary}) {
    return GestureDetector(
      onTap: onReportHoursTapped,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.w),
        decoration: BoxDecoration(
          color: isPrimary
              ? theme.themeData.colorScheme.primary.withOpacity(0.1)
              : theme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.circular(4.w),
          border: Border.all(
            color: isPrimary
                ? theme.themeData.colorScheme.primary
                : theme.themeData.colorScheme.outline.withOpacity(0.6),
            width: 0.8.w,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isPrimary ? Icons.add_circle_outline : Icons.edit_outlined,
              size: 7.w,
              color: isPrimary
                  ? theme.themeData.colorScheme.primary
                  : theme.themeData.colorScheme.outline,
            ),
            SizedBox(width: 1.w),
            CommonText(
              isPrimary ? 'geo.report.hours'.t18 : 'geo.report.correction'.t18,
              theme.textStyleExtension.geoPlaceInfoCardBusinessTime.copyWith(
                color: isPrimary
                    ? theme.themeData.colorScheme.primary
                    : theme.themeData.colorScheme.outline,
                fontSize: 7.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示汇报更正表单
  static void showReportCorrectionDialog(BuildContext context, GeoPlace place) {
    Get.bottomSheet(
      _buildReportCorrectionSheet(context, place),
      isScrollControlled: true,
    );
  }

  /// 构建汇报更正底部表单
  static Widget _buildReportCorrectionSheet(
      BuildContext context, GeoPlace place) {
    final theme = Get.find<ThemePlugin>();
    final currentStatus = Rx<String?>(null);
    final notesController = TextEditingController();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.w)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                    color:
                        theme.themeData.colorScheme.outline.withOpacity(0.2)),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonText(
                  'geo.report.correction.title'.t18,
                  theme.textStyleExtension.geoPlaceInfoCardTitle,
                ),
                GestureDetector(
                  onTap: () => Get.back(),
                  child: Icon(
                    Icons.close,
                    color: theme.themeData.colorScheme.outline,
                    size: 24.w,
                  ),
                ),
              ],
            ),
          ),

          // 内容
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 地点信息
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: theme.themeData.colorScheme.surface,
                    borderRadius: BorderRadius.circular(8.w),
                    border: Border.all(
                        color: theme.themeData.colorScheme.outline
                            .withOpacity(0.2)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: theme.themeData.colorScheme.primary,
                        size: 20.w,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonText(
                              place.displayName?.text ??
                                  'geo.place.unknown'.t18,
                              theme.textStyleExtension.geoPlaceInfoCardTitle,
                            ),
                            if (place.formattedAddress != null)
                              CommonText(
                                place.formattedAddress!,
                                theme
                                    .textStyleExtension.geoPlaceInfoCardMileage,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20.w),

                // 当前状态报告
                CommonText(
                  'geo.report.current.status'.t18,
                  theme.textStyleExtension.geoPlaceInfoCardTitle,
                ),
                SizedBox(height: 12.w),

                // 状态选择
                Obx(() => Column(
                      children: [
                        _buildStatusOption(
                          theme,
                          'open',
                          'geo.business.status.open'.t18,
                          Icons.check_circle,
                          Colors.green,
                          currentStatus.value == 'open',
                          () => currentStatus.value = 'open',
                        ),
                        SizedBox(height: 8.w),
                        _buildStatusOption(
                          theme,
                          'closed',
                          'geo.business.status.closed'.t18,
                          Icons.cancel,
                          Colors.red,
                          currentStatus.value == 'closed',
                          () => currentStatus.value = 'closed',
                        ),
                      ],
                    )),

                SizedBox(height: 20.w),

                // 备注
                CommonText(
                  'geo.report.notes'.t18,
                  theme.textStyleExtension.geoPlaceInfoCardTitle,
                ),
                SizedBox(height: 8.w),
                TextField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'geo.report.notes.hint'.t18,
                    hintStyle: theme.textStyleExtension.geoPlaceInfoCardMileage,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.w),
                      borderSide: BorderSide(
                        color: theme.themeData.colorScheme.outline
                            .withOpacity(0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.w),
                      borderSide: BorderSide(
                        color: theme.themeData.colorScheme.primary,
                      ),
                    ),
                  ),
                ),

                SizedBox(height: 30.w),

                // Submit button for report business hours
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (currentStatus.value != null) {
                        // Submit report
                        _submitCorrection(
                            place, currentStatus.value!, notesController.text);
                        Get.back();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.themeData.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.w),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.w),
                      ),
                    ),
                    child: CommonText(
                      'geo.report.submit'.t18,
                      theme.textStyleExtension.geoPlaceInfoCardTitle.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build status option
  static Widget _buildStatusOption(
    ThemePlugin theme,
    String statusValue,
    String title,
    IconData icon,
    Color color,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.themeData.colorScheme.primary.withOpacity(0.1)
              : theme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            color: isSelected
                ? theme.themeData.colorScheme.primary
                : theme.themeData.colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2.w : 1.w,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? theme.themeData.colorScheme.primary : color,
              size: 24.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: CommonText(
                title,
                theme.textStyleExtension.geoPlaceInfoCardBusinessStatus
                    .copyWith(
                  color: isSelected
                      ? theme.themeData.colorScheme.primary
                      : theme.themeData.colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: theme.themeData.colorScheme.primary,
                size: 20.w,
              ),
          ],
        ),
      ),
    );
  }

  /// Submit correction report
  static void _submitCorrection(GeoPlace place, String status, String notes) {
    // Here we can add the logic to submit to the server
    print('提交报告：地点=${place.displayName?.text}, 状态=$status, 备注=$notes');

    // Show submit success toast
    ToastUtil.showToast('geo.report.submitted'.t18);
  }

  /// HH:MM
  static String _formatTime(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return 'geo.business.time.unknown'.t18;
    }

    if (RegExp(r'^\d{2}:\d{2}$').hasMatch(timeString)) {
      return timeString;
    }

    try {
      if (timeString.contains(':')) {
        final timeMatch = RegExp(r'(\d{1,2}):(\d{2})').firstMatch(timeString);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
        }
      }

      if (RegExp(r'^\d{3,4}$').hasMatch(timeString)) {
        final timeInt = int.parse(timeString);
        final hour = timeInt ~/ 100;
        final minute = timeInt % 100;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      }

      return timeString;
    } catch (e) {
      return timeString;
    }
  }

  /// format Hours
  static String _formatTodayHours(List<String> weekdayDescriptions) {
    final today = DateTime.now().weekday;
    final todayIndex = today == 7 ? 0 : today; // Sunday is 0, Monday is 1

    if (todayIndex < weekdayDescriptions.length) {
      final todayHours = weekdayDescriptions[todayIndex];
      // Remove the weekday prefix, only keep the time
      String timeOnly = todayHours.replaceFirst(RegExp(r'^[^\s]+:\s*'), '');

      // Format the time range
      final timeRangeMatch =
          RegExp(r'(\d{1,2}):(\d{2})\s*[–-]\s*(\d{1,2}):(\d{2})')
              .firstMatch(timeOnly);
      if (timeRangeMatch != null) {
        final startHour = int.parse(timeRangeMatch.group(1)!);
        final startMinute = int.parse(timeRangeMatch.group(2)!);
        final endHour = int.parse(timeRangeMatch.group(3)!);
        final endMinute = int.parse(timeRangeMatch.group(4)!);

        return '${startHour.toString().padLeft(2, '0')}:${startMinute.toString().padLeft(2, '0')} - ${endHour.toString().padLeft(2, '0')}:${endMinute.toString().padLeft(2, '0')}';
      }

      return timeOnly;
    }

    return 'geo.business.hours.unavailable'.t18;
  }

  static Widget buildSearchTextButton(ThemePlugin theme, BuildContext context,
      {AsyncVoidCallback? onPressed}) {
    // Background
    CommonBar bg = CommonBar(
      color: theme.themeData.colorScheme.surface,
      width: theme.layout.geoSearchTextButtonBgSize,
      height: theme.layout.geoSearchTextButtonBgSize,
      circular: theme.layout.authBackButtonBgCircular,
      hasShadow: true,
      shadowColor: theme.colorExtension.authBackButtonShadow,
      shadowOffset: theme.layout.authBackButtonBgShadowOffset,
      shadowBlurRadius: theme.layout.authBackButtonBgShadowBlur,
    );

    // Icon
    CommonImageAsset icon = CommonImageAsset(
      path: ImageAssetConst.mapSearchTextIcon,
      color: theme.themeData.colorScheme.primary,
      width: theme.layout.geoSearchTextButtonIconSize,
      height: theme.layout.geoSearchTextButtonIconSize,
    );

    // Button
    return GestureDetector(
      onTap: () async {
        FocusScope.of(context).unfocus();
        // To ensure the keyboard is closed
        await Future.delayed(Duration(milliseconds: 200));
        if (onPressed != null) {
          await onPressed();
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          bg,
          icon,
        ],
      ),
    );
  }

  static CommonTextField3Controller buildSearchTextFieldController(
      ThemePlugin theme, Rx<TextEditingController>? textEditingController,
      {Rx<GlobalKey>? globalKey,
      Rx<AsyncCallbackWithContext?>? onCleared,
      Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.text.obs,
      width: theme.layout.geoSearchTextFieldWidth.obs,
      height: theme.layout.geoSearchTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoSearchTextFieldCircular.obs,
      clearIconColor: theme.themeData.colorScheme.secondary.obs,
      iconColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: 0.0.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,
      hintText: 'geo.search.hint'.t18.obs,
      hintStyle: theme.textStyleExtension.geoSearchHintText.obs,
      textStyle: theme.textStyleExtension.geoSearchInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.geoSearchInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.geoSearchHintText.obs,
      textPaddingHorizontal:
          theme.layout.geoSearchTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.geoSearchTextFieldPaddingVertical.obs,
      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  // TODO:  maybe we can store the image to the firebase storage
  static void preloadPlaceImages(GeoPlace place) {
    if (place.photos != null && place.photos!.isNotEmpty) {
      for (var photo in place.photos!) {
        if (photo.name != null) {
          final String imageUrl =
              'https://places.googleapis.com/v1/${photo.name}/media?maxHeightPx=600&maxWidthPx=800&key=${ApiConst.gmApiKey}';
          //
          CachedNetworkImageProvider(imageUrl).resolve(ImageConfiguration());
        }
      }
    }
  }

  /// Show place detail bottom sheet
  static void _showPlaceDetailBottomSheet(GeoPlace place) {


    Get.bottomSheet(
      isDismissible: true,
      Container(
        width: 392.w,
        height: 640.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
        ),
        child: _buildPlaceDetailContent(place, null),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }

  /// Build place detail content
  static Widget _buildPlaceDetailContent(GeoPlace place, ScrollController? scrollController) {
    final theme = Get.find<ThemePlugin>();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Stack(
        children: [
          // Close button (top right)
          Positioned(
            top: 16.w,
            right: 16.w,
            child: IconButton(
              icon: Icon(
                Icons.close,
                color: theme.themeData.colorScheme.onSurface,
                size: 20.w,
              ),
              onPressed: () => Get.back(),
            ),
          ),
          // Place section
          Positioned(
            top: 20.w,
            left: 20.w,
            child: _buildPlaceSection(place, theme),
          ),
          // Image section
          Positioned(
            top: 130.w, // Place section height (90) + margin (40)
            left: 20.w,
            right: 20.w,
            child: _buildImageSection(place, theme),
          ),
          // Address info section
          Positioned(
            top: 325.w, // Image section (175) + Place section (90) + margins (60)
            left: 20.w,
            right: 20.w,
            child: _buildAddressInfoSection(place, theme),
          ),
        ],
      ),
    );
  }







  /// Build place section (184*90)
  static Widget _buildPlaceSection(GeoPlace place, ThemePlugin theme) {
    return SizedBox(
      width: 392.w,
      height: 90.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // (1) 名称
          CommonText(
            place.displayName?.text ?? place.name ?? 'place.unknown'.t18,
            TextStyle(
              color: Color(0xFF262626),
              fontFamily: 'Manrope',
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
          ),
          SizedBox(height: 4.w),

          // (2) 距离和评分星级
          Row(
            children: [
              // 距离 (假设1.3km，实际需要计算)
              Icon(
                Icons.location_on,
                size: 12.w,
                color: Color(0xFF666666),
              ),
              SizedBox(width: 2.w),
              CommonText(
                '1.3 km',
                TextStyle(
                  color: Color(0xFF666666),
                  fontSize: 12.sp,
                ),
              ),
              SizedBox(width: 8.w),

              // 评分星级
              if (place.rating != null) ...[
                Icon(
                  Icons.star,
                  size: 12.w,
                  color: Colors.amber,
                ),
                SizedBox(width: 2.w),
                CommonText(
                  '${place.rating}',
                  TextStyle(
                    color: Color(0xFF666666),
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 4.w),

          // (3) 当前是否开业，营业时间
          _buildBusinessStatus(place, theme),
          SizedBox(height: 4.w),

          // (4) type（待定，可以留空）
          if (place.types != null && place.types!.isNotEmpty)
            CommonText(
              place.types!.first.replaceAll('_', ' '),
              TextStyle(
                color: Color(0xFF999999),
                fontSize: 11.sp,
              ),
            ),
        ],
      ),
    );
  }

  /// Build business status for place section
  static Widget _buildBusinessStatus(GeoPlace place, ThemePlugin theme) {
    final openingHours = place.regularOpeningHours;

    if (openingHours == null) {
      return CommonText(
        'place.hours.unknown'.t18,
        TextStyle(
          color: Color(0xFF666666),
          fontSize: 12.sp,
        ),
      );
    }

    // 简化的营业状态显示
    return CommonText(
      'Open - Closes 10pm', // 这里需要根据实际营业时间计算
      TextStyle(
        color: Color(0xFF00AA00), // 绿色表示营业中
        fontSize: 12.sp,
      ),
    );
  }

  /// Build image section with horizontal scrollable large images
  static Widget _buildImageSection(GeoPlace place, ThemePlugin theme) {
    if (place.photos == null || place.photos!.isEmpty) {
      return Container(
        height: 175.w,
        decoration: BoxDecoration(
          color: theme.themeData.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.w),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported,
                size: 40.w,
                color: theme.themeData.colorScheme.onSurfaceVariant,
              ),
              SizedBox(height: 8.w),
              CommonText(
                'No images available',
                TextStyle(
                  color: theme.themeData.colorScheme.onSurfaceVariant,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return _ImageGallery(photos: place.photos!, theme: theme);
  }

  /// Build address info section with address, hours, parking, discounts, and events
  static Widget _buildAddressInfoSection(GeoPlace place, ThemePlugin theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Address
        if (place.formattedAddress != null && place.formattedAddress!.isNotEmpty)
          _buildAddressRow(place.formattedAddress!, theme),

        SizedBox(height: 12.w),

        // Business hours
        _buildBusinessHoursRow(place, theme),

        SizedBox(height: 12.w),

        // Parking info
        _buildParkingRow(place, theme),

        SizedBox(height: 16.w),

        // Discount section
        _buildDiscountSection(place, theme),

        SizedBox(height: 16.w),

        // Events section
        _buildEventsSection(place, theme),

        SizedBox(height: 20.w),

        // Check in button
        _buildCheckInButton(theme),
      ],
    );
  }

  /// Build address row
  static Widget _buildAddressRow(String address, ThemePlugin theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.location_on_outlined,
          size: 16.w,
          color: theme.themeData.colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: CommonText(
            address,
            TextStyle(
              color: theme.themeData.colorScheme.onSurfaceVariant,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  /// Build business hours row with dropdown
  static Widget _buildBusinessHoursRow(GeoPlace place, ThemePlugin theme) {
    // Get current day info
    final now = DateTime.now();
    final currentDayName = _getCurrentDayName(now.weekday);
    final currentHours = _getCurrentDayHoursFromDescriptions(place, now.weekday);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(
          Icons.access_time_outlined,
          size: 16.w,
          color: theme.themeData.colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 8.w),
        // Day name
        CommonText(
          currentDayName,
          TextStyle(
            color: theme.themeData.colorScheme.onSurfaceVariant,
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(width: 8.w), // Equal spacing
        // Business hours
        if (currentHours.isNotEmpty)
          CommonText(
            currentHours,
            TextStyle(
              color: theme.themeData.colorScheme.onSurfaceVariant,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        SizedBox(width: 8.w), // Equal spacing
        // Dropdown button
        GestureDetector(
          onTap: () => _showBusinessHoursDialog(place, theme),
          child: Icon(
            Icons.keyboard_arrow_down,
            size: 20.w,
            color: theme.themeData.colorScheme.onSurfaceVariant,
          ),
        ),
        // Spacer to push everything to the left
        Expanded(child: Container()),
      ],
    );
  }

  /// Build parking row
  static Widget _buildParkingRow(GeoPlace place, ThemePlugin theme) {
    String parkingText = _getParkingInfo(place);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(
          Icons.local_parking_outlined,
          size: 16.w,
          color: theme.themeData.colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: CommonText(
            parkingText,
            TextStyle(
              color: theme.themeData.colorScheme.onSurfaceVariant,
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  /// Get parking information from place types or default
  static String _getParkingInfo(GeoPlace place) {
    if (place.types != null) {
      // Check for parking-related types
      for (String type in place.types!) {
        if (type.toLowerCase().contains('parking')) {
          return 'Parking available';
        }
      }

      // Check for place types that typically have parking
      for (String type in place.types!) {
        if (type.toLowerCase().contains('park') ||
            type.toLowerCase().contains('store') ||
            type.toLowerCase().contains('hospital') ||
            type.toLowerCase().contains('shopping')) {
          return 'Customer parking';
        }
      }
    }

    // Default for most places
    return 'Parking available';
  }

  /// Build discount section
  static Widget _buildDiscountSection(GeoPlace place, ThemePlugin theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'Limited-time discounts',
          TextStyle(
            color: Color(0xFFE91E63), // Pink color
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 4.w),
        Text(
          'Get up to 20% off on selected pet food and grooming services. Offer ends May 16 at 11:59 PM - shop now!',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          maxLines: null, // Allow multiple lines
          overflow: TextOverflow.visible,
        ),
      ],
    );
  }

  /// Build events section
  static Widget _buildEventsSection(GeoPlace place, ThemePlugin theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'Current events',
          TextStyle(
            color: Color(0xFF9C27B0), // Purple color
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 4.w),
        Text(
          'Live Event Ongoing - Join our Pet Play Day happening here today! 10:00 AM - 4:00 PM Fun games, free treats, and live demos.',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 12.sp,
            fontWeight: FontWeight.w400,
          ),
          maxLines: null, // Allow multiple lines
          overflow: TextOverflow.visible,
        ),
      ],
    );
  }

  /// Build check in button
  static Widget _buildCheckInButton(ThemePlugin theme) {
    return Center(
      child: SizedBox(
        width: 185.w,
        height: 35.w,
        child: ElevatedButton(
          onPressed: () {
            // 显示宠物选择弹窗
            PetSelectionBottomSheetHelper.show(
              onAppointmentMade: () {
                // TODO: 处理预约成功后的逻辑
                print('Appointment made successfully');
              },
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(0xFFF2D3A4), // Background color
            foregroundColor: Colors.white, // Text color
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.w),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.w),
            ),
            elevation: 0,
          ),
          child: Text(
            'Check in',
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  /// Get current day name
  static String _getCurrentDayName(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }

  /// Get current day hours from weekdayDescriptions (more reliable)
  static String _getCurrentDayHoursFromDescriptions(GeoPlace place, int weekday) {
    final openingHours = place.regularOpeningHours;
    if (openingHours?.weekdayDescriptions == null || openingHours!.weekdayDescriptions!.isEmpty) {
      return '';
    }

    // weekdayDescriptions: Sunday is 0, Monday is 1, etc.
    final todayIndex = weekday == 7 ? 0 : weekday; // Convert to Google's format

    if (todayIndex < openingHours.weekdayDescriptions!.length) {
      final todayHours = openingHours.weekdayDescriptions![todayIndex];
      // Remove the weekday prefix, only keep the time
      String timeOnly = todayHours.replaceFirst(RegExp(r'^[^\s]+:\s*'), '');
      return timeOnly;
    }

    return '';
  }

  /// Get current day hours from periods (fallback)
  static String _getCurrentDayHours(GeoPlace place, int weekday) {
    if (place.regularOpeningHours?.periods == null) return '';

    final periods = place.regularOpeningHours!.periods!;
    for (final period in periods) {
      if (period.open?.day == weekday - 1) {
        final openHour = period.open?.hour ?? 0;
        final openMinute = period.open?.minute ?? 0;
        final closeHour = period.close?.hour ?? 0;
        final closeMinute = period.close?.minute ?? 0;

        final openTime = _formatTimeFromHourMinute(openHour, openMinute);
        final closeTime = _formatTimeFromHourMinute(closeHour, closeMinute);

        return '$openTime - $closeTime';
      }
    }

    return '';
  }

  /// Format time from hour and minute to HH:MM am/pm
  static String _formatTimeFromHourMinute(int hour, int minute) {
    final minuteStr = minute.toString().padLeft(2, '0');

    if (hour == 0) {
      return '12:$minuteStr am';
    } else if (hour < 12) {
      return '$hour:$minuteStr am';
    } else if (hour == 12) {
      return '12:$minuteStr pm';
    } else {
      return '${hour - 12}:$minuteStr pm';
    }
  }

  /// Show business hours dialog
  static void _showBusinessHoursDialog(GeoPlace place, ThemePlugin theme) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.w),
        ),
        child: Container(
          padding: EdgeInsets.all(20.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                'Business Hours',
                TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: theme.themeData.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 16.w),
              ..._buildWeeklyHours(place, theme),
              SizedBox(height: 16.w),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Get.back(),
                  child: CommonText(
                    'Close',
                    TextStyle(
                      color: theme.themeData.colorScheme.primary,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build weekly hours list
  static List<Widget> _buildWeeklyHours(GeoPlace place, ThemePlugin theme) {
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    final List<Widget> hoursList = [];

    final openingHours = place.regularOpeningHours;

    // Use weekdayDescriptions if available (more reliable)
    if (openingHours?.weekdayDescriptions != null && openingHours!.weekdayDescriptions!.isNotEmpty) {
      final descriptions = openingHours.weekdayDescriptions!;

      for (int i = 0; i < 7; i++) {
        // Google's weekdayDescriptions: Sunday=0, Monday=1, etc.
        // Our dayNames: Monday=0, Tuesday=1, etc.
        final googleIndex = i == 6 ? 0 : i + 1; // Convert Monday=0 to Google's Monday=1, Sunday=6 to Google's Sunday=0

        String dayHours = '';
        if (googleIndex < descriptions.length) {
          final fullDescription = descriptions[googleIndex];
          // Remove the weekday prefix, only keep the time
          dayHours = fullDescription.replaceFirst(RegExp(r'^[^\s]+:\s*'), '');
        }

        hoursList.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonText(
                  dayNames[i],
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: theme.themeData.colorScheme.onSurface,
                  ),
                ),
                CommonText(
                  dayHours.isEmpty ? 'Closed' : dayHours,
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: theme.themeData.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    } else {
      // Fallback to periods if weekdayDescriptions not available
      for (int i = 0; i < 7; i++) {
        final dayHours = _getCurrentDayHours(place, i + 1);
        hoursList.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonText(
                  dayNames[i],
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: theme.themeData.colorScheme.onSurface,
                  ),
                ),
                CommonText(
                  dayHours.isEmpty ? 'Closed' : dayHours,
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: theme.themeData.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return hoursList;
  }


}

/// Image gallery widget with page indicator
class _ImageGallery extends StatefulWidget {
  final List<GeoPhoto> photos;
  final ThemePlugin theme;

  const _ImageGallery({
    required this.photos,
    required this.theme,
  });

  @override
  State<_ImageGallery> createState() => _ImageGalleryState();
}

class _ImageGalleryState extends State<_ImageGallery> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          height: 175.w,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.photos.length,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.only(right: 8.w),
                child: _buildLargeImage(widget.photos[index]),
              );
            },
          ),
        ),
        // Page indicator
        if (widget.photos.length > 1)
          Positioned(
            bottom: 12.w,
            right: 12.w,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.w),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(12.w),
              ),
              child: CommonText(
                '${_currentPage + 1}/${widget.photos.length}',
                TextStyle(
                  color: Colors.white,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Build large image
  Widget _buildLargeImage(GeoPhoto photo) {
    final imageUrl = photo.name != null
        ? 'https://places.googleapis.com/v1/${photo.name}/media?maxHeightPx=350&maxWidthPx=640&key=${ApiConst.gmApiKey}'
        : '';

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.w),
      ),
      clipBehavior: Clip.hardEdge,
      child: imageUrl.isNotEmpty
          ? CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: widget.theme.themeData.colorScheme.surfaceContainerHighest,
                child: Center(
                  child: CircularProgressIndicator(
                    color: widget.theme.themeData.colorScheme.primary,
                    strokeWidth: 2.w,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: widget.theme.themeData.colorScheme.surfaceContainerHighest,
                child: Center(
                  child: Icon(
                    Icons.broken_image,
                    size: 30.w,
                    color: widget.theme.themeData.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            )
          : Container(
              color: widget.theme.themeData.colorScheme.surfaceContainerHighest,
              child: Center(
                child: Icon(
                  Icons.image,
                  size: 30.w,
                  color: widget.theme.themeData.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
    );
  }
}

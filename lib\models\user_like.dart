import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/geo_i.dart';
import 'base_full_model.dart';

part 'user_like.g.dart';

@JsonSerializable()
class Like extends BaseFullModel {

  String uid;
  String otherId;
  String postId;

  Like({
    required this.uid,
    required this.otherId,
    required this.postId,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Like.fromJson(Map<String, dynamic> json) => _$LikeFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$LikeToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Like.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Like.fromJson(jsonData);
  }

  factory Like.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Like.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'like';

  static create ({
    required String uid,
    required String otherId,
    required String postId,
  }) {

    return Like(
      sid: uuid.v4(),
      uid: uid,
      otherId: otherId,
      postId: postId,
      isValid: true,
    );
  }

  static copyFrom(Like other) {

    return Like(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      otherId: other.otherId,
      postId: other.postId,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/profile/profile_widget_builder.dart';

import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingDeviceAlertPage extends StatefulWidget {
  const UserSettingDeviceAlertPage({super.key});

  @override
  State<UserSettingDeviceAlertPage> createState() => _UserSettingDeviceAlertPageState();
}

class _UserSettingDeviceAlertPageState extends State<UserSettingDeviceAlertPage> {
  late Future<UserSettingDeviceAlertPageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => UserSettingDeviceAlertPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingDeviceAlertPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
        final controller = snapshot.data!;
        return CommonPage(
          theme: controller.theme,
          backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProfileWidgetBuilder.buildUserSettingTopSection(
                theme: controller.theme,
                onBack: () => Get.back(),
                topic: 'Device Alerts',
              ),
              _buildMainSwitch(controller),
              Obx(() {
                if (!controller.deviceAlertsEnabled.value) return const SizedBox.shrink();
                return Column(children: _buildSubSwitches(controller));
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMainSwitch(UserSettingDeviceAlertPageController controller) {
    return Obx(() => SwitchListTile(
      title: Text("Turn on Device Alerts", style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text("Receive real-time updates about your device's status.",
          style: controller.theme.textStyleExtension.petAvatar),
      value: controller.deviceAlertsEnabled.value,
      onChanged: (val) => controller.deviceAlertsEnabled.value = val,
    ));
  }

  List<Widget> _buildSubSwitches(UserSettingDeviceAlertPageController controller) {
    return [
      _buildSubSwitch(controller, "Get Battery Alerts", "Be warned right away when dangerous weather is on the way.",
          controller.batteryAlertEnabled),
      _buildSubSwitch(controller, "Get Offline Notifications", "Find out instantly when your device goes offline.",
          controller.offlineAlertEnabled),
      _buildSubSwitch(controller, "Receive Hardware Error Alerts", "Get notified if there's a malfunction or system issue.",
          controller.hardwareErrorEnabled),
      _buildSubSwitch(controller, "Get Feeder Refill Reminders", "We’ll remind you when it’s time to refill your feeder.",
          controller.refillReminderEnabled),
    ];
  }

  Widget _buildSubSwitch(UserSettingDeviceAlertPageController controller, String title, String subtitle, RxBool toggleValue) {
    return Obx(() => SwitchListTile(
      title: Text(title, style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text(subtitle, style: controller.theme.textStyleExtension.petAvatar),
      value: toggleValue.value,
      onChanged: (val) => toggleValue.value = val,
    ));
  }
}

class UserSettingDeviceAlertPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  late ThemePlugin theme;

  final RxBool deviceAlertsEnabled = false.obs;
  final RxBool batteryAlertEnabled = false.obs;
  final RxBool offlineAlertEnabled = false.obs;
  final RxBool hardwareErrorEnabled = false.obs;
  final RxBool refillReminderEnabled = false.obs;

  Future<UserSettingDeviceAlertPageController> init() async {
    await PageInterceptor.pageAuthCheck();
    theme = await Get.putAsync(() => ThemePlugin().init());

    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
      theme,
      userId: _authService.userAccount.value!.sid!,
      avatar: userData?.avatar,
    );

    return this;
  }
}


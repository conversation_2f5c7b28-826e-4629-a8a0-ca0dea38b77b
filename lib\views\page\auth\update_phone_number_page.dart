import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';

class UpdatePhoneNumberPage extends StatefulWidget {
  const UpdatePhoneNumberPage({super.key});

  @override
  UpdatePhoneNumberPageState createState() => UpdatePhoneNumberPageState();
}

class UpdatePhoneNumberPageState extends State<UpdatePhoneNumberPage> {
  late Future<UpdatePhoneNumberPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UpdatePhoneNumberPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UpdatePhoneNumberPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;


            logger.i('Object ID: ${identityHashCode(controller.passwordInputController)}');

            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [
                      // Header
                      SizedBox(
                          height: controller.theme.layout.authAvatarHeaderLineHeight +
                              controller.theme.layout.authAvatarTitleMarginTop +
                              controller.theme.layout.authWhiteSectionCircular / 2
                      ),

                      // Label
                      SizedBox(
                        // height: 85.w,
                        child: Obx(()=> AuthWidgetBuilder.buildLabel(
                          controller.theme,
                          title: controller.pageLabelTitle.value,
                          desc: controller.pageLabelDesc.value)),
                      ),
                      SizedBox(height: 20.w,),

                      // Form
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [

                              // Phone input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPhone2(
                                  controller.theme, controller.usernameInputController,
                              )),
                              SizedBox(height: 20.w,),

                              // Verification code input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxVerificationCode2(
                                  controller.theme, controller.passwordInputController,
                              )),
                              SizedBox(height: 20.w,),

                              // Resend section
                              SizedBox(
                                height: 50.w,
                                child: Obx(()=> (controller.isVerificationCodeSent.isTrue && controller.isPhoneNumberValid.isTrue) ? controller.isReSendEnabled.value ?
                                // Resend enabled
                                GestureDetector(
                                  onTap: () async {
                                    await controller.sendVerificationCodeOfPhoneNumber();
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      CommonText(
                                          'auth.button.resend'.t18,
                                          controller.theme.textStyleExtension.authPageResend
                                      ),
                                      SizedBox(width: 38.w,)
                                    ],
                                  ),

                                ):
                                // Resend disabled
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Obx(()=> CommonText(
                                        'auth.button.resend.in.second'.t19({
                                          'second': '${controller.remainedTime.value.toInt()}'
                                        }),
                                        controller.theme.textStyleExtension.authPageResendDisabled
                                    )),
                                    SizedBox(width: 38.w,)
                                  ],
                                ):
                                SizedBox.shrink(),
                              )),

                              SizedBox(height: 220.w,),
                              // Expanded(child: SizedBox.shrink()),

                              // Send verification code
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.buttonController,
                                )),
                              SizedBox(height: 30.w,),

                            ],
                          ),
                        )
                      ),
                    ],
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
                ],
              ),
            );
          }
        });
  }
}

class UpdatePhoneNumberPageController extends GetxController {

  // Services
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;

  // Submit buttons
  late CommonButton3Controller buttonController;
  //
  // final CommonButtonController buttonController = Get.put(CommonButtonController(), tag: 'sign-up-submit');
  // final CommonButtonController alterButtonController = Get.put(CommonButtonController(), tag: 'sign-up-alter');

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  // var buttonText = ''.obs;
  // var onPressed = Rx<AsyncCallback?>(null);

  var isVerificationCodeSent = false.obs;
  var isVerificationCodeValid = false.obs;
  var verificationId = ''.obs;
  var verificationCode = ''.obs;
  var isPhoneNumberValid = false.obs;
  var isReSendEnabled = false.obs;
  var remainedTime = 30.obs;
  Timer? timer;

  final String validCode = '123';

  // Theme plugin
  late ThemePlugin theme;

  Future<UpdatePhoneNumberPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.update.phone.number'.t18,
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
        'auth.label.link.phone.number.desc'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildPhoneNumberTextFieldController(
        theme, usernameInputBox.obs,
        onCleared: onPhoneNumberCleared.obs,
        onChanged: isInputPhoneNumberValid.obs,
    );
    // Listen to the focus node
    // usernameInputController.textFieldFocusNode.addListener(() {
    //   usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
    // });

    // build password input controller
    passwordInputController = AuthWidgetBuilder.buildVerificationCodeTextFieldController(
      theme, passwordInputBox.obs,
      onCleared: onVerificationCodeCleared.obs,
      onChanged: isInputVerificationCodeValid.obs
    );
    // Listen to the focus node
    // passwordInputController.textFieldFocusNode.addListener(() {
    //   passwordInputController.isFocused.value = passwordInputController.textFieldFocusNode.hasFocus;
    // });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.send.code'.t18.obs,
      onPressed: sendVerificationCodeOfPhoneNumber.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
  }

  @override
  void dispose() {
    timer?.cancel();
    usernameInputController.textFieldFocusNode.dispose(); // ✅ Dispose FocusNode
    passwordInputController.textFieldFocusNode.dispose(); // ✅ Dispose FocusNode
    buttonController.isInProgress.value = false;
    super.dispose();
  }

  void startCountdown() {

    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainedTime.value == 0) {
        isReSendEnabled.value = true;
        timer.cancel();
      } else {
        remainedTime.value--;
      }
    });
  }

  bool isButtonEnabled() {

    // Click to verify code
    if (isVerificationCodeSent.value) {
      return isVerificationCodeValid.value;
    }
    // Click to send verification code
    else {
      return isPhoneNumberValid.value;
    }
  }

  Future<void> onPhoneNumberCleared(BuildContext context) async {
    await resetState();
  }

  Future<void> onVerificationCodeCleared(BuildContext context) async {

    passwordInputBox.text = '';
    isVerificationCodeValid.value = false;
    buttonController.isEnabled.value = false;
    // buttonController.onPressed.value = verifyOtp;

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputPhoneNumberValid({required BuildContext context, required bool result}) async {
    isPhoneNumberValid.value = result;
    usernameInputController.hasMistake.value = isPhoneNumberValid.isFalse && usernameInputBox.text != '';

    if (isPhoneNumberValid.isTrue) {
      buttonController.text = 'auth.button.send.code'.t18.obs;
      buttonController.onPressed = sendVerificationCodeOfPhoneNumber.obs;
      buttonController.isEnabled.value = true;
    } else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputVerificationCodeValid({required BuildContext context, required bool result}) async {
    isVerificationCodeValid.value = result;
    passwordInputController.hasMistake.value = isVerificationCodeValid.isFalse && passwordInputBox.text != '';

    if (isVerificationCodeValid.isTrue && isPhoneNumberValid.isTrue) {
      logger.d('show continue');
      buttonController.text = 'auth.button.verify.code'.t18.obs;
      buttonController.onPressed = verifyOtp.obs;
      buttonController.isEnabled.value = true;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationCodeOfPhoneNumber() async {

    if (!isPhoneNumberValid.value) return;

    String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';
    // String phoneNumber = InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text);
    // String phoneNumber = kReleaseMode ? InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text) : '+***********';

    // Check existing
    UserAccount? userAccount = await userDao.getUserAccountById(phoneNumber: phoneNumber);
    if (userAccount != null) {
      Get.snackbar('auth.error.title'.t18, 'auth.error.number.exists'.t18);
      return;
    }

    // Check existing
    ServiceResponse<String> response = await authService.sendVerificationCodePhoneNumber(phoneNumber);

    if (response.code == 201) {

      // update phone number done in the auth service
      await callbackSuccess();

      // Login success
      await resetState();
      Get.offAll(RootPage());
      return;
    }
    else if (response.code == 200) {

      verificationId.value = response.data!;
      isReSendEnabled.value = true;
      isVerificationCodeSent.value = true;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.sent'.t18,
        theme.textStyleExtension.authPageNotification,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.isEnabled.value = true;
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;

      buttonController.text.value = 'auth.button.verify.code'.t18;
      buttonController.isEnabled.value = false;
      buttonController.onPressed.value = verifyOtp;

      startCountdown();
    }
    else if (response.code == 408) {

      verificationId.value = response.data!;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.timeout'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
    else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.desc'.t18);

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
  }

  Future<void> verifyOtp() async {

    try {
      // Sign in the user with the sms code
      ServiceResponse<User> response = await authService.updatePhoneNumber(
          verificationId.value, passwordInputBox.text);

      if (response.code == 200) {

        await callbackSuccess();

        // TODO - Go to add pet page
        await resetState();
        Get.offAll(SelectPetPage());
        return;
      }
      else {

        pageLabelDesc.value = CommonText(
          'auth.error.code.invalid'.t18,
          theme.textStyleExtension.authPageWarning,
          width: 350.w,
          height: 50.w,
          maxLines: 2,
          softWrap: true,
        );
        //
        // passwordInputBox.text = '';
        // passwordInputController.isEnabled.value = true;
        // passwordInputController.obscureText.value = true;
        // passwordInputController.hasMistake.value = false;

        // isReSendEnabled.value = false;
        // buttonController.isEnabled.value = true;
        // isVerificationCodeSent.value = false;
        // onPressed.value = sendVerificationCodeOfPhoneNumber;
      }
    }
    catch (e) {
      isVerificationCodeValid.value = false;
    }
  }

  Future<void> callbackSuccess() async {

    // Update current user in auth service
    UserAccount? account =  await userService.getUserAccountById(fid: authService.currentUser.value!.uid);
    if (account != null) {
      account.phoneNumber = usernameInputBox.text;
      await userDao.updateUserAccount(account);
    }

    // add auth record in Firestore and security storage
    await userService.recordAuthActivity(authService.currentUser.value!, AuthChannel.sms);

  }

  Future<void> resetState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.phone'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;

    // Reset states
    isPhoneNumberValid.value = false;
    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    isVerificationCodeSent.value = false;
    isVerificationCodeValid.value = false;

    // Button
    buttonController.text = 'auth.button.send.code'.t18.obs;
    buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }
}

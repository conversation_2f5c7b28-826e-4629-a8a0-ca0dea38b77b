import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:onenata_app/common/const/const_i.dart';

import 'common/const/env.dart';
import 'common/type/global_var.dart';
import 'common/plugin/firebase/firebase_i.dart';
import 'common/plugin/logger/logger_i.dart';
import 'common/plugin/storage/storage_i.dart';
import 'common/plugin/i18n/i18n_i.dart';
import 'common/plugin/api/dio_client.dart';
import 'controller/weatherController.dart';
import 'services/services_i.dart';
import 'views/theme/layouts/theme_layout_i.dart';
import 'views/component/components_i.dart';
import 'views/page/page_i.dart';

import 'router.dart';
import 'views/page/app_locker_controller.dart';

// Temporarily handle the FCM here (required for handling messages in background)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  logger.i("📩 Handling a background message: ${message.messageId}");
}

void main() async {

  WidgetsFlutterBinding.ensureInitialized();

  // await Get.putAsync(() => GetStoragePlugin().init());
  MainController mainController = await Get.putAsync(() => MainController().init());

  // Localization
  preferenceLocale = await LocaleUtil.storedLocale();
  Locale deviceLocale = LocaleUtil.systemLocale();
  List<Locale> supportedLocales = await LocaleUtil.supportedLocales();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    // DeviceOrientation.portraitDown,
  ]).then((_) {
    runApp(EasyLocalization(
      supportedLocales: supportedLocales,
      path: 'assets/translations',
      // <-- change the path of the translation files
      // startLocale: preferenceLocale,
      startLocale: supportedLocales.first, // TODO: change to preferenceLocale when translation script ready
      fallbackLocale: supportedLocales.first,
      child: RestartWidget(
          child: OneNataApp(
        controller: mainController,
      )),
      // MyApp(controller: mainController)
    ));
  });
}

class OneNataApp extends StatelessWidget {

  final MainController controller;
  const OneNataApp({super.key, required this.controller});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {

    // Load device real screen size, which will be used by some pages
    screenSize = Size(
        MediaQuery.of(context).size.width,
        MediaQuery.of(context).size.height
    );
    devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    return ScreenUtilInit(
      // Designed size
      designSize: MediaQuery.of(context).orientation == Orientation.portrait
          ? Size(ThemeLayout.screenWidth, ThemeLayout.screenHeight)
          : Size(ThemeLayout.screenHeight, ThemeLayout.screenWidth),
      rebuildFactor: RebuildFactors.orientation,
      builder: (_, child) => Listener(
        behavior: HitTestBehavior.translucent,
        onPointerDown: (_)=> controller.appLockerController.updateInteractionTime(),
        child: GetMaterialApp(
          title: 'OneNata World',
          // theme: ThemeData(
          //   // This is the theme of your application.
          //   //
          //   // TRY THIS: Try running your application with "flutter run". You'll see
          //   // the application has a purple toolbar. Then, without quitting the app,
          //   // try changing the seedColor in the colorScheme below to Colors.green
          //   // and then invoke "hot reload" (save your changes or press the "hot
          //   // reload" button in a Flutter-supported IDE, or press "r" if you used
          //   // the command line to start the app).
          //   //
          //   // Notice that the counter didn't reset back to zero; the application
          //   // state is not lost during the reload. To reset the state, use hot
          //   // restart instead.
          //   //
          //   // This works for code too, not just values: Most code changes can be
          //   // tested with just a hot reload.
          //   colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          //   useMaterial3: true,
          // ),
          debugShowCheckedModeBanner: false,
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          home: StartPage(),
          //initialRoute: GlobalRouters.rootPage,
          getPages: GlobalRouters.appRouter,
          navigatorObservers: [BotToastNavigatorObserver()],
          // initialBinding: BindingsBuilder(
          //       () {
          //     Get.put(UserController());
          //   },
          // ),
        ),
      ),
    );
  }
}

class MainController extends GetxController {

  late AppLockerController appLockerController;

  Future<MainController> init() async {
    await _globalInit();
    return this;
  }

  /// Initialize essential infrastructure
  Future<void> _globalInit() async {

    // Logger
    await Get.putAsync(() async => Logger());

    // Storage
    await Get.putAsync(() async => SecureStorage());
    await Get.putAsync(() => Storage().init());
    await Get.putAsync(() => LocalStorage().init());

    // Timezone changed handler
    TimezonePlugin tzHandler = TimezonePlugin();
    await tzHandler.init();

    // Localization
    await EasyLocalization.ensureInitialized();

    // Load env
    await dotenv.load(fileName: Env.envFile);
    await Get.putAsync(() async => DioClient());

    // Theme service should NOT be initialized here, as the ScreenUtil is not initialized yet.

    // Firebase initialization
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Enable Firebase App Check, according to the current build mode
    // App check should be always activated before any other Firebase services
    await FirebaseAppCheck.instance.activate(
      androidProvider: kReleaseMode ? AndroidProvider.playIntegrity : AndroidProvider.debug,
      appleProvider: kReleaseMode ? AppleProvider.deviceCheck : AppleProvider.debug,
    );

    // To enable emulators for debug, must after app check activation
    await FirebaseConfig.enableFirebaseEmulators();

    // debug token b5659720-4ab2-4fb5-b5b7-5cf1574c0313

    // Set the background message handler
    // FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // App locker
    appLockerController = await Get.putAsync(() async => AppLockerController());

    // Global config
    userDataEmailSkippedThisTime = false;
    userDataNameSkippedThisTime = false;
    userDataAddressSkippedThisTime = false;
    userDataPetListSkippedThisTime = false;

    // TODO is pop up configs

    // Temporarily disable the geo location services
    // await Get.putAsync(() async => GeolocatorPlugin());

    // Auth and user service
    await Get.putAsync(() => AuthService().init());

    // Pet service
    await Get.putAsync(() => PetService().init());

    //await Get.putAsync(() => WeatherNotificationController().init());
  }

}

    
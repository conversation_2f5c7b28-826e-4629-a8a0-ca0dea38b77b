import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class DateTimeUtil {

  // MilliSeconds

  /// Get milliseconds since epoch of the current time
  static int currentMilliseconds() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// Get seconds since epoch of the current time
  static int currentSeconds() {
    return DateTime.now().millisecondsSinceEpoch ~/ 1000;
  }

  /// Day in milliseconds
  static int dayInMilliseconds(int year, int month, int day, {int hour = 0, int minute = 0, int second = 0}) {
    return DateTime(year, month, day, hour, minute, second).millisecondsSinceEpoch;
  }

  // Timestamp

  /// Get milliseconds since epoch of the current time
  static Timestamp currentTimestamp() {
    return Timestamp.now();
  }

  /// Day in milliseconds
  static Timestamp normalizedTimestamp(int year, int month, int day, {int hour = 0, int minute = 0, int second = 0}) {
    int milliseconds = DateTime(year, month, day, hour, minute, second).millisecondsSinceEpoch;
    return Timestamp.fromMillisecondsSinceEpoch(milliseconds);
  }

  // Normalize

  /// Convert datetime without hour/min/sec info
  static DateTime normalizeDate(DateTime dateTime) {
    return DateTime(dateTime.year, dateTime.month, dateTime.day);
  }

  /// Date of today without time of local time zone
  static DateTime normalizedToday(){
    return DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
  }

  /// Convert day from milliseconds to datetime without hour/min/sec info
  static DateTime normalizeTimestamp(int timestamp) {
    return normalizeDate(DateTime.fromMillisecondsSinceEpoch(timestamp));
  }

  // Format

  /// Checks if two DateTime objects are the same day.
  /// Returns `false` if either of them is null.
  static bool isSameDay(DateTime? a, DateTime? b) {
    if (a == null || b == null) {
      return false;
    }
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

}

extension DateTimeExtension on DateTime {
  String get yMMMMd => DateFormat("dd MMMM y").format(this);
  String get toIso8601Date => DateFormat('yyyy-MM-dd').format(this);
  String get toIso8601Time12 => DateFormat('hh:mm a').format(this);
  String get toIso8601Time24 => DateFormat('HH:mm').format(this);
  bool get isToday =>
      year == DateTime.now().year &&
      month == DateTime.now().month &&
      day == DateTime.now().day;
}

extension MilliSecondsExtension on int {
  String get yMMMMd => DateTime.fromMillisecondsSinceEpoch(this).yMMMMd;
  String get toIso8601DateTime => DateTime.fromMillisecondsSinceEpoch(this).toIso8601String();
  String get toIso8601Date => DateTime.fromMillisecondsSinceEpoch(this).toIso8601Date;
  String get toIso8601Time12 => DateTime.fromMillisecondsSinceEpoch(this).toIso8601Time12;
  String get toIso8601Time24 => DateTime.fromMillisecondsSinceEpoch(this).toIso8601Time24;
  bool get isToday => DateTime.fromMillisecondsSinceEpoch(this).isToday;
}

extension TimestampExtension on Timestamp {
  String get yMMMMd => millisecondsSinceEpoch.yMMMMd;
  String get toIso8601DateTime => millisecondsSinceEpoch.toIso8601DateTime;
  String get toIso8601Date => millisecondsSinceEpoch.toIso8601Date;
  String get toIso8601Time12 => millisecondsSinceEpoch.toIso8601Time12;
  String get toIso8601Time24 => millisecondsSinceEpoch.toIso8601Time24;
  bool get isToday => millisecondsSinceEpoch.isToday;
}

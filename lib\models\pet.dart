import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

import 'base_full_model.dart';

part 'pet.g.dart';

@JsonSerializable()
class Pet extends BaseFullModel {

  String owner; // current owner, all history owner will be stored in owner record
  String? regId;
  String? breed;
  PetGender? gender;
  PetType? type;
  String? avatar;
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isLive;
  int? birthday; // milliseconds since epoch
  List<Device>? attachedDevices;
  @JsonKey(fromJson: JsonUtil.geoFirePointFromJson, toJson: JsonUtil.geoFirePointToJson)
  GeoFirePoint? latestLocation;
  int? latestLocationAt;
  int? latestBatteryLevel;
  int? latestBatteryLevelAt;
  int? latestStandbyTime; // in seconds
  int? latestStandbyTimeAt;
  PetVisibility? visibility;

  Pet({
    required this.owner,
    this.regId,
    this.breed,
    this.gender,
    this.type,
    this.avatar,
    this.isLive,
    this.birthday,
    this.attachedDevices,
    this.latestLocation,
    this.latestLocationAt,
    this.latestBatteryLevel,
    this.latestBatteryLevelAt,
    this.latestStandbyTime,
    this.latestStandbyTimeAt,
    this.visibility = PetVisibility.friend,

    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Pet.fromJson(Map<String, dynamic> json) => _$PetFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Pet.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Pet.fromJson(jsonData);
  }

  factory Pet.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Pet.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'pet';

  static create ({
    required String owner,
    required String name,
    String? regId,
    String? breed,
    PetGender? gender,
    PetType? type,
    String? avatar,
    bool? isLive,
    int? birthday,
    List<Device>? attachedDevices,
    GeoFirePoint? latestLocation,
    int? latestLocationAt,
    int? latestBatteryLevel,
    int? latestBatteryLevelAt,
    int? latestStandbyTime,
    int? latestStandbyTimeAt,
    PetVisibility? visibility,
  }) {

    return Pet(
      sid: uuid.v4(),
      name: name,
      owner: owner,
      regId: regId,
      breed: breed,
      gender: gender,
      type: type,
      avatar: avatar,
      isLive: isLive?? true,
      birthday: birthday,
      attachedDevices: attachedDevices,
      latestLocation: latestLocation,
      latestLocationAt: latestLocationAt,
      latestBatteryLevel: latestBatteryLevel,
      latestBatteryLevelAt: latestBatteryLevelAt,
      latestStandbyTime: latestStandbyTime,
      latestStandbyTimeAt: latestStandbyTimeAt,
      visibility: visibility,
      isValid: true,
    );
  }

  static copyFrom(Pet? other) {
    return other == null ? null : Pet(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      owner: other.owner,
      regId: other.regId,
      breed: other.breed,
      gender: other.gender,
      type: other.type,
      avatar: other.avatar,
      isLive: other.isLive,
      birthday: other.birthday,
      attachedDevices: List<Device>.from(other.attachedDevices ?? []),
      latestLocation: other.latestLocation == null
          ? null
          : GeoFirePoint(GeoPoint(other.latestLocation!.latitude, other.latestLocation!.longitude)),
      latestLocationAt: other.latestLocationAt,
      latestBatteryLevel: other.latestBatteryLevel,
      latestBatteryLevelAt: other.latestBatteryLevelAt,
      latestStandbyTime: other.latestStandbyTime,
      latestStandbyTimeAt: other.latestStandbyTimeAt,
      visibility: other.visibility,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'device_message_header.dart';

part 'device_status_message.g.dart';

@JsonSerializable()
class DeviceStatusMessage extends DeviceMessageHeader {

  int? battery;
  int? standbyTime;

  DeviceStatusMessage({
    super.sid,
    super.pid,
    required super.deviceId,
    super.generateDate,
    super.sendDate,
    super.receiveDate,
    this.battery,
    this.standbyTime,
  });

  factory DeviceStatusMessage.fromJson(Map<String, dynamic> json) => _$DeviceStatusMessageFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$DeviceStatusMessageToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory DeviceStatusMessage.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return DeviceStatusMessage.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory DeviceStatusMessage.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return DeviceStatusMessage.fromJson(jsonData);
  }

  factory DeviceStatusMessage.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return DeviceStatusMessage.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(DeviceStatusMessage? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? sid,
    String? pid,
    required String deviceId,
    int? generateDate,
    int? sendDate,
    int? receiveDate,
    int? battery,
    int? standbyTime,
  }) {

    return DeviceStatusMessage(
      sid: sid?? uuid.v4(),
      pid: pid,
      deviceId: deviceId,
      generateDate: generateDate,
      sendDate: sendDate,
      receiveDate: receiveDate,
      battery: battery,
      standbyTime: standbyTime,
    );
  }

  static DeviceStatusMessage? copyFrom(DeviceStatusMessage? other) {
    return other == null ? null : DeviceStatusMessage(
      sid: other.sid,
      pid: other.pid,
      deviceId: other.deviceId,
      generateDate: other.generateDate,
      sendDate: other.sendDate,
      receiveDate: other.receiveDate,
      battery: other.battery,
      standbyTime: other.standbyTime,
    );
  }

  static String get collection => 'device_status_message';

  @override
  String toString() {
    return jsonEncode(this);
  }

}

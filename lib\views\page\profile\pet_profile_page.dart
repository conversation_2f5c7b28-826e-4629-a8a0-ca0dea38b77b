import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import '../../../models/pet_growing_record.dart';
import '../../../models/user_account.dart';
import '../../../models/pet.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../models/vo/pet_profile_vo.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../../component/progress/loading_widget.dart';
import '../../component/widgets/common_page.dart';
import '../../component/widgets/common_text.dart';
import '../../theme/colors/color_extension.dart';
import '../../theme/layouts/theme_layout.dart';
import '../../theme/text_schemes/text_style_extension.dart';
import '../../theme/theme_plugin.dart';
import '../../theme/theme_service.dart';
import '../page_interceptor.dart';
import 'posts_section.dart';
import 'events_section.dart';
import 'places_section.dart';

class PetProfilePage extends StatefulWidget {
  const PetProfilePage({super.key});

  @override
  PetProfilePageState createState() => PetProfilePageState();
}

class PetProfilePageState extends State<PetProfilePage> {
  late Future<PetProfilePageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => PetProfilePageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PetProfilePageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: NotificationListener<ScrollNotification>(
                onNotification: (scrollNotification) {
                  if (scrollNotification is ScrollUpdateNotification) {
                    setState(() {
                      controller.topSectionHeight =
                          (100 - scrollNotification.metrics.pixels)
                              .clamp(0, 100)
                              .toDouble();
                      controller.opacity =
                          (controller.topSectionHeight / 100).clamp(0, 1);
                      controller.avatarSize =
                          (128 - scrollNotification.metrics.pixels)
                              .clamp(controller.avatarMinSize, 128)
                              .toDouble();
                      controller.avatarBorder =
                          (17 - scrollNotification.metrics.pixels)
                              .clamp(controller.avatarMinBorder, 17)
                              .toDouble();
                    });
                  }
                  return true;
                },
                child: Column(
                  children: [
                    _buildTopSection(controller),
                    _buildProfileSection(controller),
                    _buildInfoSection(controller),
                    Expanded(
                      child: SingleChildScrollView(
                        physics: BouncingScrollPhysics(),
                        child: _buildTabSection(controller),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        });
  }

  Widget _buildTopSection(PetProfilePageController controller) {
    return Column(children: [
      AnimatedContainer(
        duration: Duration(milliseconds: 200),
        height: controller.topSectionHeight.w,
        padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 12.w),
        child: Opacity(
          opacity: controller.opacity,
          child: Row(
            children: [
              IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Icon(Icons.arrow_back,
                    color: controller.theme.themeData.colorScheme.onSurface),
              ),
              SizedBox(width: 8.w),
              Container(
                width: 1.w,
                height: 22.w,
                color: controller.theme.themeData.colorScheme.tertiary,
              ),
              SizedBox(width: 8.w),
              CommonText(
                "Pet Profile",
                controller.theme.textStyleExtension.userProfileSettingBody,
              ),
              const Spacer(),
              Obx(() => Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: controller.selectedPet.value,
                    icon: Icon(Icons.keyboard_arrow_down,
                        color: controller
                            .theme.themeData.colorScheme.tertiary),
                    dropdownColor: Colors.white,
                    items: controller.pets.map((pet) {
                      return DropdownMenuItem<String>(
                        value: pet.name,
                        child: Row(
                          children: [
                            //   AuthWidgetBuilder.buildAvatar(
                            //   controller.theme,
                            //       avatar: await ProfileWidgetBuilder.buildPetAvatar(
                            //       controller.theme,
                            //       userId: controller._authService.userAccount.value!.sid!,
                            //       petId: controller.pet.sid!,
                            //       avatar: controller.pet.avatar,
                            //       size: 125
                            //   ),
                            // ),
                            Container(
                              width: 20.w,
                              height: 20.w,
                              child: buildPetAvatarWidget(
                                controller.theme,
                                controller.userAccount.value!.sid!,
                                controller.petList!
                                    .firstWhere(
                                      (p) =>
                                  p.name ==
                                      controller.selectedPet.value,
                                  orElse: () =>
                                  controller.petList!.first,
                                )
                                    .sid!,
                                controller.petList!
                                    .firstWhere(
                                      (p) =>
                                  p.name ==
                                      controller.selectedPet.value,
                                  orElse: () =>
                                  controller.petList!.first,
                                )
                                    .avatar,
                                controller.avatarSize,
                              ),
                            ),
                            // CircleAvatar(
                            //   radius: 10.w,
                            //   backgroundImage: AssetImage(pet["image"]!),
                            // ),
                            SizedBox(width: 8.w),
                            Container(
                              width: 40.w,
                              child: CommonText(
                                pet.name,
                                controller.theme.textStyleExtension
                                    .placeListItemMileage,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        controller.selectedPet.value = newValue;
                        controller.currentPetData.value = controller.pets
                            .firstWhere((pet) => pet.name == newValue);
                      }
                    },
                  ),
                ),
              )),
            ],
          ),
        ),
      )
    ]);
  }

  Widget _buildProfileSection(PetProfilePageController controller) {
    return Obx(() => Padding(
      //padding: EdgeInsets.all(38.w),
      padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 20.w),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                  color: controller.theme.themeData.colorScheme.primary,
                  width: controller.avatarBorder),
            ),
            child: AnimatedContainer(
              duration: Duration(milliseconds: 200),
              width: controller.avatarSize.w,
              height: controller.avatarSize.w,
              child: FutureBuilder<Widget?>(
                future: controller.petAvatarFutures[controller.petList!
                    .firstWhere(
                      (p) => p.name == controller.selectedPet.value,
                  orElse: () => controller.petList!.first,
                )
                    .sid!],
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return SizedBox(
                        width: controller.avatarSize.w,
                        height: controller.avatarSize.w);
                  } else if (snapshot.hasError || snapshot.data == null) {
                    return CircleAvatar(
                      radius: controller.avatarSize / 2.w,
                      backgroundImage:
                      AssetImage("assets/images/default_pet.png"),
                    );
                  } else {
                    return AuthWidgetBuilder.buildAvatar(controller.theme,
                        avatar: snapshot.data);
                  }
                },
              ),
            ),
          ),
          SizedBox(width: 20.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 97.w,
                    child: CommonText(
                      controller.currentPetData.value.name ?? "Unknown",
                      controller
                          .theme.textStyleExtension.petAvatarLargeName,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Opacity(
                    opacity: controller.opacity,
                    child: GestureDetector(
                      onTap: () {
                        final pet = controller.petList!.firstWhere(
                              (p) => p.name == controller.selectedPet.value,
                          orElse: () => controller.petList!.first,
                        );
                        //print("Selected Pet: ${pet.name}");
                        Get.toNamed('/petSetting', arguments: {"selectedPet": pet});
                      },
                      child: Container(
                        padding: EdgeInsets.all(9.w),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: controller.theme.themeData.colorScheme.primary,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(14),
                        ),
                        child: Icon(
                          Icons.edit,
                          size: 20.sp,
                          color: controller.theme.themeData.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4.w),
              Opacity(
                opacity: controller.opacity,
                child: CommonText(
                  "${controller.currentPetData.value.type ?? "-"}  |  ${controller.currentPetData.value.breed ?? "-"}",
                  controller.theme.textStyleExtension.petAvatarLargeIntro,
                ),
              ),
            ],
          )
        ],
      ),
    ));
  }

  Widget _buildInfoSection(PetProfilePageController controller) {
    return Obx(() => Padding(
      padding: EdgeInsets.symmetric(horizontal: 38.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //SizedBox(height: 24.h),
          _buildInfoRow(controller, "Gender",
              controller.currentPetData.value.gender ?? "-"),
          SizedBox(height: 8.w),
          _buildInfoRow(controller, "Weight",
              controller.currentPetData.value.weight ?? "-"),
          SizedBox(height: 24.w),
          _buildDetailRow(
              controller,
              Icons.cake,
              "Birthday",
              controller.currentPetData.value.birthday ?? "-",
              _calculateAge(
                  controller, controller.currentPetData.value.birthday ?? "")),
          SizedBox(height: 16.w),
          _buildDetailRow(controller, Icons.home, "My home",
              controller.currentPetData.value.home ?? "-", ""),
          SizedBox(height: 16.w),
          _buildDetailRow(controller, Icons.vaccines, "Next vaccine",
              controller.currentPetData.value.nextVaccine ?? "-", ""),
          SizedBox(height: 34.w),
        ],
      ),
    ));
  }

  Widget _buildInfoRow(
      PetProfilePageController controller, String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CommonText(
          title,
          controller.theme.textStyleExtension.petInfoTitle,
        ),
        CommonText(
          value,
          controller.theme.textStyleExtension.petInfoBody,
        ),
      ],
    );
  }

  Widget _buildDetailRow(PetProfilePageController controller, IconData icon,
      String title, String value, String? subValue) {
    return Row(
      children: [
        Container(
          width: 46.w,
          height: 46.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: controller.theme.themeData.colorScheme.primary,
                width: 1.w),
          ),
          child: Icon(icon,
              size: 20.sp,
              color: controller.theme.themeData.colorScheme.primary),
        ),
        SizedBox(width: 10.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonText(
              title,
              controller.theme.textStyleExtension.petInfoListTitle,
            ),
            CommonText(
              value,
              controller.theme.textStyleExtension.petInfoBody,
            ),
          ],
        ),
        const Spacer(),
        if (subValue != null && subValue.isNotEmpty)
          Text(
            subValue,
            style: controller.theme.textStyleExtension.petAvatar,
          ),
      ],
    );
  }

  Widget _buildTabSection(PetProfilePageController controller) {
    return Column(
      children: [
        DefaultTabController(
          length: 3,
          child: Column(
            children: [
              TabBar(
                labelStyle:
                controller.theme.textStyleExtension.petInfoBody, // 选中时的颜色
                unselectedLabelStyle:
                controller.theme.textStyleExtension.petInfoTitle, // 未选中时的颜色
                tabs: [
                  Tab(text: "Posts"),
                  Tab(text: "Events"),
                  Tab(text: "Places"),
                ],
              ),
              SizedBox(
                height: 350.w,
                child: TabBarView(
                  children: [
                    PostsSection(theme: controller.theme),
                    EventsSection(theme: controller.theme),
                    PlacesSection(theme: controller.theme),
                  ],
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  String _calculateAge(PetProfilePageController controller, String birthday) {
    try {
      DateTime birthDate = DateFormat("d MMMM yyyy").parse(birthday);
      DateTime now = DateTime.now();

      int years = now.year - birthDate.year;
      int months = now.month - birthDate.month;

      if (months < 0) {
        years -= 1;
        months += 12;
      }

      return "$years y $months m";
    } catch (e) {
      return "N/A";
    }
  }

  Widget buildPetAvatarWidget(
      ThemePlugin theme,
      String userId,
      String petId,
      String? avatar,
      double size,
      ) {
    return FutureBuilder<Widget?>(
      future: ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: userId,
        petId: petId,
        avatar: avatar,
        size: size,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(width: size.w, height: size.w);
        } else if (snapshot.hasError || snapshot.data == null) {
          return CircleAvatar(
            radius: size / 2.w,
            backgroundImage: AssetImage("assets/images/default_pet.png"),
          );
        } else {
          return AuthWidgetBuilder.buildAvatar(theme, avatar: snapshot.data);
        }
      },
    );
  }
}

class PetProfilePageController extends GetxController {
  late ThemePlugin theme;
  final AuthService _authService = AuthService.instance;
  var userAccount = Rx<UserAccount?>(null);
  final PetService _petService = PetService();
  List<Pet>? petList;
  UserData? userData;

  final Map<String, Future<Widget?>> petAvatarFutures = {};

  double topSectionHeight = 100.0;
  double opacity = 1.0;
  double avatarSize = 128;
  double avatarMinSize = 74;
  double avatarBorder = 17;
  double avatarMinBorder = 10;

  late RxString selectedPet = 'Unknown'.obs;

  late List<PetProfileVO> pets;

  final Rx<PetProfileVO> currentPetData = PetProfileVO.empty().obs;

  Future<PetProfilePageController> init() async {

    await PageInterceptor.pageAuthCheck();

    final Pet? passedPet = (Get.arguments as Map<String, dynamic>?)?['selectedPet'];

    theme = await Get.putAsync(() => ThemePlugin().init());
    userAccount.value = _authService.userAccount.value;
    userData = _authService.userData.value;

    petList = await _petService.getOwnedPets(userAccount.value!.sid!);
    pets = [];

    for (var pet in petList ?? []) {
      PetGrowingRecord? record =
      await _petService.getLatestPetGrowingRecordById(pet.sid!);
      double? weight = record?.weight;
      pets.add(PetProfileVO(
        name: pet.name ?? "Unnamed",
        image: pet.avatar ?? "assets/images/default_pet.png",
        type: pet.type == PetType.cat
            ? PetType.cat.t18key.t18
            : pet.type == PetType.dog
            ? PetType.dog.t18key.t18
            : PetType.others.t18key.t18,
        breed: pet.breed ?? "unknown",
        gender: pet.gender == PetGender.boy
            ? PetGender.boy.t18key.t18
            : pet.gender == PetGender.girl
            ? PetGender.girl.t18key.t18
            : "unknown",
        weight: weight != null ? "${weight.toStringAsFixed(2)} kg" : "unknown",
        birthday: pet.birthday != null
            ? DateFormat("d MMMM yyyy")
            .format(DateTime.fromMillisecondsSinceEpoch(pet.birthday!))
            : "unknown",
        home: getFormattedLocation(),
        nextVaccine: "N/A",
      ));

      petAvatarFutures[pet.sid!] = ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: userAccount.value!.sid!,
        petId: pet.sid!,
        avatar: pet.avatar,
        size: avatarSize,
      );
    }

    if (passedPet != null) {
      final found = pets.firstWhere(
            (p) => p.name == passedPet.name,
        orElse: () => pets.first,
      );
      currentPetData.value = found;
      selectedPet.value = found.name;
    }
    else {
       if (pets.isNotEmpty) {
         currentPetData.value = pets.first;
         selectedPet.value = currentPetData.value.name;
       }
     }

    return this;
  }

  // Future<PetProfilePageController> init() async {
  //   theme = await Get.putAsync(() => ThemePlugin().init());
  //   userAccount.value = _authService.userAccount.value;
  //   petList = await _petService.getOwnedPets(userAccount.value!.sid!);
  //   userData = _authService.userData.value;
  //
  //   PetGrowingRecord? record = await _petService.getLatestPetGrowingRecordById(pet.sid!);
  //   double? weight = record?.weight;
  //
  //   pets = petList?.map((pet) {
  //         return {
  //           "name": pet.name ?? "Unnamed",
  //           "image": "assets/images/default_pet.png", // 替换为 pet.avatar 如果有图片路径
  //           "type": pet.type != null
  //               ? pet.type!.name.toLowerCase()
  //               : "unknown",
  //           "breed": pet.breed ?? "Unknown",
  //           "gender": pet.gender != null
  //               ? (pet.gender == PetGender.boy ? "male" : "female")
  //               : "unknown",
  //           // "weight": pet.weight != null ? "${pet.weight} kg" : "Unknown",
  //           "weight": weight,
  //           "birthday": pet.birthday != null
  //               ? DateFormat("d MMMM yyyy")
  //                   .format(DateTime.fromMillisecondsSinceEpoch(pet.birthday!))
  //               : "Unknown",
  //           "home": getFormattedLocation(), // 如果你有 pet.location，可改成拼接结果
  //           "nextVaccine": "N/A", // 你可以之后补充 vaccineRecord 逻辑
  //         };
  //       }).toList() ??
  //       [];
  //
  //   if (pets.isNotEmpty) {
  //     currentPetData.value = pets.first;
  //   }
  //   selectedPet.value = currentPetData["name"]!;
  //
  //   return this;
  // }

  String getFormattedLocation() {
    final city = userData?.location?.locality ?? userData?.location?.subLocality;
    final state = userData?.location?.administrativeArea;
    final country = userData?.location?.region;

    final displayCity = city?.isNotEmpty == true ? city : "userCity";
    final displayState = state?.isNotEmpty == true ? state : "userState";
    final displayCountry =
    country?.isNotEmpty == true ? country : "userCountry";

    final isAllDefault = displayCity == "userCity" &&
        displayState == "userState" &&
        displayCountry == "userCountry";

    return isAllDefault
        ? "Add your address"
        : "$displayCity, $displayState, $displayCountry";
  }
}

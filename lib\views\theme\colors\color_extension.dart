import 'package:flutter/material.dart';

import 'onenata_classic_colors.dart';

class ColorExtension extends ThemeExtension<ColorExtension> {

  // Common
  late Color commonIconBackground;
  late Color commonSurface2;

  // Auth page colors
  late Color authWhiteSectionShadow;
  late Color authBackButtonShadow;
  late Color authAvatarShadow;
  late Color authPetBreedBallDogLargeGradient1;
  late Color authPetBreedBallDogLargeGradient2;
  late Color authPetBreedBallDogSmallGradient1;
  late Color authPetBreedBallDogSmallGradient2;
  late Color authPetBreedBallCatLargeGradient1;
  late Color authPetBreedBallCatLargeGradient2;
  late Color authPetBreedBallCatSmallGradient1;
  late Color authPetBreedBallCatSmallGradient2;
  late Color authPetBreedBallOtherLargeGradient1;
  late Color authPetBreedBallOtherLargeGradient2;
  late Color authPetBreedBallOtherSmallGradient1;
  late Color authPetBreedBallOtherSmallGradient2;
  late Color authPetAddAvatarCameraShadow;
  late Color authPendingVerifyLightRingLarge;
  late Color authPendingVerifyLightRingMedium;
  late Color authPendingVerifyLightRingSmall;

  // Event page colors
  late Color eventPostTileShadow;
  late Color eventEventTileShadow1;
  late Color eventEventTileShadow2;
  late Color eventPlaceTileShadow1;
  late Color eventPlaceTileShadow2;

  // Profile page colors
  late Color profileAvatarCameraShadow;

  // Geo page colors
  late Color geoOutdoor;
  late Color geoPark;
  late Color geoTrail;
  late Color geoVet;
  late Color geoPetStore;
  late Color geoDogFriendlyRestaurant;
  late Color geoFavor;
  late Color geoCommonPointBg;
  late Color geoPointPetActiveGradientStart;
  late Color geoPointPetActiveGradientEnd;
  late Color geoPointPetInactiveGradientStart;
  late Color geoPointPetInactiveGradientEnd;
  late Color geoPointParkGradient1;
  late Color geoPointParkGradient2;
  late Color geoPointParkSelected;
  late Color geoPointTrailGradient1;
  late Color geoPointTrailGradient2;
  late Color geoPointTrailSelected;
  late Color geoFilterSelected;
  late Color geoFilterButtonShadow;
  late Color geoPlaceInfoListScrollBar;

  @override
  ThemeExtension<ColorExtension> copyWith() {
    // TODO: implement copyWith
    throw UnimplementedError();
  }

  @override
  ThemeExtension<ColorExtension> lerp(covariant ThemeExtension<ColorExtension>? other, double t) {
    // TODO: implement lerp
    throw UnimplementedError();
  }
}


extension ColorConvertExtension on double {
  int get colorAlpha => (this * 255).toInt();
}


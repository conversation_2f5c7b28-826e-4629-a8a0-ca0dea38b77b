import 'package:flutter/material.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';

class PlaceInfoCard extends StatelessWidget {
  final Map<String, dynamic> place;
  final VoidCallback onClose;
  final VoidCallback onNavigate;
  final VoidCallback onAddToFavorites;
  final bool isFavorite;

  const PlaceInfoCard({
    Key? key,
    required this.place,
    required this.onClose,
    required this.onNavigate,
    required this.onAddToFavorites,
    required this.isFavorite,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  place['name'] ?? 'mappage.placeInfoCard.unknownAddress'.t18,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                children: [
                  IconButton(
                    icon: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: Colors.red,
                    ),
                    onPressed: onAddToFavorites,
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: onClose,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            place['formatted_address'] ?? 'mappage.placeInfoCard.noAddress'.t18,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          if (place['rating'] != null)
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${place['rating']} · ${place['user_ratings_total'] ?? 0} ${'mappage.placeInfoCard.comments'.t18}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onNavigate,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 40),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('mappage.placeInfoCard.navigationToHere'.t18),
          ),
        ],
      ),
    );
  }
}

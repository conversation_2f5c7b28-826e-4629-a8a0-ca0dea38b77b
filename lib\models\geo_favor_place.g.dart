// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_favor_place.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoFavorPlace _$GeoFavorPlaceFromJson(Map<String, dynamic> json) =>
    GeoFavorPlace(
      uid: json['uid'] as String,
      placeName: json['placeName'] as String,
      filterType: $enumDecode(_$GeoPlaceFilterTypeEnumMap, json['filterType']),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$GeoFavorPlaceToJson(GeoFavorPlace instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'placeName': instance.placeName,
      'filterType': _$GeoPlaceFilterTypeEnumMap[instance.filterType]!,
    };

const _$GeoPlaceFilterTypeEnumMap = {
  GeoPlaceFilterType.outdoor: 'OUT',
  GeoPlaceFilterType.park: 'PRK',
  GeoPlaceFilterType.trail: 'TRL',
  GeoPlaceFilterType.vet: 'VET',
  GeoPlaceFilterType.petStore: 'GRM',
  GeoPlaceFilterType.dogFriendlyRestaurant: 'DFR',
  GeoPlaceFilterType.favor: 'FAV',
};

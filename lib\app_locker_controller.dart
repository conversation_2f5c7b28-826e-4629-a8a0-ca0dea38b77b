import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/plugin/logger/logger_i.dart';

class AppLockerController extends GetxController {

  final int timerInterval = 1000;
  var isLocked = false.obs;
  var lastInteractionTime = DateTime.now().obs;
  late Timer _timer;

  @override
  void onInit() {
    super.onInit();
    _startTimer();
  }

  @override
  void onClose() {
    _timer.cancel();
    super.onClose();
  }

  // TODO double confirm if timer work since the async method lockApp is called
  Future<void> _startTimer() async {
    _timer = Timer.periodic(Duration(minutes: timerInterval), (timer) async {
      if (DateTime.now()
          .difference(lastInteractionTime.value)
          .inMinutes >= timerInterval) {
        // await lockApp();
      }
    });
  }

  void updateInteractionTime() {
    logger.d('User gesture tapping or scaling detected. Resetting timer...');
    FocusScope.of(Get.context!).unfocus();  // 取消焦点, 不重建状态, 确保不会触发onClose影响操作
    lastInteractionTime.value = DateTime.now();
  }

}

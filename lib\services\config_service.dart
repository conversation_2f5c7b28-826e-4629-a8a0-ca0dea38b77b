import 'dart:async';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';

class ConfigService {

  final SecureStorage _secureStorage = SecureStorage.instance;
  final Storage _storage = Storage.instance;

  Future<void> clearGlobalConfig() async {
    await _storage.remove(StorageKeys.userDataEmailSkipped);
    await _storage.remove(StorageKeys.userDataNameSkipped);
    await _storage.remove(StorageKeys.userDataAddressSkipped);
    await _storage.remove(StorageKeys.userDataPetListSkipped);
  }

}

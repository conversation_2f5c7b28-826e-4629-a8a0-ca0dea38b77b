// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_auth_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserAuthRecord _$UserAuthRecordFromJson(Map<String, dynamic> json) =>
    UserAuthRecord(
      uid: json['uid'] as String,
      authChannel: $enumDecode(_$AuthChannelEnumMap, json['authChannel']),
      deviceModel: json['deviceModel'] as String?,
      deviceOS: json['deviceOS'] as String?,
      deviceId: json['deviceId'] as String?,
      fcmToken: json['fcmToken'] as String?,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
    );

Map<String, dynamic> _$UserAuthRecordToJson(UserAuthRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'uid': instance.uid,
      'authChannel': _$AuthChannelEnumMap[instance.authChannel]!,
      'deviceModel': instance.deviceModel,
      'deviceOS': instance.deviceOS,
      'deviceId': instance.deviceId,
      'fcmToken': instance.fcmToken,
    };

const _$AuthChannelEnumMap = {
  AuthChannel.emailPassword: 'CRED',
  AuthChannel.sms: 'SMS',
};

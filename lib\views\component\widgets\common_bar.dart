import 'package:flutter/material.dart';

import 'package:onenata_app/views/theme/colors/colors_i.dart';

class CommonBar extends StatelessWidget {

  final BoxShape? shape;
  final Color? color;
  final double? opacity;

  final double? radius;
  final double? width;
  final double? height;
  final double? circular;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? margin;

  final bool? hasBorder;
  final Color? borderColor;
  final double? borderWidth;
  final double? borderOpacity;

  final bool? hasShadow;
  final Color? shadowColor;
  final Offset? shadowOffset;
  final double? shadowBlurRadius;
  final double? shadowSpreadRadius;

  const CommonBar({
    super.key,
    this.shape = BoxShape.rectangle,
    this.color,
    this.opacity,
    this.radius,
    this.width,
    this.height,
    this.circular,
    this.borderRadius,
    this.margin,
    this.hasBorder = false,
    this.borderColor,
    this.borderWidth,
    this.borderOpacity = 1,
    this.hasShadow = false,
    this.shadowColor,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.shadowSpreadRadius
  });

  @override
  Widget build(BuildContext context) {

    Color c = color ?? Colors.white;
    if (opacity != null) {
      c = c.withAlpha(opacity!.colorAlpha);
    }

    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: c,
        borderRadius: borderRadius ?? (shape == BoxShape.circle
            ? BorderRadius.circular(radius! / 2)
            : circular == null
                ? null
                : BorderRadius.circular(circular!)),
        border: !hasBorder!
            ? null
            : Border.all(
                color: borderColor!.withAlpha(borderOpacity!.colorAlpha),
                width: borderWidth ?? 1.0,
              ),
        boxShadow: !hasShadow!
            ? null
            : [
                BoxShadow(
                  color: shadowColor!,
                  offset: shadowOffset ?? const Offset(0, 0),
                  blurRadius: shadowBlurRadius ?? 10.0,
                  spreadRadius: shadowSpreadRadius ?? 0.0,
                ),
              ],
      ),
      width: shape == BoxShape.circle
          ? radius
          : width!,
      height: shape == BoxShape.circle
          ? radius
          : height!,
      margin: margin,
    );
  }
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  ResetPasswordPageState createState() =>
      ResetPasswordPageState();
}

class ResetPasswordPageState
    extends State<ResetPasswordPage> {
  late Future<ResetPasswordPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller =
        Get.putAsync(() => ResetPasswordPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ResetPasswordPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [
                      // Avatar
                      SizedBox(
                          height: controller.theme.layout.authAvatarHeaderLineHeight +
                              controller.theme.layout.authAvatarTitleMarginTop +
                              controller.theme.layout.authWhiteSectionCircular / 2
                      ),

                      // Sign up label
                      SizedBox(
                        // height: 70.w,
                        child: Obx(()=> AuthWidgetBuilder.buildLabel(
                            controller.theme,
                            title: controller.pageLabelTitle.value,
                            desc: controller.pageLabelDesc.value)
                        ),
                      ),

                      Expanded(child: SizedBox.shrink()),

                      // submit button
                      SizedBox(
                          height: 60.w,
                          child: AuthWidgetBuilder.buildButton2(
                            controller.theme,
                            controller.buttonController,
                          )),

                      // bottom padding
                      SizedBox(height: 35.w,),
                    ],
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context, avatar: controller.pageHeaderUserAvatar.value, title: controller.pageHeaderUserName.value),
                ],
              ),
            );
          }
        });
  }
}

class ResetPasswordPageController extends GetxController {

  // Services
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  // Submit buttons
  late CommonButton3Controller buttonController;

  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);
  var onPressed = Rx<AsyncCallback?>(null);

  // Theme plugin
  late ThemePlugin theme;

  Future<ResetPasswordPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.reset.password.hello'.t19({
          'name': authService.userData.value?.firstName ?? 'auth.label.reset.password.default.name'.t18,
        }),
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
      'auth.label.reset.password.desc'.t18,
      theme.textStyleExtension.authPageDesc,
      width: 350.w,
      height: 70.w,
      maxLines: 3,
      softWrap: true,
    );

    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.reset.password.continue'.t18.obs,
      onPressed: redirectToResetPassword.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  Future<void> redirectToResetPassword() async {



    Get.to(NewPasswordPage());
  }
}

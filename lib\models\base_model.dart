import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

part 'base_model.g.dart';

@JsonSerializable()
class BaseModel {

  int? id;
  String? sid;
  String? name;
  @Json<PERSON>ey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isValid;
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isSynced;

  BaseModel({
    this.id,
    this.sid,
    this.name,
    this.isValid,
    this.isSynced,
  });

  factory BaseModel.fromJson(Map<String, dynamic> json) => _$BaseModelFromJson(json);
  Map<String, dynamic> toJson() => _$BaseModelToJson(this);

  static copyFrom(BaseModel other) {
    return BaseModel(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

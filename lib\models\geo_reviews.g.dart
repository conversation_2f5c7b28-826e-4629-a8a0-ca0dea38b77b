// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_reviews.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoReviews _$GeoReviewsFromJson(Map<String, dynamic> json) => GeoReviews(
      authorAttributions: (json['authorAttributions'] as List<dynamic>?)
          ?.map((e) => GeoAuthorAttribution.fromJson(e as Map<String, dynamic>))
          .toList(),
      name: json['name'] as String?,
      rating: json['rating'] as String?,
      text: json['text'] == null
          ? null
          : GeoReviewsText.fromJson(json['text'] as Map<String, dynamic>),
      originalText: json['originalText'] == null
          ? null
          : GeoReviewsText.fromJson(
              json['originalText'] as Map<String, dynamic>),
      authorUri: json['authorUri'] as String?,
      publishTime: json['publishTime'] == null
          ? null
          : DateTime.parse(json['publishTime'] as String),
      flagContentUri: json['flagContentUri'] as String?,
      googleMapsUri: json['googleMapsUri'] as String?,
    );

Map<String, dynamic> _$GeoReviewsToJson(GeoReviews instance) =>
    <String, dynamic>{
      'authorAttributions': instance.authorAttributions,
      'name': instance.name,
      'rating': instance.rating,
      'text': instance.text,
      'originalText': instance.originalText,
      'authorUri': instance.authorUri,
      'publishTime': instance.publishTime?.toIso8601String(),
      'flagContentUri': instance.flagContentUri,
      'googleMapsUri': instance.googleMapsUri,
    };

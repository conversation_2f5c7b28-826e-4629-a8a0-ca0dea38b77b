import 'dart:collection';

import 'package:dio/dio.dart' as d;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:onenata_app/common/enum/geo_location_enum.dart';

import 'package:onenata_app/models/vo/vo_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import '../dio_client.dart';

class GeoLocationApi {

  final DioClient _dio = DioClient.instance;

  /// Get nearby places
  Future<ApiResponseData> searchNearBy({
    required LatLng latlng,
    required List<String> type,
    GeoLocationZoomLevel? zoomLevel = GeoLocationZoomLevel.level_14,
    int? pageSize = 10}
  ) async {

    String path = _path(GoogleMapResources.nearby);
    Map<String, dynamic> params = {
      'includedTypes': type,
      "maxResultCount": pageSize,
      "locationRestriction": {
        "circle": {
          "center": {
            "latitude": latlng.latitude,
            "longitude": latlng.longitude
          },
          "radius": zoomLevel!.radius
        }
      },
    };

    d.Options options = _header();

    return await _dio.gmPost(path, data: params, options: options);
  }

  /// Text search
  Future<ApiResponseData> searchText({
    required LatLng low,
    required LatLng high,
    required String keyword,
    List<String>? type,
    LatLng? latlng,
    GeoLocationZoomLevel? zoomLevel = GeoLocationZoomLevel.level_14,
    int? pageSize = 10}
      ) async {

    String path = _path(GoogleMapResources.text);
    Map<String, dynamic> params = latlng == null ? {
      "maxResultCount": pageSize,
      "locationBias": {
        "rectangle": {
          "low": {
            "latitude": low.latitude,
            "longitude": low.longitude
          },
          "high": {
            "latitude": high.latitude,
            "longitude": high.longitude
          },
        }
      },
    } : {
      "maxResultCount": pageSize,
      "locationBias": {
        "circle": {
          "center": {
            "latitude": latlng.latitude,
            "longitude": latlng.longitude
          },
          "radius": zoomLevel!.radius
        }
      },
    };

    params['textQuery'] = keyword;

    if (type != null) {
      params['includedTypes'] = type;
    }

    d.Options options = _header();

    return await _dio.gmPost(path, data: params, options: options);
  }

  // Supporting methods --------------------------------------------------------
  String _path(GoogleMapResources res){
    return '/${ApiConst.gmApiPathPlace}:${res.path}';
  }

  d.Options _header() {
    return d.Options(
      headers: {
        GoogleMapApiHeader.apiKey.key: ApiConst.gmApiKey,
        GoogleMapApiHeader.placeField.key: ApiConst.gmApiSearchFieldNearbyPlaces,
      },
    );
  }
}

enum GoogleMapResources {
  nearby('searchNearby'),
  text('searchText'),
  ;

  const GoogleMapResources(this.path);
  final String path;
}


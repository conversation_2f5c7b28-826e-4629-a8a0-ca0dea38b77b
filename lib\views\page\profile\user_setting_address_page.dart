import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingAddressPage extends StatefulWidget {
  const UserSettingAddressPage({super.key});

  @override
  UserSettingAddressPageState createState() => UserSettingAddressPageState();
}

class UserSettingAddressPageState extends State<UserSettingAddressPage> {
  late Future<UserSettingAddressPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserSettingAddressPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingAddressPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  ProfileWidgetBuilder.buildUserSettingTopSection(
                    theme: controller.theme,
                    onBack: () {
                      Get.back();
                    },
                    topic: "Address",
                    showAvatar: false,
                  ),
                  ProfileWidgetBuilder.buildText(
                      controller.theme, 128, 'Street address'),
                  ProfileWidgetBuilder.buildSettingInputBox(
                      controller.theme, controller.addressInputController, 168),
                  ProfileWidgetBuilder.buildText(controller.theme, 248, 'City'),
                  ProfileWidgetBuilder.buildSettingInputBox(
                      controller.theme, controller.cityInputController, 288),
                  ProfileWidgetBuilder.buildText(controller.theme, 368, 'Country'),
                  ProfileWidgetBuilder.buildCountryField(
                    theme: controller.theme,
                    selectedCountry: controller.selectedCountry,
                    isCountrySelected: controller.isCountrySelected,
                    top: 408,
                    onChanged: (newValue) {
                      if (newValue != null) {
                        controller.selectedCountry.value = newValue;
                        controller.isCountrySelected.value = true;
                      }
                    },
                  ),
                  ProfileWidgetBuilder.buildText(
                      controller.theme, 488, 'Post code'),
                  ProfileWidgetBuilder.buildSettingInputBox(controller.theme,
                      controller.postcodeInputController, 528),
                  ProfileWidgetBuilder.buildChangedNotification(
                    isVisible: controller.isChangedNotificationVisible,
                    theme: controller.theme,
                    top: 608,
                    //left: 117,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.buttonController,
                    721,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.alterButtonController,
                    781,
                  ),
                ],
              ),
            );
          }
        });
  }
}

class UserSettingAddressPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final TextEditingController addressInputBox = TextEditingController();
  late CommonTextField3Controller addressInputController;
  final TextEditingController cityInputBox = TextEditingController();
  late CommonTextField3Controller cityInputController;
  final TextEditingController postcodeInputBox = TextEditingController();
  late CommonTextField3Controller postcodeInputController;

  final RxBool isAddressEntered = false.obs;
  final RxBool isCityEntered = false.obs;
  final RxBool isCountrySelected = false.obs;
  final RxBool isPostCodeEntered = false.obs;
  final RxBool isChangedNotificationVisible = false.obs;

  late RxString addressHint;
  late RxString cityHint;
  late RxString postcodeHint;
  late RxString selectedCountry;

  final RxBool isContinueEnabled = true.obs;
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  Future<UserSettingAddressPageController> init({bool? isFromAuth}) async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    userData = _authService.userData.value;

    final city = userData?.location?.locality ?? userData?.location?.subLocality;
    final state = userData?.location?.administrativeArea;
    final country = userData?.location?.regionCode;

    addressHint=(userData?.location?.addressLines?.first ?? 'userLocation').obs;
    cityHint=(userData?.location?.locality ??  userData?.location?.subLocality ?? 'userCity').obs;
    selectedCountry=(userData?.location?.administrativeArea?? 'Canada').obs;
    postcodeHint=(userData?.location?.postalCode?? 'userPostcode').obs;

    addressInputController =
        ProfileWidgetBuilder.buildAddressTextFieldController(
            theme, addressInputBox.obs,addressHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    cityInputController = ProfileWidgetBuilder.buildAddressTextFieldController(
        theme, cityInputBox.obs, cityHint
        //onCleared: onPhoneNumberCleared.obs,
        // onChanged: isInputPhoneNumberValid.obs,
        );
    postcodeInputController =
        ProfileWidgetBuilder.buildAddressTextFieldController(
            theme, postcodeInputBox.obs, postcodeHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'save.changes'.t18.obs,
      onPressed: saveChanges.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'common.button.skip'.t18.obs,
      onPressed: onCancelPressed.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  //void saveChanges() {
  Future<void> saveChanges() async {
    String address = addressInputBox.text.trim();
    String city = cityInputBox.text.trim();
    String postCode = postcodeInputBox.text.trim();
    bool isCountryUpdated = isCountrySelected.value;

    if (address.isEmpty &&
        city.isEmpty &&
        postCode.isEmpty &&
        !isCountryUpdated) {
      return;
    }

    UserData? data = UserData.copyFrom(_authService.userData.value!);
    GeoPostalAddress? l = GeoPostalAddress.create();

    if (address.isNotEmpty) {
      addressHint.value = address;
      l?.addressLines = [];
      l?.addressLines?.add(address);
      l?.addressLines?.add('test line');
    } else {
      l?.addressLines = [];
      l?.addressLines?.add(addressHint.value);
      l?.addressLines?.add('test line');
    }

    if (city.isNotEmpty) {
      cityHint.value = city;
      l?.locality=city;
    } else {
      l?.locality=cityHint.value;
    }

    l?.region=selectedCountry.value;

    if (postCode.isNotEmpty) {
      l?.postalCode = postCode;
    } else {
      l?.postalCode=postcodeHint.value;
    }

    data?.location = l;
    await _userService.updateUserData(data!);

    addressInputBox.clear();
    cityInputBox.clear();
    postcodeInputBox.clear();

    isAddressEntered.value = false;
    isCityEntered.value = false;
    isPostCodeEntered.value = false;
    isCountrySelected.value = false;

    isChangedNotificationVisible.value = true;
    Future.delayed(Duration(seconds: 3), () {
      isChangedNotificationVisible.value = false;
    });

    // Skip user address setting
    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const SelectPetPage());
    } else {
      Get.back();
    }
  }

  Future<void> onCancelPressed() async {

    // Skip user address setting
    await Storage.instance.write(StorageKeys.userDataAddressSkipped, true);
    userDataAddressSkippedThisTime = true;

    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const SelectPetPage());
    } else {
      Get.back();
    }
  }
}

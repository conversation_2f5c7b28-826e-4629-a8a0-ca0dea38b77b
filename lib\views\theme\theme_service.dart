import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';

import 'text_schemes/text_schemes_i.dart';
import 'layouts/theme_layout_i.dart';

class ThemeService extends GetxService {

  static ThemeService get instance => Get.find();

  // TODO: set the platform brightness as the default currently
  // MediaQuery.of(Get.context!).platformBrightness cannot be used here, since at the time of initialization, Get.context is null.
  // Do not use Get.isDarkMode which is the theme's brightness.

  late ThemeData themeData;
  late TextStyleExtension textStyleExtension;
  late ThemeLayout themeLayout;

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  Future<ThemeService> init() async {
    await _initializeTheme();
    return this;
  }

  /// Initialize the theme, based on the brightness and theme type
  Future<void> _initializeTheme() async {

    Brightness globalBrightness = await _loadBrightNess();
    ThemeType themeType = await _loadThemeType();
    await _loadThemes(globalBrightness, themeType);
  }

  Future<Brightness> _loadBrightNess() async {

    // TODO load from storage
    return Brightness.light;
  }

  Future<ThemeType> _loadThemeType() async {

    // TODO load from storage
    return ThemeType.oneNataClassic;
  }

  Future<void> _loadThemes(Brightness brightNess, ThemeType themeType) async {

    if (themeType == ThemeType.oneNataClassic) {

      themeData = ThemeData(
        brightness: Brightness.light,
        colorScheme: const OneNataClassicColorScheme(),
        extensions: [OneNataClassicColorExtension()],
        textTheme: OneNataClassicTextTheme(),
      );

      textStyleExtension = OneNataClassicTextStyleExtension();
      themeLayout = OneNataClassicThemeLayout();
    }
    // TODO other themes
  }
}

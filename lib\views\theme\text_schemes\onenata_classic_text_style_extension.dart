import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'text_scheme_util.dart';
import 'text_style_extension.dart';

class OneNataClassicTextStyleExtension extends TextStyleExtension {

  OneNataClassicTextStyleExtension() {

    // Common widgets
    buttonLargeFilled = TextThemeUtil.buildTextStyle(
      size: 20.sp,
      weight: FontWeight.w700,
      color: OneNataClassicColors.white,
    );

    buttonLargeOutlined = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.veronica,
    );

    buttonMedium = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.white,
    );

    buttonMedium2 = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    inputLarge = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.veronica,
    );

    inputLargeHint = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
    );

    inputLargeError = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.crimson,
    );

    mapSearchHeader = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    mapSearchHeaderHint = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.silver,
    );

    dropdownItem = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
    );

    dropdownItemSelected = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.veronica,
    );

    radioItem = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
    );

    radioItemSelected = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.veronica,
    );

    bottomItem = TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.silver,
    );

    bottomItemSelected = TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.sunset,
    );

    // Auth page
    appAvatarLarge = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.white,
    );

    appAvatarLarge2 = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.eerieBlack2,
    );

    appAvatarMedium = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack2,
    );

    appAvatarSelect = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.eerieBlack2,
    );

    appAvatarSelect2 = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    authPageTitle = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.eerieBlack2,
      height: 2.5,
    );

    authPageDesc = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
      height: 1.375,
    );

    authPageNotification = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.veronica,
      height: 1.2,
    );

    authPageWarning = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.crimson,
      height: 1.2,
    );

    authPageButton = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.white,
      // height: 1.5,
    );

    authPageDisabledButton = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.white,
      // height: 1.5,
    );

    authPageAlterButton = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.veronica,
    );

    authPageResendDisabled = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.veronica,
    );

    authPagePassTipTitle = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.eerieBlack2,
    );

    authPagePassTipBody = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    authPageResend = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.veronica,
      height: 50.sp,
    );

    authPageResendDisabled = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.silver,
      height: 3.57,
    );

    authPageInvalid = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.crimson,
    );

    authPageChangeLogin = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.veronica,
    );

    authPageForgotPassword = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.sunset,
    );

    authPageCelebration = TextStyle(
      fontSize: 24.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    authPageHintText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
      height: 1.5,
    );

    authPageInputText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.veronica,
      height: 1.5,
    );

    authPageInvalidText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.crimson,
      height: 1.5,
    );

    // Function page
    userAvatarLarge = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    userAvatarLargeTip = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.silver,
    );

    userAvatar = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    postStatisticTitle = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    postStatisticBody = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    contactTitle = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    contactInfo = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    petAvatar = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    petAvatarMore = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    petAvatarLargeName = TextStyle(
      fontSize: 22.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    petAvatarLargeIntro = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
    );

    petInfoTitle = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    petInfoBody = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    petInfoListTitle = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.silver,
    );

    petInfoListBody = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    petInfoListRemark = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    postGridItemTitle = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    postGridItemBody = TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    eventListItemDateTime = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    eventListItemBody = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    placeListItemTitle = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    placeListItemMileage = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    placeListItemDateTime = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    userProfileBody1 = TextStyle(
      fontSize: 20.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    userProfileSettingBody = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    userProfileSettingTitle = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w700,
      color: OneNataClassicColors.eerieBlack,
    );

    userProfileSettingNotification = TextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.w500,
    color: OneNataClassicColors.white,
    );

    // Geo page
    geoFilterButtonText = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    geoPlaceInfoCardTitle = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    geoPlaceInfoCardMileage = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );
    geoPlaceInfoCardRating = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    geoPlaceInfoCardBusinessStatus = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.yellowGreen,
    );
    geoPlaceInfoCardBusinessTime = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );
    geoPlaceInfoCardTag = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    geoPlaceInfoCardCheckInButtonText = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.white,
    );

    geoSearchInputText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );
    geoSearchHintText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.silver,
    );
    geoSearchInvalidText = TextStyle(
      fontSize: 18.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.crimson,
    );

    recordWeek = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.silver,
    );

    recordMonth = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    recordNextMonth = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.silver,
    );

    recordMonthSelect = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.sunset,
    );

    recordMonthUnselect = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.silver,
    );

    recordDayDisplay = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.eerieBlack,
    );

    recordAllTime = TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.silver,
    );

    recordAllType= TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    recordAllTimes = TextStyle(
      fontSize: 10.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    recordDelete = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    recordDeleteCancel = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w600,
      color: OneNataClassicColors.veronica,
    );

    recordListTime = TextStyle(
      fontSize: 12.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );

    recordListDescription = TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
      color: OneNataClassicColors.eerieBlack,
    );

    recordTimePicker = TextStyle(
      fontSize: 16.sp,
      fontWeight: FontWeight.w400,
      color: OneNataClassicColors.eerieBlack,
    );
  }
}

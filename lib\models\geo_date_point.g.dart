// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_date_point.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoDatePoint _$GeoDatePointFromJson(Map<String, dynamic> json) => GeoDatePoint(
      date: json['date'] == null
          ? null
          : GeoDate.fromJson(json['date'] as Map<String, dynamic>),
      truncated: json['truncated'] as bool?,
      day: (json['day'] as num?)?.toInt(),
      hour: (json['hour'] as num?)?.toInt(),
      minute: (json['minute'] as num?)?.toInt(),
    );

Map<String, dynamic> _$GeoDatePointToJson(GeoDatePoint instance) =>
    <String, dynamic>{
      'date': instance.date,
      'truncated': instance.truncated,
      'day': instance.day,
      'hour': instance.hour,
      'minute': instance.minute,
    };

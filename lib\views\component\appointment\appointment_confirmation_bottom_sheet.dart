import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/views/component/appointment/service_selection_bottom_sheet.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';

class AppointmentConfirmationBottomSheet extends StatelessWidget {
  final Pet selectedPet;
  final ServiceType selectedService;
  final DateTime selectedDate;
  final TimeSlot selectedTimeSlot;
  final Staff selectedStaff;
  final String notes;
  final VoidCallback? onBackToMain;

  const AppointmentConfirmationBottomSheet({
    Key? key,
    required this.selectedPet,
    required this.selectedService,
    required this.selectedDate,
    required this.selectedTimeSlot,
    required this.selectedStaff,
    required this.notes,
    this.onBackToMain,
  }) : super(key: key);

  void _backToMain() {
    Get.back();
    onBackToMain?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: double.infinity,
            alignment: Alignment.center,
            child: Container(
              width: 36.w,
              height: 4.w,
              decoration: BoxDecoration(
                color: Color(0xFFE0E0E0),
                borderRadius: BorderRadius.circular(2.w),
              ),
            ),
          ),
          
          // 成功标题
          Padding(
            padding: EdgeInsets.only(top: 32.w, left: 24.w, right: 24.w),
            child: Text(
              'Your appointment is booked!',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
          ),
          
          // 副标题
          Padding(
            padding: EdgeInsets.only(top: 8.w, left: 24.w, right: 24.w),
            child: Text(
              'We\'ll send you an email your appointment.',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: Color(0xFF666666),
              ),
            ),
          ),
          
          // 预约详情
          Expanded(
            child: Container(
              margin: EdgeInsets.all(24.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 24.w),
                  
                  // 宠物名称
                  _buildDetailRow('Pet Name:', selectedPet.name ?? 'Unknown'),
                  
                  SizedBox(height: 16.w),
                  
                  // 时间
                  _buildDetailRow(
                    'Time:', 
                    '${DateFormat('MMMM d, yyyy').format(selectedDate)} at ${selectedTimeSlot.time}'
                  ),
                  
                  SizedBox(height: 16.w),
                  
                  // 地址 (写死的示例地址)
                  _buildDetailRow(
                    'Location:', 
                    '13071 Vanier Pl, Richmond, BC, V6V2J1'
                  ),
                  
                  SizedBox(height: 16.w),
                  
                  // 服务
                  _buildDetailRow('Service:', selectedService.name),
                  
                  SizedBox(height: 16.w),
                  
                  // 价格
                  _buildDetailRow('Price:', 'CA \$${selectedService.price.toStringAsFixed(0)}'),
                  
                  SizedBox(height: 16.w),
                  
                  // 预计时间
                  _buildDetailRow('Estimated Time:', '${selectedService.durationMinutes} min'),
                  
                  SizedBox(height: 16.w),
                  
                  // 工作人员
                  _buildDetailRow('Staff:', selectedStaff.name),
                  
                  // 备注 (如果有的话)
                  if (notes.isNotEmpty) ...[
                    SizedBox(height: 16.w),
                    _buildDetailRow('Notes:', notes),
                  ],
                ],
              ),
            ),
          ),
          
          // Back to main 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: _backToMain,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFFF2D3A4),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Back to main',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        SizedBox(height: 4.w),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: Color(0xFF666666),
          ),
        ),
      ],
    );
  }
}

/// 显示预约确认底部弹窗的静态方法
class AppointmentConfirmationBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    required ServiceType selectedService,
    required DateTime selectedDate,
    required TimeSlot selectedTimeSlot,
    required Staff selectedStaff,
    required String notes,
    VoidCallback? onBackToMain,
  }) {
    Get.bottomSheet(
      AppointmentConfirmationBottomSheet(
        selectedPet: selectedPet,
        selectedService: selectedService,
        selectedDate: selectedDate,
        selectedTimeSlot: selectedTimeSlot,
        selectedStaff: selectedStaff,
        notes: notes,
        onBackToMain: onBackToMain,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}

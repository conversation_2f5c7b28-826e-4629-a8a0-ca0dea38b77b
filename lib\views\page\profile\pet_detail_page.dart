import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/models/pet_growing_record.dart';
import 'package:onenata_app/views/component/profile/profile_widget_builder.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../common/const/image_asset_const.dart';
import '../../../common/utils/date_time_util.dart';
import '../../../models/pet.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/service_response.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../page_interceptor.dart';

class PetDetailPage extends StatefulWidget {
  const PetDetailPage({super.key});

  @override
  PetDetailPageState createState() => PetDetailPageState();
}

class PetDetailPageState extends State<PetDetailPage> {
  late Future<PetDetailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => PetDetailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PetDetailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  //_buildWhiteSection1(),
                  ProfileWidgetBuilder.buildPetDetailWhiteSection1(),
                  //_buildBackButton(controller),
                  CommonBackButton(
                    onPressed: () {
                      //controller.clearStepInput(controller.currentStep.value);
                      if (controller.currentStep.value > 1) {
                        controller.currentStep.value -= 1;
                      } else {
                        Get.back();
                      }
                    },
                  ),
                  ProfileWidgetBuilder.buildPetDetailTopSection(
                    theme: controller.theme,
                    currentStep: controller.currentStep,
                  ),
                  ProfileWidgetBuilder.buildPetStepIndicator(
                    theme: controller.theme,
                    currentStep: controller.currentStep,
                  ),
                  ProfileWidgetBuilder.buildPetProgressIndicator(
                    theme: controller.theme,
                    currentStep: controller.currentStep,
                  ),
                  _buildContent(controller),
                  ProfileWidgetBuilder.buildPetDetailWhiteSection2(),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.buttonController,
                    721,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.alterButtonController,
                    781,
                  ),
                  // ProfileWidgetBuilder.buildSubmitSection(
                  //     theme: controller.theme,
                  //     //isEnabled: controller.isContinueEnabled,
                  //     isEnabled: true.obs,
                  //     submitButtonController: controller.submitButtonController,
                  //     skipButtonController: controller.skipButtonController,
                  //     onContinuePressed: controller.onContinuePressed,
                  //     onSkipPressed: controller.onSkipPressed,
                  //     submitButtonText: 'continue'.t18,
                  //     skipButtonText: 'skip'.t18),
                ],
              ),
            );
          }
        });
  }

  Widget _buildContent(PetDetailPageController controller) {
    return Obx(() {
      final theme = controller.theme;

      if (controller.currentStep.value == 1) {
        return _buildPetBreedInput(
          controller: controller,
          theme: theme,
          textController: controller.breedInputBox,
          value: controller.petBreed,
          onChanged: (val) {
            controller.petBreed.value = val;
            controller.validateInput();
          },
        );
      } else if (controller.currentStep.value == 2) {
        return _buildPetNameInput(
          controller: controller,
          theme: theme,
          value: controller.petName,
          onChanged: (val) {
            controller.petName.value = val;
            controller.validateInput();
          },
          onImageTap: () {
            print("Tap to change pet image");
          },
        );
      } else if (controller.currentStep.value == 3) {
        return _buildPetWeightInput(
          controller: controller,
          theme: theme,
          value: controller.petWeight,
          onChanged: (val) {
            controller.petWeight.value = val;
            controller.validateInput();
          },
        );
      } else if (controller.currentStep.value == 4) {
        return _buildPetBirthdayInput(
          controller: controller,
          theme: theme,
          value: controller.petBirthday,
          onChanged: (val) {
            controller.petBirthday.value = val;
            controller.validateInput();
          },
        );
      } else if (controller.currentStep.value == 5) {
        return _buildPetGenderInput(
          controller: controller,
          theme: theme,
          selectedGender: controller.petGender,
          onChanged: (val) {
            controller.petGender.value = val;
            controller.validateInput();
          },
        );
      } else if (controller.currentStep.value == 6) {
        return _buildPetCountryInput(
          controller: controller,
          theme: theme,
          selectedCountry: controller.selectedCountry,
          onChanged: (val) {
            controller.selectedCountry.value = val;
            controller.validateInput();
          },
        );
      } else {
        return const SizedBox.shrink(); // 默认空视图
      }
    });
  }

  //step 1
  Widget _buildPetBreedInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required TextEditingController textController,
    required RxString value,
    required Function(String) onChanged,
    double top = 260,
    double left = 14,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 186.w,
                      height: 186.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.themeData.colorScheme.tertiary,
                      ),
                      child: Icon(
                        Icons.image,
                        size: 50,
                        color: theme.themeData.colorScheme.secondary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 36.w),
                CommonText(
                  "breed.text".t18,
                  theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                ProfileWidgetBuilder.buildSettingInputBox(
                  controller.theme,
                  controller.breedInputController,
                  0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  //step2
  Widget _buildPetNameInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required RxString value,
    required Function(String) onChanged,
    required VoidCallback onImageTap,
    double top = 260,
    double left = 14,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    _buildPetAvatar(controller),
                    // Container(
                    //   width: 186.w,
                    //   height: 186.w,
                    //   child: ClipOval(
                    //     child: Image.asset(
                    //       "assets/images/pet_placeholder.png",
                    //       width: 180.w,
                    //       height: 180.w,
                    //       fit: BoxFit.cover,
                    //     ),
                    //   ),
                    // ),
                    // Align(
                    //   alignment: Alignment.bottomCenter,
                    //   child: Padding(
                    //     padding: EdgeInsets.only(bottom: 12.w),
                    //     child: GestureDetector(
                    //       onTap: onImageTap,
                    //       child: Container(
                    //         width: 36.w,
                    //         height: 36.w,
                    //         decoration: const BoxDecoration(
                    //           shape: BoxShape.circle,
                    //           color: Colors.white,
                    //         ),
                    //         child: Icon(
                    //           Icons.camera_alt,
                    //           color: theme.themeData.colorScheme.secondary,
                    //           size: 24.sp,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
                SizedBox(height: 36.w),
                Text(
                  "name.text".t18,
                  style: theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                ProfileWidgetBuilder.buildSettingInputBox(
                  controller.theme,
                  controller.nameInputController,
                  0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  //step3
  Widget _buildPetWeightInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required RxString value,
    required Function(String) onChanged,
    double top = 240,
    double left = 14,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              children: [
                FutureBuilder<Widget>(
                  future: _buildAvatar(controller),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator(); // or SizedBox(height: 125.w);
                    } else if (snapshot.hasError) {
                      return Text('Error loading avatar');
                    } else {
                      return snapshot.data!;
                    }
                  },
                ),
                SizedBox(height: 36.w),
                Text(
                  "weight.text".t18,
                  style: theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                ProfileWidgetBuilder.buildSettingInputBox(
                  controller.theme,
                  controller.weightInputController,
                  0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  //step4
  Widget _buildPetBirthdayInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required RxString value,
    required Function(String) onChanged,
    double top = 240,
    double left = 14,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              children: [
                FutureBuilder<Widget>(
                  future: _buildAvatar(controller),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator(); // or SizedBox(height: 125.w);
                    } else if (snapshot.hasError) {
                      return Text('Error loading avatar');
                    } else {
                      return snapshot.data!;
                    }
                  },
                ),
                SizedBox(height: 36.w),
                Text(
                  "birthday.text".t18,
                  style: theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                // ProfileWidgetBuilder.buildSettingInputBox(
                //   controller.theme,
                //   controller.birthdayInputController,
                //   0,
                // ),
                ProfileWidgetBuilder.buildDatePicker(
                  theme: controller.theme,
                  selectedDate: controller.birthdayDate,
                  onChanged: () {
                    final picked = controller.birthdayDate.value;
                    if (picked.isAfter(DateTime.now())) {
                      controller.birthdayDate.value = DateTime.now();
                      //CommonToast.show("Date cannot be in the future");
                    } else {
                      controller.petBirthday.value = picked.toIso8601String();
                    }
                    controller.validateInput();
                  },
                ),

              ],
            ),
          ),
        ),
      ],
    );
  }

  //step5
  Widget _buildPetGenderInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required RxString selectedGender,
    required Function(String) onChanged,
    double top = 240,
    double left = 14,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              children: [
                FutureBuilder<Widget>(
                  future: _buildAvatar(controller),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator(); // or SizedBox(height: 125.w);
                    } else if (snapshot.hasError) {
                      return Text('Error loading avatar');
                    } else {
                      return snapshot.data!;
                    }
                  },
                ),
                SizedBox(height: 36.w),
                Text(
                  "gender.text".t18,
                  style: theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ProfileWidgetBuilder.buildGenderButton(
                      theme,
                      selectedGender,
                      PetGender.boy.t18key.t18,
                      Icons.male,
                      onChanged,
                    ),
                    SizedBox(width: 16.w),
                    ProfileWidgetBuilder.buildGenderButton(
                      theme,
                      selectedGender,
                      PetGender.girl.t18key.t18,
                      Icons.female,
                      onChanged,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  //step6
  Widget _buildPetCountryInput({
    required PetDetailPageController controller,
    required ThemePlugin theme,
    required RxString selectedCountry,
    required Function(String) onChanged,
    double top = 240,
    double left = 14,
  }) {
    final List<Map<String, String>> countries = [
      {"name": "Canada", "flag": "🇨🇦"},
      {"name": "U.S.", "flag": "🇺🇸"},
      {"name": "China", "flag": "🇨🇳"},
    ];
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: left.w),
          child: SizedBox(
            width: 375.w,
            child: Column(
              children: [
                FutureBuilder<Widget>(
                  future: _buildAvatar(controller),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator(); // or SizedBox(height: 125.w);
                    } else if (snapshot.hasError) {
                      return Text('Error loading avatar');
                    } else {
                      return snapshot.data!;
                    }
                  },
                ),
                SizedBox(height: 36.w),
                Text(
                  "country.text".t18,
                  style: theme.textStyleExtension.appAvatarSelect2,
                ),
                SizedBox(height: 24.w),
                Obx(() {
                  // 将选中的国家移到第一位，其余按原顺序排列
                  final List<Map<String, String>> sortedCountries = [
                    if (selectedCountry.value.isNotEmpty)
                      ...countries
                          .where((c) => c["name"] == selectedCountry.value),
                    ...countries
                        .where((c) => c["name"] != selectedCountry.value),
                  ];
                  return Container(
                    width: 327.w,
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: theme.themeData.colorScheme.secondary,
                        width: 1.w,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: selectedCountry.value.isNotEmpty
                            ? selectedCountry.value
                            : null,
                        hint: Row(
                          children: [
                            SizedBox(width: 10.w),
                            Text(
                              "country.hind".t18,
                              style: theme.textStyleExtension.dropdownItem,
                            ),
                          ],
                        ),
                        isExpanded: true,
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          color: theme.themeData.colorScheme.secondary,
                        ),
                        items: sortedCountries.map((country) {
                          return DropdownMenuItem<String>(
                            value: country["name"],
                            child: Row(
                              children: [
                                Text(
                                  country["flag"]!,
                                  style: TextStyle(fontSize: 20.sp),
                                ),
                                SizedBox(width: 10.w),
                                Text(
                                  country["name"]!,
                                  style: selectedCountry.value ==
                                      country["name"]
                                      ? theme.textStyleExtension
                                      .dropdownItemSelected
                                      : theme.textStyleExtension.dropdownItem,
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            onChanged(newValue);
                          }
                        },
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPetAvatar(PetDetailPageController controller) {
    return GestureDetector(
      onTap: controller.pickPetImage,
      child: Obx(() => ProfileWidgetBuilder.buildAvatar1(
        controller.theme,
        avatar: controller.pageHeaderPetAvatar.value,
        editable: true,
      )),
    );
  }

  Future<Widget> _buildAvatar(PetDetailPageController controller) async {
    return Column(
      children: [
        Center(
          child: GestureDetector(
            child: AuthWidgetBuilder.buildAvatar(
              controller.theme,
              avatar: await ProfileWidgetBuilder.buildPetAvatar(
                  controller.theme,
                  userId: controller._authService.userAccount.value!.sid!,
                  petId: controller.pet.sid!,
                  avatar: controller.pet.avatar,
                  size: 125
              ),
            ),
          ),
        ),
      ],
    );
  }
// Future<Widget> _buildAvatar(PetDetailPageController controller) async {
//     return Column(
//       children: [
//         SizedBox(height: 90.w), // 原来的 top 距离
//         Center(
//           child: GestureDetector(
//             onTap: controller.pickImage,
//             child: await ProfileWidgetBuilder.buildPetAvatar(
//                 controller.theme,
//                 userId: controller.pet.owner,
//                 petId: controller.pet.sid!,
//                 avatar: (controller._authService.ownedPets.value != null && controller. _authService.ownedPets.value!.isNotEmpty)
//                     ? controller._authService.ownedPets.value?.first.avatar
//                     : '03a7dd46-c49f-459d-ba94-9c4e36908ab2.png'
//             ),
//           ),
//         ),
//       ],
//     );
//   }
}

class PetDetailPageController extends GetxController {
  final PetService _petService = PetService();
  final AuthService _authService = AuthService.instance;
  late Pet pet;
  late PetGrowingRecord petGrowingRecord;

  final RxInt currentStep = 1.obs; // 当前步骤 (1-6)
  final RxString petBreed = ''.obs;
  final RxString petName = ''.obs;
  final RxString petWeight = ''.obs;
  final RxString petBirthday = ''.obs;
  final RxString petGender = "".obs;
  final RxString selectedCountry = 'Canada'.obs;
  final RxBool isContinueEnabled = false.obs;
  final String petType = Get.arguments['selectedPet'];
  final Rx<Widget?> petAvatar = Rx<Widget?>(null);
  Rx<Widget?> pageHeaderPetAvatar = Rx<Widget?>(null);

  final RegExp nameRegex = RegExp(r'^[a-zA-Z0-9]+$');
  final RegExp weightRegex = RegExp(r'^\d+(\.\d{1,2})?$');

  final TextEditingController breedInputBox = TextEditingController();
  late CommonTextField3Controller breedInputController;
  final TextEditingController nameInputBox = TextEditingController();
  late CommonTextField3Controller nameInputController;
  final TextEditingController weightInputBox = TextEditingController();
  late CommonTextField3Controller weightInputController;
  final TextEditingController birthdayInputBox = TextEditingController();
  late CommonTextField3Controller birthdayInputController;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  // Theme plugin
  late ThemePlugin theme;
  late final CommonButtonController submitButtonController;
  late final CommonButtonController skipButtonController;
  Rx<DateTime> birthdayDate = DateTime.now().obs;


  Future<PetDetailPageController> init() async {

    await PageInterceptor.pageAuthCheck();
    birthdayDate.value = DateTime.now();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    breedInputBox.addListener(() {
      petBreed.value = breedInputBox.text;
      validateInput();
    });

    nameInputBox.addListener(() {
      petName.value = nameInputBox.text;
      validateInput();
    });

    weightInputBox.addListener(() {
      petWeight.value = weightInputBox.text;
      validateInput();
    });

    birthdayInputBox.addListener(() {
      petBirthday.value = birthdayInputBox.text;
      validateInput();
    });
    breedInputController = ProfileWidgetBuilder.buildBreedTextFieldController(
      theme, breedInputBox.obs, "breed.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    nameInputController = ProfileWidgetBuilder.buildNameTextFieldController(
      theme, nameInputBox.obs, "name.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    weightInputController = ProfileWidgetBuilder.buildWeightTextFieldController(
      theme, weightInputBox.obs, "weight.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    birthdayInputController =
        ProfileWidgetBuilder.buildBirthdayTextFieldController(
          theme, birthdayInputBox.obs, "birthday.hind".t18.obs,
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    buttonController = CommonButton3Controller(
      //isEnabled: true.obs,
      isEnabled: isContinueEnabled,
      text: 'continue'.t18.obs,
      //onPressed: onContinuePressed.obs,
      onPressed: Rx(() async {
        await onContinuePressed();
      }),
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'skip'.t18.obs,
      onPressed: onSkipPressed.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    validateInput();

    pet = Pet.create(
      owner: _authService.userAccount.value!.sid!,
      name: 'default_name',
      //breed: breedInputBox.text,
      //type: {
      //'cat': PetType.cat,
      //'dog': PetType.dog,
      //'other': PetType.others,
      //}[petType],
      //gender: {
      //'Male': PetGender.boy,
      //'Female': PetGender.girl,
      //}[petGender.value],
      //avatar:,
      //birthday: _parseBirthday(birthdayInputBox.text),
    );
    //await _petService.addOwnedPet(pet);

    pageHeaderPetAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: _authService.userAccount.value!.sid!,
        petId: pet.sid!,
        avatar: pet.avatar,
        size: 186
    );

    return this;
  }

  void validateInput() {
    bool isValid = false;

    switch (currentStep.value) {
      case 1:
        isValid = petBreed.value.isNotEmpty;
        break;
      case 2:
        isValid = petName.value.isNotEmpty && nameRegex.hasMatch(petName.value);
        break;
      case 3:
        isValid =
            petWeight.value.isNotEmpty && weightRegex.hasMatch(petWeight.value);
        break;
      case 4:
        final birthday = birthdayDate.value;
        final today = DateTime.now();
        isValid = birthday.isBefore(today);
        break;
      // case 4:
      //   try {
      //     DateFormat format = DateFormat('dd/MM/yy');
      //     DateTime enteredDate = format.parseStrict(petBirthday.value);
      //     DateTime today = DateTime.now();
      //     isValid = enteredDate.isBefore(today);
      //   } catch (e) {
      //     isValid = false;
      //   }
      //   break;
      case 5:
        isValid = petGender.value.isNotEmpty;
        break;
      case 6:
        isValid = selectedCountry.value.isNotEmpty;
        break;
    }
    isContinueEnabled.value = isValid;
    buttonController.isEnabled.value = isValid;
  }

  void clearStepInput(int step) {
    switch (step) {
      case 1:
      //petBreedController.text = "";
        breedInputBox.text = "";
        petBreed.value = "";
        break;
      case 2:
        nameInputBox.text = "";
        petName.value = "";
        break;
      case 3:
      //petWeightController.text = "";
        weightInputBox.text = "";
        petWeight.value = "";
        break;
      case 4:
      //petBirthdayController.text = "";
        birthdayInputBox.text = "";
        petBirthday.value = "";
        break;
      case 5:
        petGender.value = "";
        break;
      case 6:
        selectedCountry.value = "";
        break;
    }
  }

  Future<void> onContinuePressed() async {
    //clearStepInput(currentStep.value);
    if (currentStep.value < 6) {
      // print(petBreed);
      // print(petName);
      // print(petGender);
      // print(petType);
      // print(petWeight);
      // print(petBirthday);
      currentStep.value++;
      validateInput();
    } else {
      pet.type = {
        PetType.cat.t18key.t18: PetType.cat,
        PetType.dog.t18key.t18: PetType.dog,
        PetType.others.t18key.t18: PetType.others,
      }[petType];
      pet.breed = breedInputBox.text;
      pet.name = nameInputBox.text;
      pet.gender = {
        PetGender.boy.t18key.t18: PetGender.boy,
        PetGender.girl.t18key.t18: PetGender.girl,
      }[petGender.value];
      //pet.birthday =// _parseBirthday(birthdayInputBox.text);
      pet.birthday=birthdayDate.value.millisecondsSinceEpoch;

      PetGrowingRecord r = PetGrowingRecord.create(
          uid: pet.owner,
          pid: pet.sid!,
          weight: double.parse(
            double.parse(petWeight.value).toStringAsFixed(2),
          ));
      await _petService.recordPetGrowing(r);
      //print(pet);
      await _petService.addOwnedPet(pet);
      await Get.toNamed('/celebration', arguments: pet);
      //await Get.toNamed('/celebration');
    }
  }

  int? _parseBirthday(String text) {
    try {
      DateTime date = DateFormat('dd/MM/yy').parseStrict(text);
      return DateTimeUtil.dayInMilliseconds(date.year, date.month, date.day);
    } catch (_) {
      return null;
    }
  }

  Future<void> onSkipPressed() async {
    //await Get.toNamed('/celebration');
    if (currentStep.value < 6) {
      currentStep.value++;
      validateInput();
    } else {
      await Get.toNamed('/celebration');
    }
  }

  Future<void> pickPetImage() async {
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      String pid = pet.sid!;
      ServiceResponse<String> res = await _petService.uploadPetAvatar(uid: uid, pid: pid, image: image);
      if (res.code == 200) {
        pageHeaderPetAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
            theme,
            userId: uid,
            petId: pid,
            avatar: res.data,
            size: 186
        );
      }
    }
  }

}

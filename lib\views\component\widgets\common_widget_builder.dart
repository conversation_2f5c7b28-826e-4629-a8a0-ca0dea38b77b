import 'package:flutter/material.dart';

import '../progress/progress_i.dart';

class CommonWidgetBuilder {

  /// Build image from firebase storage
  /// [path] is the path of the image in firebase storage including path + name
  static Widget buildImageFromFirebaseStorage(String path) {
    return Image.network(
      path,
      // "${Env.firebaseStorageHost}/$path",
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(child: LoadingWidget());
      },
      errorBuilder: (context, error, stackTrace) {
        return Icon(Icons.error);
      },
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/input_validators_util.dart';
import 'package:path/path.dart';
import '../../../common/plugin/logger/logger.dart';
import 'common_bar.dart';

class CommonTextField4 extends StatelessWidget {

  final CommonTextField4Controller controller;

  const CommonTextField4({

    super.key,
    required this.controller,
  }) ;

  @override
  Widget build(BuildContext context) {

    Widget? bg = controller.hasShadow.isFalse ?
    SizedBox.shrink() :
    CommonBar(
      width: controller.width.value,
      height: controller.height.value,
      circular: controller.circular.value,
      hasShadow: true,
      shadowColor: controller.shadowColor.value,
      shadowOffset: controller.shadowOffset.value,
      shadowBlurRadius: controller.shadowBlurRadius.value,
      shadowSpreadRadius: controller.shadowSpreadRadius.value,
    );

    Widget textField = SizedBox(
      width: controller.width.value,
      height: controller.height.value,
      child: Obx(()=>
          TextField(
            key: controller.globalKey.value,
            controller: controller.textEditingController.value,
            focusNode: controller.textFieldFocusNode,
            keyboardType: controller.keyboardType.value,
            obscureText: controller.keyboardType.value == TextInputType.visiblePassword ? controller.obscureText.value : false,
            enabled: true,
            readOnly: !controller.isEnabled.value,
            inputFormatters: controller.inputFormatters.value,
            onChanged: (value)=> controller.onTextChanged(value, context),
            style: controller.isEnabled.isTrue ? (controller.hasMistake.isTrue ? controller.mistakeTextStyle.value : controller.textStyle.value) : controller.disabledTextStyle.value,
            showCursor: controller.showCursor.value,
            decoration: InputDecoration(
              hintText: controller.hintText.value,
              hintStyle: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value : controller.hintStyle.value,
              fillColor: controller.fillColor.value,
              filled: controller.isFilled.value,
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Clear icon
                  if (controller.isFocused.value && controller.enableClearIcon.value)
                    GestureDetector(
                      onTap: () {
                        controller.textEditingController.value!.clear();
                        if (controller.onCleared.value != null) controller.onCleared.value!(context);
                      },
                      child: Icon(Icons.close, size: controller.passwordVisibilityIconSize.value, color: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value.color : controller.isFocused.value ? controller.textStyle.value.color : controller.iconColor.value),
                    ),
                  if (controller.isFocused.value && controller.enablePasswordVisibilityIcon.value) SizedBox(width: controller.passwordVisibilityIconSize.value / 4), // ✅ 只有密码时才加间距
                  // Password visibility icon
                  if (controller.isFocused.value && controller.enablePasswordVisibilityIcon.value) // ✅ 只有在有输入时，才显示眼睛 Icon
                    GestureDetector(
                      onTap: controller.togglePasswordVisibility,
                      child: Icon(
                        controller.obscureText.value
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                        size: controller.passwordVisibilityIconSize.value,
                        color: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value.color! : controller.isFocused.value ? controller.textStyle.value.color : controller.iconColor.value,
                      ),
                    ),
                  if (controller.isFocused.value && controller.enableClearIcon.value) SizedBox(width: controller.passwordVisibilityIconSize.value * 2 / 3,)
                ],
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: controller.hasRadius.value ? BorderRadius.circular(controller.circular.value) : BorderRadius.zero,
                borderSide: BorderSide(
                  color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.borderColor.value,
                  width: controller.borderWidth.value,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(controller.circular.value),
                borderSide: BorderSide(
                  color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.focusBorderColor.value,
                  width: controller.borderWidth.value,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(controller.circular.value),
                borderSide: BorderSide(
                  color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.disabledBorderColor.value,
                  width: controller.borderWidth.value,
                ),
              ),
              contentPadding: EdgeInsets.symmetric(
                  horizontal: controller.textPaddingHorizontal.value,
                  vertical: controller.textPaddingVertical.value
              ),
            ),
            maxLines: null,
            minLines: 5,
            expands: false,
          ),
      ),
    );

    return Stack(
      alignment: Alignment.center,
      children: [
        bg?? SizedBox.shrink(),
        textField,
      ],
    );
  }
}

class CommonTextField4Controller {

  var globalKey = Rx<GlobalKey?>(null);
  final FocusNode textFieldFocusNode = FocusNode();

  var textEditingController = Rx<TextEditingController?>(null);
  var keyboardType = TextInputType.text.obs;

  var isEnabled = true.obs;
  var isFocused = false.obs;
  var obscureText = false.obs;
  var hasMistake = false.obs;
  var enablePasswordVisibilityIcon = false.obs;

  // Common properties
  var width = 327.w.obs;
  var height = 50.w.obs;
  var hasRadius = true.obs;
  var circular = 10.w.obs;

  // Bg properties
  var hasShadow = false.obs;
  var shadowColor = Colors.black12.obs;
  var shadowOffset = Offset(0, 1.w).obs;
  var shadowBlurRadius = 1.w.obs;
  var shadowSpreadRadius = 0.0.obs;

  // TextField properties
  var hintText = ''.obs;
  var inputFormatters = Rx<List<TextInputFormatter>?>(null);
  var borderWidth = 1.w.obs;
  var showCursor = true.obs;
  var isFilled = true.obs;
  var fillColor = Colors.white.obs;
  var fillColorOpacity = 0.0.obs;
  var textPaddingHorizontal = 10.w.obs;
  var textPaddingVertical = 10.w.obs;

  // Icon properties
  var enableClearIcon = false.obs;
  var clearIconSize = 12.w.obs;
  var passwordVisibilityIconSize = 24.w.obs;

  var iconColor = Colors.black12.obs;
  var borderColor = Colors.black12.obs;
  var focusBorderColor = Colors.black12.obs;
  var mistakeBorderColor = Colors.black12.obs;
  var disabledBorderColor = Colors.black12.obs;
  var hintStyle = TextStyle().obs;
  var textStyle = TextStyle().obs;
  var mistakeTextStyle = TextStyle().obs;
  var disabledTextStyle = TextStyle().obs;

  // Callbacks
  var onCleared = Rx<AsyncCallbackWithContext?>(null);
  var onChanged = Rx<AsyncCallbackWithContextAndResult?>(null);
  var onTogglePasswordVisibility = Rx<VoidCallback?>(null);

  // business vars
  var areaCode = '+1'.obs;

  CommonTextField4Controller({
    Rx<GlobalKey?>? globalKey,
    Rx<TextEditingController?>? textEditingController,
    Rx<TextInputType>? keyboardType,
    RxBool? isEnabled,
    RxBool? isFocused,
    RxBool? obscureText,
    RxBool? hasMistake,
    RxBool? enablePasswordVisibilityIcon,

    RxDouble? width,
    RxDouble? height,
    RxBool? hasRadius,
    RxDouble? circular,

    RxBool?  hasShadow,
    Rx<Color>? shadowColor,
    Rx<Offset>? shadowOffset,
    RxDouble? shadowBlurRadius,
    RxDouble? shadowSpreadRadius,

    RxString? hintText,
    Rx<List<TextInputFormatter>>? inputFormatters,
    RxDouble? borderWidth,
    RxBool? showCursor,
    RxBool? isFilled,
    Rx<Color>? fillColor,
    RxDouble? fillColorOpacity,
    RxDouble? textPaddingHorizontal,
    RxDouble? textPaddingVertical,

    RxBool? enableClearIcon,
    RxDouble? clearIconSize,
    RxDouble? passwordVisibilityIconSize,

    Rx<Color>? iconColor,
    Rx<Color>? borderColor,
    Rx<Color>? focusBorderColor,
    Rx<Color>? mistakeBorderColor,
    Rx<Color>? disabledBorderColor,
    Rx<TextStyle>? hintStyle,
    Rx<TextStyle>? textStyle,
    Rx<TextStyle>? mistakeTextStyle,
    Rx<TextStyle>? disabledTextStyle,

    Rx<AsyncCallbackWithContext?>? onCleared,
    Rx<AsyncCallbackWithContextAndResult?>? onChanged,
    Rx<VoidCallback?>? onTogglePasswordVisibility,

  }):
        globalKey = globalKey?? GlobalKey().obs,
        textEditingController = textEditingController?? TextEditingController().obs,
        keyboardType = keyboardType?? TextInputType.text.obs,
        isEnabled = isEnabled?? true.obs,
        isFocused = isFocused?? false.obs,
        obscureText = obscureText?? false.obs,
        hasMistake = hasMistake?? false.obs,
        enablePasswordVisibilityIcon = enablePasswordVisibilityIcon?? false.obs,
        width = width?? 327.w.obs,
        height = height?? 50.w.obs,
        hasRadius = hasRadius?? true.obs,
        circular = circular?? 10.w.obs,
        hasShadow = hasShadow?? false.obs,
        shadowColor = shadowColor?? Colors.black12.obs,
        shadowOffset = shadowOffset?? Offset(0, 1.w).obs,
        shadowBlurRadius = shadowBlurRadius?? 1.w.obs,
        shadowSpreadRadius = shadowSpreadRadius?? 0.0.obs,
        hintText = hintText?? ''.obs,
        inputFormatters = inputFormatters?? Rx<List<TextInputFormatter>?>(null),
        borderWidth = borderWidth?? 1.w.obs,
        showCursor = showCursor?? true.obs,
        isFilled = isFilled?? true.obs,
        fillColor = fillColor?? Colors.white.obs,
        fillColorOpacity = fillColorOpacity?? 0.0.obs,
        textPaddingHorizontal = textPaddingHorizontal?? 10.w.obs,
        textPaddingVertical = textPaddingVertical?? 10.w.obs,
        enableClearIcon = enableClearIcon?? false.obs,
        clearIconSize = clearIconSize?? 12.w.obs,
        passwordVisibilityIconSize = passwordVisibilityIconSize?? 24.w.obs,
        iconColor = iconColor?? Colors.black12.obs,
        borderColor = borderColor?? Colors.black12.obs,
        focusBorderColor = focusBorderColor?? Colors.black12.obs,
        mistakeBorderColor = mistakeBorderColor?? Colors.black12.obs,
        disabledBorderColor = disabledBorderColor?? Colors.black12.obs,
        hintStyle = hintStyle?? TextStyle().obs,
        textStyle = textStyle?? TextStyle().obs,
        mistakeTextStyle = mistakeTextStyle?? TextStyle().obs,
        disabledTextStyle = disabledTextStyle?? TextStyle().obs,
        onCleared = onCleared?? Rx<AsyncCallbackWithContext?>(null),
        onChanged = onChanged?? Rx<AsyncCallbackWithContextAndResult?>(null),
        onTogglePasswordVisibility = onTogglePasswordVisibility?? Rx<VoidCallback?>(null)
  ;


  //
  // @override
  // void onInit() {
  //   super.onInit();
  //
  //   // ✅ Listen for focus changes and update reactive variable
  //   textFieldFocusNode.addListener(() {
  //     isFocused.value = textFieldFocusNode.hasFocus;
  //   });
  // }
  //
  // @override
  // void onClose() {
  //   textFieldFocusNode.dispose(); // ✅ Dispose FocusNode
  //   super.onClose();
  // }

  void onTextChanged(String value, BuildContext context) {


    logger.d('onTextChanged: ${isFocused.value}');

    // isFocused.value = textFieldFocusNode.hasFocus;
    isFocused.value = true;

    logger.d('onTextChanged: ${isFocused.value}');
    logger.d('eye icon: ${enablePasswordVisibilityIcon.value}');

    if (hasMistake.value) {
      hasMistake.value = false;
    }

    // Validate phone number
    if (keyboardType.value == TextInputType.phone) {
      if (areaCode.value == '+1') {

        // Not a valid phone number
        if (value.length != 10 || !RegExp(InputUtil.phoneNumberArea1Digit).hasMatch(value)) {

          if (value.isNotEmpty) {
            textEditingController.value!.text = InputUtil.toOriginPhoneNumber(
                phoneNumber: textEditingController.value!.text);
          }

          if(onChanged.value != null) onChanged.value!(context: context, result: false);
        }
        // Valid phone number, display in format
        else {

          textEditingController.value!.text = InputUtil.toDisplayPhoneNumber(phoneNumber: value); // ✅ Trims excess input
          // textEditingController.value!.selection = TextSelection.fromPosition(
          //   TextPosition(offset: 14),
          // );

          if(onChanged.value != null) onChanged.value!(context: context, result: true);
        }

      }
      else if (areaCode.value == '+86') {

        // Not a valid phone number
        if ( value.length != 11 || !RegExp(InputUtil.phoneNumberArea86Digit).hasMatch(value)) {
          //
          // if (value.isNotEmpty) {
          //   textEditingController.value!.text =
          //       value.substring(0, value.length - 1);
          // }
          if (value.isNotEmpty) {
            textEditingController.value!.text = InputUtil.toOriginPhoneNumber(
                phoneNumber: textEditingController.value!.text);
          }

          if(onChanged.value != null) onChanged.value!(context: context, result: false);
        }
        // Valid phone number, display in format
        else {

          textEditingController.value!.text = InputUtil.toDisplayPhoneNumber(phoneNumber: value, areaCode: areaCode.value); // ✅ Trims excess input
          // textEditingController.value!.selection = TextSelection.fromPosition(
          //   TextPosition(offset: 14),
          // );

          if(onChanged.value != null) onChanged.value!(context: context, result: true);
        }
      }
    }
    // Validate OTP code
    else if (keyboardType.value == TextInputType.number) {

      if (!RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(value)) {

        // textEditingController.value!.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.value!.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.value!.text.length),
        // );
        if(onChanged.value != null) onChanged.value!(context: context, result: false);
      }
      else {

        if(onChanged.value != null) onChanged.value!(context: context, result: true);
      }
      //
      // if (RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(textEditingController.value!.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
    // Verify email address
    else if (keyboardType.value == TextInputType.emailAddress) {

      if (!RegExp(InputUtil.validEmailAddressRegEx).hasMatch(value)) {

        if(onChanged.value != null) onChanged.value!(context: context, result: false);
        // textEditingController.value!.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.value!.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.value!.text.length),
        // );
      } else {
        if(onChanged.value != null) onChanged.value!(context: context, result: true);
      }

      //
      // if (RegExp(InputUtil.validEmailAddressRegEx).hasMatch(textEditingController.value!.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
    // Verify password
    else if (keyboardType.value == TextInputType.visiblePassword) {

      if (!RegExp(InputUtil.validPasswordRegEx).hasMatch(value)) {

        if(onChanged.value != null) onChanged.value!(context: context, result: false);
        // textEditingController.value!.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.value!.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.value!.text.length),
        // );
      } else {
        if(onChanged.value != null) onChanged.value!(context: context, result: true);
      }
      //
      // if (RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(textEditingController.value!.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
  }

  void toggleMistakeState() {
    hasMistake.value = !hasMistake.value;
  }

  void togglePasswordVisibility() {
    obscureText.value = !obscureText.value;
  }

  void resetTextField() {
    hasMistake.value = false;
    textEditingController.value!.clear();
  }

  void toggleEnabled() {
    isEnabled.value = !isEnabled.value;
  }
}

import 'dart:convert';

import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/geo_i.dart';

import 'base_full_model.dart';

part 'pet_geo_record.g.dart';

@JsonSerializable()
class PetGeoRecord extends BaseFullModel {

  String? uid;
  String pid;
  String? deviceId;
  String? msgId;
  @JsonKey(fromJson: JsonUtil.geoFirePointFromJson, toJson: JsonUtil.geoFirePointToJson)
  GeoFirePoint? location;
  int? altitude;
  GeoPostalAddress? address;
  int? generateDate;
  int? sendDate;
  int? receiveDate;

  PetGeoRecord({
    this.uid,
    required this.pid,
    this.deviceId,
    this.msgId,
    this.location,
    this.altitude,
    this.address,
    this.generateDate,
    this.sendDate,
    this.receiveDate,
    super.id,
    required super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory PetGeoRecord.fromJson(Map<String, dynamic> json) => _$PetGeoRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetGeoRecordToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'address') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoPostalAddress.fromFirestoreMap(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory PetGeoRecord.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return PetGeoRecord.fromJson(jsonData);
  }

  factory PetGeoRecord.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return PetGeoRecord.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'address') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoPostalAddress.toFirestoreJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static String get collection => 'pet_geo_record';

  static create ({
    String? uid,
    required String pid,
    String? deviceId,
    String? msgId,
    GeoFirePoint? location,
    int? altitude,
    GeoPostalAddress? address,
    int? generateDate,
    int? sendDate,
    int? receiveDate,
  }) {

    return PetGeoRecord(
      sid: uuid.v4(),
      uid: uid,
      pid: pid,
      deviceId: deviceId,
      msgId: msgId,
      location: location,
      altitude: altitude,
      address: address,
      generateDate: generateDate,
      sendDate: sendDate,
      receiveDate: receiveDate,
      isValid: true,
    );
  }

  static copyFrom(PetGeoRecord other) {

    return PetGeoRecord(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      pid: other.pid,
      deviceId: other.deviceId,
      msgId: other.msgId,
      location: other.location == null
          ? null
          : GeoFirePoint(GeoPoint(other.location!.latitude, other.location!.longitude)),
      altitude: other.altitude,
      address: GeoPostalAddress.copyFrom(other.address),
      generateDate: other.generateDate,
      sendDate: other.sendDate,
      receiveDate: other.receiveDate,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

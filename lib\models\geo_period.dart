import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'geo_i.dart';

part 'geo_period.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoPeriod {

  GeoDatePoint? open;
  GeoDatePoint? close;

  GeoPeriod({
    this.open,
    this.close,
  });

  factory GeoPeriod.fromJson(Map<String, dynamic> json) => _$GeoPeriodFromJson(json);
  Map<String, dynamic> toJson() => _$GeoPeriodToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'open') {
        jsonData[key] = GeoDatePoint.fromApi(value);
      } else if (key == 'close') {
        jsonData[key] = GeoDatePoint.fromApi(value);
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoPeriod.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoPeriod.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'open') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoDatePoint.fromFirestore(value);
      } else if (key == 'close') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoDatePoint.fromFirestore(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoPeriod.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoPeriod.fromJson(jsonData);
  }

  factory GeoPeriod.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoPeriod.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'open') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoDatePoint.toFirestoreJson(value);
      } else if (key == 'close') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoDatePoint.toFirestoreJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoPeriod? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({

    GeoDatePoint? open,
    GeoDatePoint? close,
  }) {

    return GeoPeriod(
      open: open,
      close: close,
    );
  }

  static GeoPeriod copyFrom(GeoPeriod other) {
    return GeoPeriod(
      open: GeoDatePoint.copyFrom(other.open),
      close: GeoDatePoint.copyFrom(other.close),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

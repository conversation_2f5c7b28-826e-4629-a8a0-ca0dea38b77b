import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';

import '../page_interceptor.dart';
import '../root_page.dart';


class UpcomingAppointmentPage extends StatefulWidget {
  const UpcomingAppointmentPage({super.key});

  @override
  UpcomingAppointmentPageState createState() => UpcomingAppointmentPageState();
}

class UpcomingAppointmentPageState extends State<UpcomingAppointmentPage> {
  late Future<UpcomingAppointmentPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UpcomingAppointmentPageController().init(this));
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UpcomingAppointmentPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Upcoming Appointment",
                      showAvatar: false,
                    ),
                  ),
                  // Main content - positioned below top section
                  Positioned(
                    top: 120.w,
                    left: 16.w,
                    right: 16.w,
                    child: _buildAppointmentDetailCard(controller),
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.buttonController,
                    721,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.alterButtonController,
                    781,
                  ),
                ],
              ),
            );
          }
        });
  }

  // Build appointment detail card
  Widget _buildAppointmentDetailCard(UpcomingAppointmentPageController controller) {
    return Container(
      width: 371.w,
      height: 408.w,
      padding: EdgeInsets.only(
        left: 14.w,
        right: 16.w,
        top: 16.w,
        bottom: 16.w,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                width: 52.w,
                height: 52.w,
                decoration: BoxDecoration(
                  color: Color(0xFFE8B4FF),
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Icon(
                  Icons.content_cut,
                  color: Colors.white,
                  size: 24.w,
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                "Grooming @ PerfectPet",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF262626),
                ),
              ),
            ],
          ),
          SizedBox(height: 20.w),
          // Details
          _buildDetailRow("Time:", "Saturday, July 25, 2025 @ 14:00"),
          SizedBox(height: 12.w),
          _buildDetailRow("Pet:", "Miro"),
          SizedBox(height: 12.w),
          _buildDetailRow("Staff:", "Jason"),
          SizedBox(height: 12.w),
          _buildDetailRow("Price:", "C\$ 35"),
          SizedBox(height: 12.w),
          _buildDetailRow("Notes:", "Please use our own shampoo this time, thank you!"),
          SizedBox(height: 12.w),
          _buildDetailRow("Location:", "13071 Vanier Pl, Richmond, BC, V6V2J1"),
          SizedBox(height: 24.w),
          // Open in Maps button
          Center(
            child: GestureDetector(
              onTap: () => _showMapsBottomSheet(),
              child: Container(
                width: 185.w,
                height: 35.w,
                decoration: BoxDecoration(
                  color: Color(0xFFF2D3A4),
                  borderRadius: BorderRadius.circular(10.w),
                ),
                child: Center(
                  child: Text(
                    "Open in Maps",
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60.w,
          child: Text(
            label,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Color(0xFF262626),
            ),
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Color(0xFF262626),
            ),
          ),
        ),
      ],
    );
  }

  // Show maps selection bottom sheet
  void _showMapsBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 220.w,
        width: 403.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.w)),
        ),
        child: Column(
          children: [
            SizedBox(height: 8.w),
            _buildMapOption("Maps"),
            SizedBox(height: 8.w),
            _buildMapOption("Google maps"),
            SizedBox(height: 8.w),
            _buildMapOption("Cancel"),
          ],
        ),
      ),
    );
  }

  // Build map option
  Widget _buildMapOption(String title) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        // Handle map option selection
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 15.w),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 20.sp,
            fontWeight: FontWeight.w700,
            color: Color(0xFFA126FF),
          ),
        ),
      ),
    );
  }

  // Show cancel appointment bottom sheet
  void _showCancelAppointmentBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 252.w,
        width: 403.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.w)),
        ),
        child: Column(
          children: [
            SizedBox(height: 24.w),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: Text(
                "Are you sure you'd like to cancel it?\nOnce cancelled, your time slot will be released and can't be recovered.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF262626),
                ),
              ),
            ),
            SizedBox(height: 20.w),
            // Keep it button
            Container(
              width: 327.w,
              height: 60.w,
              margin: EdgeInsets.symmetric(horizontal: 38.w),
              decoration: BoxDecoration(
                color: Color(0xFFA126FF),
                borderRadius: BorderRadius.circular(30.w),
              ),
              child: Center(
                child: Text(
                  "Keep it",
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.w),
            // Cancel now button
            GestureDetector(
              onTap: () {
                Navigator.pop(context);
                // Handle cancel appointment
              },
              child: Text(
                "Cancel now",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFFA126FF),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class UpcomingAppointmentPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  late RxString displayNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  // Reference to the state for showing bottom sheets
  late UpcomingAppointmentPageState pageState;

  // Rewards page specific variables
  final RxInt selectedTabIndex = 0.obs; // 0: Tasks, 1: Redeem, 2: History
  final RxBool isPointsCardExpanded = false.obs;
  final RxInt availablePoints = 1590.obs;
  final RxInt weeklyProgress = 40.obs;
  final RxInt weeklyGoal = 50.obs;

  Future<UpcomingAppointmentPageController> init(UpcomingAppointmentPageState state) async {

    await PageInterceptor.pageAuthCheck();

    // Store reference to state
    pageState = state;

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;

    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'Add to calendar'.obs,
      onPressed: null,
      color: Color(0xFFA126FF).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: TextStyle(
        fontFamily: 'Manrope',
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ).obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'Cancel appointment'.obs,
      onPressed: () async {
        pageState._showCancelAppointmentBottomSheet();
      }.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: TextStyle(
        fontFamily: 'Manrope',
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: Color(0xFFA126FF),
      ).obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    return this;
  }

}

import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/appointment_enum.dart';
import 'appointment_time_info.dart';
import 'base_full_model.dart';

part 'appointment.g.dart';

@JsonSerializable()
class CustomerInfo {
  String name;
  String? email;
  String? phoneNumber;

  CustomerInfo({
    required this.name,
    this.email,
    this.phoneNumber,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) =>
      _$CustomerInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerInfoToJson(this);
}

@JsonSerializable()
class ServiceInfo {
  String serviceName;
  ServiceCategory serviceCategory;
  ServiceBreed serviceBreed;
  int duration;
  double price;
  Currency currency;

  ServiceInfo({
    required this.serviceName,
    required this.serviceCategory,
    required this.serviceBreed,
    required this.duration,
    required this.price,
    required this.currency,
  });

  factory ServiceInfo.fromJson(Map<String, dynamic> json) =>
      _$ServiceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceInfoToJson(this);
}

@JsonSerializable()
class StaffInfo {
  String staffName;
  String? staffEmail;

  StaffInfo({
    required this.staffName,
    this.staffEmail,
  });

  factory StaffInfo.fromJson(Map<String, dynamic> json) =>
      _$StaffInfoFromJson(json);

  Map<String, dynamic> toJson() => _$StaffInfoToJson(this);
}

/// Appointment - 预约
/// 存储在 Firestore collection "appointment"
@JsonSerializable()
class Appointment extends BaseFullModel {
  String appointmentId;              // 预约唯一ID
  String storeId;                    // 店铺ID
  String customerId;                 // 客户ID
  String staffId;                    // 员工ID
  String serviceId;                  // 服务ID
  AppointmentStatus status;          // 预约状态
  AppointmentSource source;          // 预约来源
  AppointmentTimeInfo timeInfo;      // 时间信息
  CustomerInfo customerInfo;         // 客户信息快照
  ServiceInfo serviceInfo;           // 服务信息快照
  StaffInfo staffInfo;               // 员工信息快照
  String? notes;                     // 预约备注
  String? customerNotes;             // 客户备注
  String? staffNotes;                // 员工备注
  String? cancellationReason;        // 取消原因
  String? completionNotes;           // 完成备注
  List<int> remindersSent;           // 已发送的提醒（小时数）
  String createdBy;                  // 创建者ID
  int? confirmedAt;                  // 确认时间（毫秒时间戳）
  int? startedAt;                    // 开始时间（毫秒时间戳）
  int? completedAt;                  // 完成时间（毫秒时间戳）
  int? cancelledAt;                  // 取消时间（毫秒时间戳）

  Appointment({
    required this.appointmentId,
    required this.storeId,
    required this.customerId,
    required this.staffId,
    required this.serviceId,
    required this.status,
    required this.source,
    required this.timeInfo,
    required this.customerInfo,
    required this.serviceInfo,
    required this.staffInfo,
    this.notes,
    this.customerNotes,
    this.staffNotes,
    this.cancellationReason,
    this.completionNotes,
    required this.remindersSent,
    required this.createdBy,
    this.confirmedAt,
    this.startedAt,
    this.completedAt,
    this.cancelledAt,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) =>
      _$AppointmentFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AppointmentToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  static String get collection => 'appointment';

  bool canBeCancelled() {
    return status == AppointmentStatus.confirmed || 
           status == AppointmentStatus.draft;
  }

  bool canBeStarted() {
    return status == AppointmentStatus.confirmed;
  }

  bool canBeCompleted() {
    return status == AppointmentStatus.inProgress;
  }

  DateTime getAppointmentDateTime() {
    return DateTime.parse('${timeInfo.date} ${timeInfo.startTime}');
  }

  bool isUpcoming() {
    final appointmentTime = getAppointmentDateTime();
    return appointmentTime.isAfter(DateTime.now()) && 
           (status == AppointmentStatus.confirmed || status == AppointmentStatus.draft);
  }

  bool isPast() {
    final appointmentTime = getAppointmentDateTime();
    return appointmentTime.isBefore(DateTime.now());
  }
}

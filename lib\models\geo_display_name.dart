import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

part 'geo_display_name.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoDisplayName {

  String? text;
  String? languageCode;

  GeoDisplayName({
    this.text,
    this.languageCode,
  });

  factory GeoDisplayName.fromJson(Map<String, dynamic> json) => _$GeoDisplayNameFromJson(json);
  Map<String, dynamic> toJson() => _$GeoDisplayNameToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory GeoDisplayName.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoDisplayName.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory GeoDisplayName.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoDisplayName.fromJson(jsonData);
  }

  factory GeoDisplayName.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoDisplayName.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoDisplayName? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? text,
    String? languageCode,
  }) {

    return GeoDisplayName(
      text: text,
      languageCode: languageCode,
    );
  }

  static GeoDisplayName copyFrom(GeoDisplayName? other) {
    return GeoDisplayName(
      text: other?.text,
      languageCode: other?.languageCode,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import 'history_detail_page.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';

import '../page_interceptor.dart';
import '../root_page.dart';


class RewardsPage extends StatefulWidget {
  const RewardsPage({super.key});

  @override
  RewardsPageState createState() => RewardsPageState();
}

class RewardsPageState extends State<RewardsPage> {
  late Future<RewardsPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => RewardsPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<RewardsPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  // Top section - positioned at the top
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Reward",
                      showAvatar: false,
                    ),
                  ),
                  // Main content - positioned below top section
                  Positioned(
                    top: 90.w, // 计算top section的实际高度：38.w(top padding) + 12.w(vertical padding) * 2 + 约28.w(内容高度)
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        SizedBox(height: 16.w),
                        // Tab navigation
                        _buildTabNavigation(controller),
                        SizedBox(height: 16.w),
                        // Content based on selected tab
                        Expanded(
                          child: Obx(() {
                            switch (controller.selectedTabIndex.value) {
                              case 0:
                                return _buildTasksContent(controller);
                              case 1:
                                return _buildRedeemContent(controller);
                              case 2:
                                return _buildHistoryContent(controller);
                              default:
                                return _buildTasksContent(controller);
                            }
                          }),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
        });
  }

  // Build tab navigation
  Widget _buildTabNavigation(RewardsPageController controller) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(25.w),
      ),
      child: Row(
        children: [
          _buildTabItem(controller, 0, 'Tasks'),
          _buildTabItem(controller, 1, 'Redeem'),
          _buildTabItem(controller, 2, 'History'),
        ],
      ),
    );
  }

  Widget _buildTabItem(RewardsPageController controller, int index, String title) {
    return Expanded(
      child: Obx(() => GestureDetector(
        onTap: () => controller.selectedTabIndex.value = index,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.w),
          decoration: BoxDecoration(
            color: controller.selectedTabIndex.value == index
                ? Color(0xFFF2D3A4)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20.w),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: controller.selectedTabIndex.value == index
                  ? Colors.white
                  : Color(0xFF666666),
            ),
          ),
        ),
      )),
    );
  }

  // Build tasks content
  Widget _buildTasksContent(RewardsPageController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // Points card
          _buildPointsCard(controller),
          SizedBox(height: 24.w),
          // Task list
          _buildTaskList(controller),
        ],
      ),
    );
  }

  // Build redeem content
  Widget _buildRedeemContent(RewardsPageController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // Points card (same as tasks)
          _buildPointsCard(controller),
          SizedBox(height: 24.w),
          // Product grid
          _buildProductGrid(controller),
        ],
      ),
    );
  }

  // Build product grid - always 2x2 layout for redeem tab
  Widget _buildProductGrid(RewardsPageController controller) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildProductCard(
                'Portable Travel Bottle (3 in 1)',
                '100ml water + 100ml food container',
                '1000 pts',
                'assets/images/travel_bottle.png',
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildProductCard(
                'Collapsible Pet Feed Bowls',
                '350ml - lightweight',
                '600 pts',
                'assets/images/feed_bowl.png',
              ),
            ),
          ],
        ),
        SizedBox(height: 16.w),
        Row(
          children: [
            Expanded(
              child: _buildProductCard(
                'LED Smart Dog Leash',
                'with interactive - light 30kg',
                '350 pts',
                'assets/images/dog_leash.png',
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildProductCard(
                'Hair Brush',
                'remove loose fur - Soften & detangle coat',
                '350 pts',
                'assets/images/hair_brush.png',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build individual product card
  Widget _buildProductCard(String name, String description, String points, String imagePath) {
    return Container(
      width: 180.w,
      height: 233.w,
      decoration: BoxDecoration(
        color: Color(0xFFF5F1E8),
        borderRadius: BorderRadius.circular(12.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image - no padding, fills top area
          Container(
            height: 166.w,
            width: 180.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.w),
                topRight: Radius.circular(12.w),
              ),
            ),
            child: Center(
              child: Icon(
                Icons.image,
                size: 40.w,
                color: Color(0xFFE0E0E0),
              ),
            ),
          ),
          // Content area with padding
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(5.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    name,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 10.sp,
                      fontWeight: FontWeight.w500, // Medium
                      color: Color(0xFF262626),
                      height: 1.6, // 160% line height
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Product description
                  Text(
                    description,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 8.sp,
                      fontWeight: FontWeight.w400, // Regular
                      color: Color(0xFF262626),
                      height: 1.6, // 160% line height
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Spacer(),
                  // Points and redeem button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        points,
                        style: TextStyle(
                          fontFamily: 'Manrope',
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600, // SemiBold
                          color: Color(0xFFAC40FF),
                          height: 1.5, // 150% line height
                        ),
                      ),
                      Container(
                        width: 24.w,
                        height: 24.w,
                        decoration: BoxDecoration(
                          color: Color(0xFFF2D3A4),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.add,
                          size: 16.w,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build history content
  Widget _buildHistoryContent(RewardsPageController controller) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          SizedBox(height: 24.w), // Top spacing (no points card)
          // History list
          _buildHistoryList(controller),
        ],
      ),
    );
  }

  // Build history list
  Widget _buildHistoryList(RewardsPageController controller) {
    final historyItems = [
      {
        'title': 'Portable Travel Bottle (3 in 1)',
        'subtitle': 'Redeemed on 2024-12-25',
        'points': '1000 pts',
        'color': Color(0xFFAC40FF),
        'icon': 'assets/images/travel_bottle.png',
      },
      {
        'title': 'CA \$5 Coupon',
        'subtitle': 'Redeemed on 2024-12-20',
        'points': '550 pts',
        'color': Color(0xFFAC40FF),
        'icon': 'assets/images/coupon.png',
      },
      {
        'title': 'CA \$5 Coupon',
        'subtitle': 'Redeemed on 2024-12-15',
        'points': '550 pts',
        'color': Color(0xFFAC40FF),
        'icon': 'assets/images/coupon.png',
      },
    ];

    return Column(
      children: historyItems.map((item) => _buildHistoryItem(item)).toList(),
    );
  }

  // Build individual history item (same format as task item)
  Widget _buildHistoryItem(Map<String, dynamic> item) {
    return GestureDetector(
      onTap: () {
        Get.to(() => HistoryDetailPage(
          itemTitle: item['title'],
          itemSubtitle: item['subtitle'],
          itemPoints: item['points'],
        ));
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16.w),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.w),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8.w,
              offset: Offset(0, 2.w),
            ),
          ],
        ),
        child: Row(
        children: [
          // Product icon/image
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: Color(0xFFF5F1E8),
              borderRadius: BorderRadius.circular(8.w),
            ),
            child: Center(
              child: Icon(
                Icons.image,
                size: 24.w,
                color: Color(0xFFE0E0E0),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['title'],
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400, // Regular
                    color: Color(0xFF262626),
                    height: 1.0, // Auto line height
                  ),
                ),
                if (item['subtitle'].isNotEmpty) ...[
                  SizedBox(height: 4.w),
                  Text(
                    item['subtitle'],
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500, // Medium
                      color: Color(0xFFC6C6C6),
                      height: 1.0, // Auto line height
                    ),
                  ),
                ],
                SizedBox(height: 8.w),
                Text(
                  item['points'],
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500, // Medium
                    color: Color(0xFFAC40FF),
                    height: 1.0, // Auto line height
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      ),
    );
  }

  // Build points card
  Widget _buildPointsCard(RewardsPageController controller) {
    return Obx(() => GestureDetector(
      onTap: () => controller.isPointsCardExpanded.value = !controller.isPointsCardExpanded.value,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        padding: EdgeInsets.all(20.w),
        decoration: BoxDecoration(
          color: Color(0xFFF2D3A4),
          borderRadius: BorderRadius.circular(16.w),
        ),
        child: controller.isPointsCardExpanded.value
            ? _buildExpandedPointsCard(controller)
            : _buildCollapsedPointsCard(controller),
      ),
    ));
  }

  // Build collapsed points card (right side style)
  Widget _buildCollapsedPointsCard(RewardsPageController controller) {
    return Row(
      children: [
        // Star icon
        Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.star,
            color: Colors.white,
            size: 24.w,
          ),
        ),
        SizedBox(width: 16.w),
        // Points text
        Expanded(
          child: Text(
            '${controller.availablePoints.value} pts.',
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 24.sp,
              fontWeight: FontWeight.w700,
              color: Color(0xFF262626),
            ),
          ),
        ),
        // Globe icon placeholder
        Container(
          width: 60.w,
          height: 60.w,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.3),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.public,
            color: Colors.white,
            size: 30.w,
          ),
        ),
      ],
    );
  }

  // Build expanded points card (left side style)
  Widget _buildExpandedPointsCard(RewardsPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Star icon
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.star,
                color: Colors.white,
                size: 24.w,
              ),
            ),
            SizedBox(width: 16.w),
            // Available points section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your available points',
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF666666),
                    ),
                  ),
                  Text(
                    '${controller.availablePoints.value} pts.',
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
            ),
            // Globe icon placeholder
            Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.public,
                color: Colors.white,
                size: 40.w,
              ),
            ),
          ],
        ),
        SizedBox(height: 20.w),
        // Weekly progress section
        Text(
          'Points of this week',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Color(0xFF666666),
          ),
        ),
        SizedBox(height: 8.w),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 8.w,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4.w),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: controller.weeklyProgress.value / controller.weeklyGoal.value,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFF8B5CF6),
                      borderRadius: BorderRadius.circular(4.w),
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Text(
              '${controller.weeklyProgress.value}/${controller.weeklyGoal.value}',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build task list
  Widget _buildTaskList(RewardsPageController controller) {
    final tasks = [
      {
        'title': 'Daily check-in',
        'subtitle': 'Check in once at local locations',
        'points': '+10 pts each time',
        'color': Color(0xFF8B5CF6),
      },
      {
        'title': 'Walk 30min with your pet',
        'subtitle': 'Auto detected via GPS',
        'points': '+10 pts each time',
        'color': Color(0xFF8B5CF6),
      },
      {
        'title': 'Record activity',
        'subtitle': 'Feed, poop, nap, etc',
        'points': '+25 pts each time',
        'color': Color(0xFF8B5CF6),
      },
      {
        'title': 'Book a service',
        'subtitle': '',
        'points': '+50 pts each time',
        'color': Color(0xFF8B5CF6),
      },
      {
        'title': 'Post a photo of your pet',
        'subtitle': '',
        'points': '+50 pts each time',
        'color': Color(0xFF8B5CF6),
      },
    ];

    return Column(
      children: tasks.map((task) => _buildTaskItem(task)).toList(),
    );
  }

  // Build individual task item
  Widget _buildTaskItem(Map<String, dynamic> task) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8.w,
            offset: Offset(0, 2.w),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task['title'],
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF262626),
                  ),
                ),
                if (task['subtitle'].isNotEmpty) ...[
                  SizedBox(height: 4.w),
                  Text(
                    task['subtitle'],
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF999999),
                    ),
                  ),
                ],
                SizedBox(height: 8.w),
                Text(
                  task['points'],
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: task['color'],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class RewardsPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  late RxString displayNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  // Rewards page specific variables
  final RxInt selectedTabIndex = 0.obs; // 0: Tasks, 1: Redeem, 2: History
  final RxBool isPointsCardExpanded = false.obs;
  final RxInt availablePoints = 1590.obs;
  final RxInt weeklyProgress = 40.obs;
  final RxInt weeklyGoal = 50.obs;

  Future<RewardsPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    return this;
  }

}

import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../views/component/widgets/common_text.dart';
import '../../../views/component/widgets/common_record_widget.dart';
import '../../../views/theme/theme_plugin.dart';
import '../../../views/theme/colors/color_extension.dart';
import 'add_record_page.dart';
import 'record_page.dart';

class RecordDailySection extends StatefulWidget {
  final RecordPageController controller;
  final bool isExpanded;
  final Function(bool) onExpandToggle;

  const RecordDailySection({
    super.key,
    required this.controller,
    required this.isExpanded,
    required this.onExpandToggle,
  });

  @override
  State<RecordDailySection> createState() => _RecordDailySectionState();
}

class _RecordDailySectionState extends State<RecordDailySection> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.controller.dateScrollController.hasClients) {
        double itemWidth = 50.w + 12.w;
        double centerOffset =
            itemWidth * 7 - (MediaQuery.of(context).size.width / 2 - 50.w);

        widget.controller.dateScrollController.jumpTo(
          centerOffset.clamp(
            0,
            widget.controller.dateScrollController.position.maxScrollExtent,
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final double backgroundHeight = widget.isExpanded ? 480.w : 100.w;
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Container(
            height: backgroundHeight,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomRight: Radius.circular(25.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: widget
                      .controller.theme.colorExtension.eventPostTileShadow,
                  offset: const Offset(0, 8),
                  blurRadius: 16,
                  spreadRadius: -4,
                ),
              ],
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            widget.isExpanded
                ? _buildMonthlyCalendar()
                : _buildHorizontalDateStrip(),
            if (widget.isExpanded) _buildMonthScrollSelector(),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12.w),
              child: Center(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => widget.onExpandToggle(!widget.isExpanded),
                  child: Container(
                    width: double.infinity,
                    height: 30.w,
                    alignment: Alignment.center,
                    child: Container(
                      width: 50.w,
                      height: 5.w,
                      decoration: BoxDecoration(
                        color: widget
                            .controller.theme.themeData.colorScheme.tertiary,
                        borderRadius: BorderRadius.circular(100),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(child: _buildDailyActivityList()),
          ],
        ),
      ],
    );
  }

  Widget _buildHorizontalDateStrip() {
    return SizedBox(
      height: 60.w,
      child: ListView.separated(
        controller: widget.controller.dateScrollController,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        itemCount: 15,
        separatorBuilder: (_, __) => SizedBox(width: 12.w),
        itemBuilder: (context, index) {
          final now = DateTime.now();
          final date = now.add(Duration(days: index - 7));

          return Obx(() {
            final isSelected =
                widget.controller.selectedDate.value.year == date.year &&
                    widget.controller.selectedDate.value.month == date.month &&
                    widget.controller.selectedDate.value.day == date.day;

            return GestureDetector(
              onTap: () {
                widget.controller.selectedDate.value = date;
              },
              child: Container(
                width: 50.w,
                height: 55.w,
                padding: EdgeInsets.only(right: 5.w, bottom: 8.w, left: 6.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  border: Border.all(
                    width: 1.w,
                    color: isSelected
                        ? widget.controller.theme.themeData.colorScheme.primary
                        : widget
                            .controller.theme.themeData.colorScheme.tertiary,
                  ),
                  color: isSelected
                      ? widget.controller.theme.themeData.colorScheme.primary
                          .withOpacity(0.2)
                      : Colors.transparent,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      DateFormat("d").format(date),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: isSelected
                            ? widget
                                .controller.theme.themeData.colorScheme.primary
                            : widget.controller.theme.themeData.colorScheme
                                .onSurface,
                      ),
                    ),
                    Text(
                      DateFormat("E").format(date),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: isSelected
                            ? widget
                                .controller.theme.themeData.colorScheme.primary
                            : widget.controller.theme.themeData.colorScheme
                                .tertiary,
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        },
      ),
    );
  }

  Widget _buildMonthlyCalendar() {
    final weekLabels = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

    return Obx(() {
      final selectedDate = widget.controller.selectedDate.value;
      final currentYear = selectedDate.year;
      final currentMonth = selectedDate.month;

      final firstDayOfMonth = DateTime(currentYear, currentMonth, 1);
      final startWeekday = firstDayOfMonth.weekday % 7;
      final totalDaysThisMonth = DateTime(currentYear, currentMonth + 1, 0).day;
      final totalDaysLastMonth = DateTime(currentYear, currentMonth, 0).day;

      List<DateTime> visibleDates = [];

      for (int i = startWeekday - 1; i >= 0; i--) {
        visibleDates.add(
            DateTime(currentYear, currentMonth - 1, totalDaysLastMonth - i));
      }

      for (int i = 1; i <= totalDaysThisMonth; i++) {
        visibleDates.add(DateTime(currentYear, currentMonth, i));
      }

      int remaining = 42 - visibleDates.length;
      for (int i = 1; i <= remaining; i++) {
        visibleDates.add(DateTime(currentYear, currentMonth + 1, i));
      }

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          children: [
            // week header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: weekLabels.map((label) {
                return SizedBox(
                  width: 42.w,
                  child: Center(
                    child: CommonText(
                      label,
                      widget.controller.theme.textStyleExtension.recordWeek,
                    ),
                  ),
                );
              }).toList(),
            ),

            SizedBox(height: 8.w),

            // calendar body
            GridView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 42,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                crossAxisSpacing: 8.w,
                mainAxisSpacing: 8.w,
                childAspectRatio: 42 / 55,
              ),
              itemBuilder: (context, index) {
                final date = visibleDates[index];
                final isSelected = widget.controller.selectedDate.value.year ==
                        date.year &&
                    widget.controller.selectedDate.value.month == date.month &&
                    widget.controller.selectedDate.value.day == date.day;

                final isCurrentMonth = date.month == currentMonth;

                return GestureDetector(
                  onTap: () {
                    widget.controller.selectedDate.value = date;
                  },
                  child: Container(
                    padding: EdgeInsets.fromLTRB(2.w, 8.w, 5.w, 8.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.r),
                      border: Border.all(
                        width: 1,
                        color: isCurrentMonth
                            ? widget
                                .controller.theme.themeData.colorScheme.primary
                            : widget.controller.theme.themeData.colorScheme
                                .tertiary,
                      ),
                      color: isCurrentMonth
                          ? (isSelected
                              ? widget.controller.theme.themeData.colorScheme
                                  .primary
                                  .withOpacity(0.2)
                              : Colors.transparent)
                          : widget
                              .controller.theme.themeData.colorScheme.tertiary
                              .withOpacity(0.2),
                    ),
                    alignment: Alignment.topCenter,
                    child: Text("${date.day}",
                        style: isCurrentMonth
                            ? widget
                                .controller.theme.textStyleExtension.recordMonth
                            : widget.controller.theme.textStyleExtension
                                .recordNextMonth),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }

  Widget _buildMonthScrollSelector() {
    final List<String> months = [
      "2025",
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    final ScrollController scrollController = ScrollController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final selectedMonth = widget.controller.selectedDate.value.month;
      final itemWidth = 50.w + 12.w;
      final offset = selectedMonth * itemWidth +
          (itemWidth / 2) -
          (MediaQuery.of(Get.context!).size.width / 2);
      if (scrollController.hasClients) {
        scrollController
            .jumpTo(offset.clamp(0, scrollController.position.maxScrollExtent));
      }
    });

    return SizedBox(
      height: 32.w,
      child: Obx(() {
        final selectedMonthName =
            DateFormat('MMM').format(widget.controller.selectedDate.value);

        return ListView.separated(
          controller: scrollController,
          scrollDirection: Axis.horizontal,
          itemCount: months.length,
          separatorBuilder: (_, __) => SizedBox(width: 12.w),
          itemBuilder: (context, index) {
            final label = months[index];
            final isYearLabel = index == 0;
            final isSelected = label == selectedMonthName;

            return GestureDetector(
              onTap: () {
                if (isYearLabel) return;
                final newDate = DateTime(
                    widget.controller.selectedDate.value.year, index, 1);
                widget.controller.selectedDate.value = newDate;
              },
              child: SizedBox(
                width: isYearLabel ? 60.w : 50.w,
                child: Center(
                  child: Text(
                    label,
                    style: isSelected? widget.controller.theme.textStyleExtension.recordMonthSelect:
                    widget.controller.theme.textStyleExtension.recordMonthUnselect
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildDailyActivityList() {
    return Obx(() {
      final selectedDate = widget.controller.selectedDate.value;
      final dateKey = DateFormat('yyyy-MM-dd').format(selectedDate);
      final activities = widget.controller.allActivities[dateKey] ?? [];

      return ListView.builder(
        padding: EdgeInsets.only(top: 12.w, bottom: 100.w),
        itemCount: activities.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            final formattedDate = DateFormat("MMMM d").format(selectedDate);
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 4.w),
              child: Align(
                alignment: Alignment.centerLeft,
                child: CommonText(
                  '$formattedDate',
                  widget.controller.theme.textStyleExtension.recordDayDisplay,
                ),
              ),
            );
          }
          final activity = activities[index - 1];
          return CommonRecordWidget(
            type: activity['type']!,
            time: activity['time']!,
            description: activity['description']!,
            onDelete: () async {
              final updated = List<Map<String, String>>.from(activities);
              updated.removeAt(index - 1);
              widget.controller.allActivities[dateKey] = updated;

              final recordSid = activity['sid'];
              if (recordSid != null) {
                switch (activity['type']) {
                  case 'feed':
                    await widget.controller.deleteFeedingRecord(recordSid);
                    break;
                  case 'poop':
                    await widget.controller.deletePoopRecord(recordSid);
                    break;
                  case 'walk':
                    await widget.controller.deleteWalkingRecord(recordSid);
                    break;
                  case 'social':
                    await widget.controller.deleteSocialRecord(recordSid);
                    break;
                  case 'med':
                    await widget.controller.deleteMedicineRecord(recordSid);
                    break;
                  case 'vaccine':
                    await widget.controller.deleteVaccineRecord(recordSid);
                    break;
                  case 'grooming':
                    await widget.controller.deleteGroomingRecord(recordSid);
                    break;
                  case 'daycare':
                    await widget.controller.deleteDaycareRecord(recordSid);
                    break;
                  case 'vet':
                    await widget.controller.deleteVetRecord(recordSid);
                    break;
                  case 'other':
                    await widget.controller.deleteOtherRecord(recordSid);
                    break;
                  default:
                    break;
                }
              }
            },
            theme: widget.controller.theme,
            onTap: () => _handleRecordTap(activity),
          );
        },
      );
    });
  }


  void _handleRecordTap(Map<String, String> record) async {
    final type = record['type']!;
    final sid = record['sid']!;
    final pet = widget.controller.petList!
        .firstWhere((p) => p.name == widget.controller.selectedPet.value);

    dynamic fullRecord;

    try {
      switch (type) {
        case 'feed':
          final records = await widget.controller.petService
              .getPetFeedingRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'poop':
          final records = await widget.controller.petService.getPetPoopRecords(
              pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'walk':
          final records = await widget.controller.petService
              .getPetWalkingRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'social':
          final records = await widget.controller.petService
              .getPetSocialRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'med':
          final records = await widget.controller.petService
              .getPetMedicineRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'vaccine':
          final records = await widget.controller.petService
              .getPetVaccineRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'grooming':
          final records = await widget.controller.petService
              .getPetGroomingRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'daycare':
          final records = await widget.controller.petService
              .getPetDaycareRecords(pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'vet':
          final records = await widget.controller.petService.getPetVetRecords(
              pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        case 'other':
          final records = await widget.controller.petService.getPetOtherRecords(
              pet.sid!);
          fullRecord = records.firstWhere((r) => r.sid == sid);
          break;
        default:
          return;
      }
    } catch (e) {
      fullRecord = null;
    }

    if (fullRecord != null) {
      final result = await Get.to(() =>
          AddRecordPage(
            parentController: widget.controller,
            type: type,
            label: type[0].toUpperCase() + type.substring(1),
            selectedPet: pet,
            recordToEdit: fullRecord,
          ));

      if (result == true) {
        await widget.controller.reloadPetsAndRefresh();
        setState(() {});
      }
    }
  }

}

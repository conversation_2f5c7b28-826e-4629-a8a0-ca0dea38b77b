enum GeoLocationZoomLevel {

  level_1(1, 5000000, 'Whole Earth'),
  level_2(2, 2500000, 'Continent scale'),
  level_3(3, 1200000, 'Subcontinent'),
  level_4(4, 600000, 'Large country / Region'),
  level_5(5, 300000, 'Country scale'),
  level_6(6, 150000, 'Large Region'),
  level_7(7, 90000, 'Province scale'),
  level_8(8, 60000, 'State or province'),
  level_9(9, 50000, 'Large metro'),
  level_10(10, 40000, 'Metro area'),
  level_11(11, 30000, 'Urban area'),
  level_12(12, 20000, 'City-level'),
  level_13(13, 10000, 'Inner city'),
  level_14(14, 5000, 'Neighborhood'),
  level_15(15, 2500, 'Streets and blocks'),
  level_16(16, 1200, 'Small district'),
  level_17(17, 600, 'Street view detail'),
  level_18(18, 300, 'Buildings'),
  level_19(19, 150, 'House-level'),
  level_20(20, 75, 'Individual buildings'),
  level_21(21, 40, 'Rooms and entrances'),
  level_22(22, 20, 'Maximum zoom (indoor level)')
  ;

  final int level;
  final int radius;
  final String desc;
  const GeoLocationZoomLevel(this.level, this.radius, this.desc);

  // Factory constructor to create a TestType object based on the code
  factory GeoLocationZoomLevel.fromLevel(int level) {
    return GeoLocationZoomLevel.values.firstWhere((element) => element.level == level, orElse: () => throw ArgumentError('Invalid level: $level'));
  }
  factory GeoLocationZoomLevel.fromRadius(int radius) {
    return GeoLocationZoomLevel.values.firstWhere((element) => element.radius == radius, orElse: () => throw ArgumentError('Invalid radius: $radius'));
  }
  factory GeoLocationZoomLevel.fromDesc(String desc) {
    return GeoLocationZoomLevel.values.firstWhere((element) => element.desc == desc, orElse: () => throw ArgumentError('Invalid desc: $desc'));
  }

}

enum GoogleMapApiHeader {

  apiKey('X-Goog-Api-Key'),
  placeField('X-Goog-FieldMask'),
  ;

  final String key;
  const GoogleMapApiHeader(this.key);

  // Factory constructor to create a TestType object based on the code
  factory GoogleMapApiHeader.fromDesc(String key) {
    return GoogleMapApiHeader.values.firstWhere((element) => element.key == key, orElse: () => throw ArgumentError('Invalid key: $key'));
  }

}

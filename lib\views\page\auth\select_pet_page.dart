// import 'dart:async';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
// import 'package:onenata_app/views/component/widgets/common_button.dart';
// import 'package:onenata_app/views/component/widgets/common_white_section.dart';
// import 'package:onenata_app/views/component/widgets/common_backbutton.dart';
// import 'package:onenata_app/views/component/widgets/common_avatar.dart';
// import 'package:onenata_app/views/component/widgets/common_page.dart';
// import 'package:onenata_app/views/theme/layouts/theme_layout_i.dart';
//
// import '../../component/progress/loading_widget.dart';
// import '../../theme/colors/color_extension.dart';
// import '../../theme/text_schemes/text_style_extension.dart';
// import '../../theme/theme_service.dart';
//
// class SelectPetPage extends StatefulWidget {
//   const SelectPetPage({super.key});
//
//   @override
//   SelectPetPageState createState() => SelectPetPageState();
// }
//
// class SelectPetPageState extends State<SelectPetPage> {
//   late Future<SelectPetPageController> _controller;
//
//   @override
//   void initState() {
//     super.initState();
//     // Initialize the controller in initState
//     _controller = Get.putAsync(() => SelectPetPageController().init());
//     // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<SelectPetPageController>(
//         future: _controller,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             // Indicator for loading
//             return const Center(
//               child: LoadingWidget(),
//             );
//           } else if (snapshot.hasError) {
//             // Error handling
//             return Center(child: Text('Error: ${snapshot.error}'));
//           } else {
//             // Page functions
//             final controller = snapshot.data!;
//             return CommonPage(
//               themeData: controller.themeData,
//               layout: controller.layout,
//               textStyleExtension: controller.textStyleExtension,
//               backgroundColor: controller.themeData.colorScheme.primary,
//               body: Stack(
//                 children: [
//                   _buildBackButton(),
//                   _buildAvatar(),
//                   _buildTitle(controller),
//                   _buildWhiteSection(),
//                   _buildPetOptionWithText(
//                       'dog'.t18, 'assets/images/dog.png', 27, 261,controller),
//                   _buildPetOptionWithText(
//                       'cat'.t18, 'assets/images/cat.png', 217, 261,controller),
//                   _buildPetOptionWithText(
//                       'other'.t18, 'assets/images/pig.png', 121, 475,controller),
//                   _buildSubmitButton(controller),
//                   _buildBottomText(controller),
//                 ],
//               ),
//             );
//           }
//         });
//   }
//
//   Widget _buildBackButton() {
//     return CommonBackButton(
//       onPressed: () {
//         Get.back();
//       },
//     );
//   }
//
//   Widget _buildAvatar() {
//     return Positioned(
//       top: 50.w,
//       left: 159.w,
//       child: CommonAvatar(
//         size: 86,
//         imagePath: 'assets/images/nata_icon_cir.png',
//       ),
//     );
//   }
//
//   Widget _buildTitle(SelectPetPageController controller) {
//     return Positioned(
//       top: 136.w,
//       left: 141.w,
//       child: Text(
//         'select.pet'.t18,
//         textAlign: TextAlign.center,
//         style: controller.textStyleExtension.appAvatarMedium,
//       ),
//     );
//   }
//
//   Widget _buildWhiteSection() {
//     return const CommonWhiteSection(
//       width: 403,
//       height: 664,
//       top: 197,
//       borderRadius: 85,
//     );
//   }
//
//   Widget _buildPetOptionWithText(
//       String petName, String imagePath, double left, double top, SelectPetPageController controller) {
//     return Positioned(
//       width: 160.w,
//       height: 213.w,
//       top: top.w,
//       left: left.w,
//       child: GestureDetector(
//         onTap: () => controller.selectedPet.value = petName,
//         child: Column(
//           children: [
//             Container(
//               width: 160.w,
//               height: 160.w,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(50.r),
//                 image: DecorationImage(
//                   image: AssetImage(imagePath),
//                   fit: BoxFit.cover,
//                 ),
//               ),
//             ),
//             SizedBox(height: 13.w),
//             Text(
//               petName,
//               style: controller.textStyleExtension.appAvatarSelect,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildSubmitButton(SelectPetPageController controller) {
//     return Positioned(
//       top: 721.w,
//       left: 38.w,
//       child: Obx(
//         () => CommonButton(
//           text: 'continue'.t18,
//           onPressed: controller.selectedPet.value.isNotEmpty
//               ? () {
//                   Get.toNamed('/petDetail');
//                 }
//               : null,
//           backgroundColor: controller.selectedPet.value.isNotEmpty
//               ? controller.themeData.colorScheme.secondary
//               : controller.themeData.colorScheme.tertiary,
//           textColor: controller.themeData.colorScheme.onPrimary,
//           borderColor: Colors.transparent,
//           width: 327.w,
//           height: 60.w,
//         ),
//       ),
//     );
//   }
//
//   Widget _buildBottomText(SelectPetPageController controller) {
//     return Positioned(
//       top: 800.w,
//       left: 154.w,
//       child: GestureDetector(
//         onTap: () {
//           Get.toNamed('/signUpEmail');
//         },
//         child: Text(
//           'skip'.t18,
//           textAlign: TextAlign.center,
//           style: controller.textStyleExtension.authPageChangeLogin,
//         ),
//       ),
//     );
//   }
// }
//
// class SelectPetPageController extends GetxController {
//   final RxString selectedPet = ''.obs;
//
//   late ThemeService themeController;
//   late ThemeData themeData;
//   late ThemeLayout layout;
//   late ColorExtension colorExtension;
//   late TextStyleExtension textStyleExtension;
//
//   Future<SelectPetPageController> init() async {
//     // Init theme and layout
//     themeController = await Get.putAsync(() => ThemeService().init());
//     themeData = themeController.themeData;
//     layout = themeController.themeLayout;
//     colorExtension = themeController.themeData.extensions.entries.first.value
//         as ColorExtension;
//     textStyleExtension = themeController.textStyleExtension;
//
//     return this;
//   }
//
//   void selectPet(String pet) {
//     selectedPet.value = pet;
//   }
// }

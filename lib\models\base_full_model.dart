import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_model.dart';

part 'base_full_model.g.dart';

@JsonSerializable()
class BaseFullModel extends BaseModel {

  String? createdBy;
  int? createDate; // in milliseconds, the time of the record creation
  String? updatedBy;
  int? updateDate; // in milliseconds, the time of the record update
  String? reviewedBy;
  int? reviewDate; // in milliseconds, the time of the record review
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? approveStatus;
  String? notes;
  List<String>? tags;

  BaseFullModel({
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    this.createdBy,
    this.createDate,
    this.updatedBy,
    this.updateDate,
    this.reviewedBy,
    this.reviewDate,
    this.approveStatus,
    this.notes,
    this.tags,
  });

  factory BaseFullModel.fromJson(Map<String, dynamic> json) => _$BaseFullModelFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$BaseFullModelToJson(this);

  static copyFrom(BaseFullModel other) {
    return BaseFullModel(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_message_header.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceMessageHeader _$DeviceMessageHeaderFromJson(Map<String, dynamic> json) =>
    DeviceMessageHeader(
      sid: json['sid'] as String?,
      pid: json['pid'] as String?,
      deviceId: json['deviceId'] as String,
      generateDate: (json['generateDate'] as num?)?.toInt(),
      sendDate: (json['sendDate'] as num?)?.toInt(),
      receiveDate: (json['receiveDate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DeviceMessageHeaderToJson(
        DeviceMessageHeader instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'pid': instance.pid,
      'deviceId': instance.deviceId,
      'generateDate': instance.generateDate,
      'sendDate': instance.sendDate,
      'receiveDate': instance.receiveDate,
    };

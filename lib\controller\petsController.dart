import 'package:flutter/foundation.dart';

import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:onenata_app/models/models_i.dart';
// import 'package:onenata_app/dao/firestore_service.dart';
import 'package:onenata_app/services/service_response.dart';

import 'package:onenata_app/views/component/components_i.dart';

class PetController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RxList<Pet> userPets = <Pet>[].obs;

  // 获取用户的所有宠物
  Future<void> getUserPets(String userId) async {
    ProcessUtil.showProgress(message: 'Loading...', cancelable: false);

    final ServiceResponse response = ServiceResponse(code:200, msg: "200", data: null);
        // await FireStoreService().getDoc('users/$userId/pets');

    if (response.code == 200) {
      userPets.value = response.data
          .map((doc) => Pet.fromJson(doc.data() as Map<String, dynamic>))
          .toList();
    } else {
      ToastUtil.showToast(response.msg);
      if (kDebugMode) {
        print("${response.code} + ${response.msg} ");
      }
    }
  }

  // 添加新宠物
  Future<void> addPet(String userId, Pet pet) async {
    ProcessUtil.showProgress(message: 'creating the pet info...', cancelable: false);
    final ServiceResponse response = ServiceResponse(code:200, msg: "200", data: null);
        // await FireStoreService().setDoc('users/$userId/pets', pet.toJson());
    if (response.msg == 200) {
      ToastUtil.showToast("Your Pets has been Added!", duration: Duration(seconds: 5));
      userPets.add(pet);
    } else {
      ToastUtil.showToast(response.msg);
      if (kDebugMode) {
        print("${response.code} + ${response.msg} ");
      }
    }
  }

  // 更新宠物信息
  Future<void> updatePet(
      String userId, String petUid, Map<String, dynamic> data) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('pets')
          .doc(petUid)
          .update({
        ...data,
        'updatedAt': Timestamp.now(),
      });

      // 更新本地列表
      final index = userPets.indexWhere((pet) => pet.sid == petUid);
      if (index != -1) {
        final doc = await _firestore
            .collection('users')
            .doc(userId)
            .collection('pets')
            .doc(petUid)
            .get();
        userPets[index] = Pet.fromJson(doc.data()!);
      }
    } catch (e) {
      print('failed to update pet info: $e');
    }
  }

  // 更新宠物位置
  Future<void> updatePetLocation(
      String userId, String petUid, GeoPoint location) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('pets')
          .doc(petUid)
          .update({
        'currentLocation': location,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      print('failed to update pet location: $e');
    }
  }

  // 添加疫苗信息
  Future<void> addVaccination(String userId, String petUid,
      Map<String, dynamic> vaccinationData) async {
    try {
      final pet = await _firestore
          .collection('users')
          .doc(userId)
          .collection('pets')
          .doc(petUid)
          .get();

      final currentVaccinations =
          List<Map<String, dynamic>>.from(pet.data()?['vaccinationInfo'] ?? []);
      currentVaccinations.add(vaccinationData);

      await _firestore
          .collection('users')
          .doc(userId)
          .collection('pets')
          .doc(petUid)
          .update({
        'vaccinationInfo': currentVaccinations,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      print('failed to add vaccination record: $e');
    }
  }

  // 添加共同拥有者
  Future<void> addOwner(String userId, String petUid, String newOwnerId) async {
    try {
      final pet = await _firestore
          .collection('users')
          .doc(userId)
          .collection('pets')
          .doc(petUid)
          .get();

      final currentOwners = List<String>.from(pet.data()?['owner'] ?? []);
      if (!currentOwners.contains(newOwnerId)) {
        currentOwners.add(newOwnerId);
        await _firestore
            .collection('users')
            .doc(userId)
            .collection('pets')
            .doc(petUid)
            .update({
          'owner': currentOwners,
          'updatedAt': Timestamp.now(),
        });
      }
    } catch (e) {
      print('add co-owner failed: $e');
    }
  }
}

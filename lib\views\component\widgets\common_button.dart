import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onenata_app/views/theme/text_schemes/onenata_classic_text_style_extension.dart'; // ✅ 直接使用 `OneNataClassicTextStyleExtension`

class CommonButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double width;
  final double height;
  final double borderRadius;

  const CommonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.width = 327,
    this.height = 60,
    this.borderRadius = 30.5,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyles = OneNataClassicTextStyleExtension(); // ✅ 获取 OneNata 主题的 `TextStyle`

    final TextStyle buttonStyle = isOutlined
        ? buttonStyles.buttonLargeOutlined // ✅ 使用 `buttonLargeOutlined`
        : buttonStyles.buttonLargeFilled; // ✅ 使用 `buttonLargeFilled`

    return SizedBox(
      width: width.w,
      height: height.h,
      child: isOutlined
          ? OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: borderColor ?? Colors.black, width: 1.w),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius.r),
          ),
        ),
        child: Text(text, style: buttonStyle), // ✅ 直接使用 `OneNataClassicTextStyleExtension`
      )
          : ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? Colors.blue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius.r),
          ),
        ),
        child: Text(text, style: buttonStyle), // ✅ 直接使用 `OneNataClassicTextStyleExtension`
      ),
    );
  }
}

import 'dart:async';

import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

class UserDao {

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final FirebaseMessaging _messagingService = FirebaseMessaging.instance;

  // Collection reference
  final CollectionReference _userAccountCollection = FirebaseFirestore.instance.collection(UserAccount.collection);
  final CollectionReference _userAuthRecordCollection = FirebaseFirestore.instance.collection(UserAuthRecord.collection);
  final CollectionReference _userDataCollection = FirebaseFirestore.instance.collection(UserData.collection);

  // user account ----------------------------------------------
  Future<void> addUserAccount ({required UserAccount account, UserData? data}) async {

    DocumentReference userAccountRef = _userAccountCollection.doc(account.sid);

    Map<String, dynamic> accountDoc = account.toFirestoreData();
    accountDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    if (data == null) {
      // only user account
      await userAccountRef.set(accountDoc);
    }
    else {

      DocumentReference userDataRef = _userDataCollection.doc(data.sid);
      data.uid = account.sid!;

      Map<String, dynamic> dataDoc = data.toFirestoreData();
      dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

      // transaction
      await _db.runTransaction((transaction) async {
        transaction.set(userAccountRef, accountDoc);
        transaction.set(userDataRef, dataDoc);
      });
    }
  }

  /// Update user account based on sid
  /// sid is used for save or query operation, it's actually the doc id of the user account object in firestore
  Future<void> updateUserAccount (UserAccount account, {bool mergeOption = false}) async {

    DocumentReference userAccountRef = _userAccountCollection.doc(account.sid!);
    Map<String, dynamic> accountDoc = account.toFirestoreData();
    accountDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await userAccountRef.set(accountDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteUserAccount (String userAccountId) async {

    DocumentReference userAccountRef = _userAccountCollection.doc(userAccountId);
    await userAccountRef.delete();
  }

  Future<void> softDeleteUserAccount (String userAccountId) async {
    DocumentReference userAccountRef = _userAccountCollection.doc(userAccountId);
    await userAccountRef.update({CommonField.isValid: 0});
  }

  /// Retrieve user account from firebase and set the doc id as the sid of it
  /// sid is used for save or query operation, it's actually the doc id of the user account object in firestore
  Future<UserAccount?> getUserAccountById ({String? uid, String? fid, String? email, String? phoneNumber}) async {

    // by user id
    if (uid != null) {
      DocumentSnapshot doc = await _userAccountCollection.doc(uid).get();

      if (doc.exists) {
        return UserAccount.fromFirestoreData(doc);
      } else {
        return null;
      }
    }
    // by firebase id
    else if (fid != null) {
      QuerySnapshot querySnapshot = await _userAccountCollection
          .where('fid', isEqualTo: fid)
          .get();

      if (querySnapshot.docs.isNotEmpty) {

        return  UserAccount.fromFirestoreData(querySnapshot.docs.first);
        // acc.sid = doc
      } else {
        return null;
      }
    }
    // by email
    else if (email != null) {
      QuerySnapshot querySnapshot = await _userAccountCollection
          .where('email', isEqualTo: email)
          .get();

      if (querySnapshot.docs.isNotEmpty) {

        return UserAccount.fromFirestoreData(querySnapshot.docs.first);
      } else {
        return null;
      }
    }
    // by phone number
    else {
      QuerySnapshot querySnapshot = await _userAccountCollection
          .where('phone_number', isEqualTo: phoneNumber)
          .get();

      if (querySnapshot.docs.isNotEmpty) {

        return UserAccount.fromFirestoreData(querySnapshot.docs.first);
      } else {
        return null;
      }
    }
  }

  // user auth record ----------------------------------------------
  Future<void> addUserAuthRecord(UserAuthRecord record) async {

    DocumentReference userAuthRecordRef = _userAuthRecordCollection.doc(record.sid);

    record.deviceModel = await DeviceInfoUtil.getDeviceModel();
    record.deviceOS = await DeviceInfoUtil.getDeviceOs();
    record.deviceId = await DeviceInfoUtil.getDeviceId();
    // record.fcmToken = await _messagingService.getToken();

    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc['createDate'] = DateTimeUtil.currentMilliseconds();

    await userAuthRecordRef.set(recordDoc);
  }

  Future<void> updateUserAuthRecord(UserAuthRecord record, {bool mergeOption = false}) async {

    DocumentReference userAuthRecordRef = _userAuthRecordCollection.doc(record.sid);
    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc['createDate'] = DateTimeUtil.currentMilliseconds();

    await userAuthRecordRef.set(recordDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteUserAuthRecord(String userAuthRecordId) async {

    DocumentReference userAuthRecordRef = _userAuthRecordCollection.doc(userAuthRecordId);
    await userAuthRecordRef.delete();
  }

  Future<void> softDeleteUserAuthRecord(String userAuthRecordId) async {

    DocumentReference userAuthRecordRef = _userAuthRecordCollection.doc(userAuthRecordId);
    await userAuthRecordRef.set({CommonField.isValid: 0});
  }

  Future<UserAuthRecord?> getLatestUserAuthRecordById ({String? userAuthRecordId, String? userId}) async {

    // by user id
    if (userAuthRecordId != null) {
      DocumentSnapshot doc = await _userDataCollection.doc(userAuthRecordId).get();

      if (doc.exists) {
        return UserAuthRecord.fromFirestoreData(doc);
      } else {
        return null;
      }
    }
    // by firebase id
    else {
      QuerySnapshot querySnapshot = await _userDataCollection
          .where('uid', isEqualTo: userId!)
          .orderBy('create_date', descending: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return UserAuthRecord.fromFirestoreData(querySnapshot.docs.first);
      } else {
        return null;
      }
    }
  }

  // user data ----------------------------------------------
  Future<void> addUserData (UserData data) async {

    DocumentReference userDataRef = _userDataCollection.doc(data.sid);
    Map<String, dynamic> dataDoc = data.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await userDataRef.set(dataDoc);
  }

  Future<void> updateUserData (UserData data, {bool mergeOption = false}) async {

    DocumentReference userDataRef = _userDataCollection.doc(data.sid!);
    Map<String, dynamic> dataDoc = data.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await userDataRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteUserData (String userDataId) async {

    DocumentReference userDataRef = _userDataCollection.doc(userDataId);
    await userDataRef.delete();
  }

  Future<void> softDeleteUserData (String userDataId) async {

    DocumentReference userDataRef = _userDataCollection.doc(userDataId);
    await userDataRef.set({CommonField.isValid: 0});
  }

  Future<UserData?> getUserDataById ({String? userDataId, String? userId}) async {

    // by user id
    if (userDataId != null) {
      DocumentSnapshot doc = await _userDataCollection.doc(userDataId).get();

      if (doc.exists) {
        return UserData.fromFirestoreData(doc);
      } else {
        return null;
      }
    }
    // by firebase id
    else {
      QuerySnapshot querySnapshot = await _userDataCollection
          .where('uid', isEqualTo: userId!)
          .get();

      if (querySnapshot.docs.isNotEmpty) {

        return UserData.fromFirestoreData(querySnapshot.docs.first);
      } else {
        return null;
      }
    }
  }

  Future<List<UserData>?> getUserDataList(List<String> uids) async {

    final batches = <Future<QuerySnapshot>>[];

    for (int i = 0; i < uids.length; i += 10) {
      final batchIds = uids.sublist(
          i, i + 10 > uids.length ? uids.length : i + 10);
      final query = _userDataCollection
          .where(FieldPath.documentId, whereIn: batchIds)
          .get();
      batches.add(query);
    }

    final snapshots = await Future.wait(batches);
    return snapshots.expand((s) =>
        s.docs.map((doc) => UserData.fromFirestoreData(doc))).toList();
  }
}

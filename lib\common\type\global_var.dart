import 'package:flutter/cupertino.dart';

Size screenSize = const Size(100, 100);
double devicePixelRatio = 1.0;
String timeZoneName = 'America/Vancouver';
Locale? preferenceLocale;
typedef AsyncVoidCallback = Future<void> Function();
typedef AsyncCallbackWithContext = Future<void> Function(BuildContext context);
typedef AsyncCallbackWithContextAndResult = Future<void> Function({required BuildContext context, required bool result});

// Profile config
bool userDataEmailPopUpEveryTime = false;
bool userDataNamePopUpEveryTime = true;
bool userDataAddressPopUpEveryTime = false;
bool userDataPetListPopUpEveryTime = false;

bool userDataEmailSkippedThisTime = false;
bool userDataNameSkippedThisTime = false;
bool userDataAddressSkippedThisTime = false;
bool userDataPetListSkippedThisTime = false;
import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../services/auth_service.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingContactPage extends StatefulWidget {
  const UserSettingContactPage({super.key});

  @override
  UserSettingContactPageState createState() => UserSettingContactPageState();
}

class UserSettingContactPageState extends State<UserSettingContactPage> {
  late Future<UserSettingContactPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserSettingContactPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingContactPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
                theme: controller.theme,
                backgroundColor:
                    controller.theme.themeData.colorScheme.onPrimary,
                body: Stack(
                  children: [
                    ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Contact us",
                      showAvatar: false,
                    ),
                    ProfileWidgetBuilder.buildText2(
                      controller.theme,
                      128,
                      "If you have any questions, just contact"
                          "\<EMAIL>"
                          " and we will be\nmore than happy to help.",
                    ),
                  ],
                ));
          }
        });
  }
}

class UserSettingContactPageController extends GetxController {
  late ThemePlugin theme;

  Future<UserSettingContactPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());
    return this;
  }
}

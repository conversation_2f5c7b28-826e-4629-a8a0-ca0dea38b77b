import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/geo_i.dart';
import 'base_full_model.dart';

part 'user_data.g.dart';

@JsonSerializable()
class UserData extends BaseFullModel {

  String uid;
  String? avatar;
  String? bio;
  String? userType;
  String? firstName;
  String? lastName;
  GeoPostalAddress? location;
  // Community fields
  int? followingCount;
  int? followersCount;
  int? friendsCount;
  int? postsCount;
  int? likedCount;
  int? favoredCount;
  PublishVisibility? publishVisibility;
  int? publishVisibleDays;
  // int petsCount; TODO move to ownership to support ownership transfer

  UserData({
    required this.uid,
    this.avatar,
    this.bio,
    this.userType,
    this.firstName,
    this.lastName,
    this.location,
    this.followingCount,
    this.followersCount,
    this.friendsCount,
    this.postsCount,
    this.likedCount,
    this.favoredCount,
    this.publishVisibility,
    this.publishVisibleDays,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory UserData.fromJson(Map<String, dynamic> json) => _$UserDataFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$UserDataToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'location') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoPostalAddress.fromFirestore(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory UserData.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return UserData.fromJson(jsonData);
  }

  factory UserData.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return UserData.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'location') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoPostalAddress.toFirestoreJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static String get collection => 'user_data';

  static create ({
    required String uid,
    String? avatar,
    String? bio,
    String? userType,
    String? firstName,
    String? lastName,
    GeoPostalAddress? location,
    int? followingCount,
    int? followersCount,
    int? friendsCount,
    int? postsCount,
    int? likedCount,
    int? favoredCount,
    PublishVisibility? publishVisibility,
    int? publishVisibleDays,
  }) {

    return UserData(
      sid: uuid.v4(),
      uid: uid,
      avatar: avatar,
      bio: bio,
      userType: userType,
      firstName: firstName,
      lastName: lastName,
      location: location,
      followingCount: followingCount?? 0,
      followersCount: followersCount?? 0,
      friendsCount: friendsCount?? 0,
      postsCount: postsCount?? 0,
      likedCount: likedCount?? 0,
      favoredCount: favoredCount?? 0,
      publishVisibility: publishVisibility?? PublishVisibility.public,
      publishVisibleDays: publishVisibleDays,
      isValid: true,
    );
  }

  static copyFrom(UserData other) {

    return UserData(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      avatar: other.avatar,
      bio: other.bio,
      userType: other.userType,
      firstName: other.firstName,
      lastName: other.lastName,
      location: GeoPostalAddress.copyFrom(other.location),
      followingCount: other.followingCount,
      followersCount: other.followersCount,
      friendsCount: other.friendsCount,
      postsCount: other.postsCount,
      likedCount: other.likedCount,
      favoredCount: other.favoredCount,
      publishVisibility: other.publishVisibility,
      publishVisibleDays: other.publishVisibleDays,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../component/auth/auth_widget_builder.dart';

class CheckEmailPage extends StatefulWidget {
  const CheckEmailPage({super.key});

  @override
  CheckEmailPageState createState() => CheckEmailPageState();
}

class CheckEmailPageState extends State<CheckEmailPage> {
  late Future<CheckEmailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => CheckEmailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CheckEmailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  //_buildBackButton(),
                  //_buildAvatar(),
                  //_buildWhiteSection(),
                  //_buildWelcomeText(controller),
                  //_buildTextLabel(controller),
                  //_buildSubmitButton(controller),
                  AuthWidgetBuilder.buildBackgroundImage(controller.theme),
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  AuthWidgetBuilder.buildPositionAvatar(
                      controller.theme, context),
                  ListView(children: [
                    SizedBox(
                      height:
                          controller.theme.layout.authAvatarHeaderLineHeight,
                    ),
                    AuthWidgetBuilder.buildCheckEmailLabel(controller.theme),
                    SizedBox(height: 409.w,),
                    AuthWidgetBuilder.buildButton(controller.theme, controller.buttonController,text: 'continue',isEnabled: true),
                  ]),
                  //AuthWidgetBuilder.buildButton(controller.theme, controller.buttonController,text: 'continue',isEnabled: true),
                  //CommonText('welcome'.t18, controller.theme.textStyleExtension.authPageTitle),
                  //AuthWidgetBuilder.buildTextButton(),
                ],
              ),
            );
          }
        });
  }

  // Widget _buildBackButton() {
  //   return CommonBackButton(
  //     onPressed: () {
  //       Get.back();
  //     },
  //   );
  // }
  //
  // Widget _buildAvatar() {
  //   return Positioned(
  //     width: 447.11.w,
  //     height: 443.96.w,
  //     left: -22.w,
  //     child: Image.asset(
  //       'assets/images/welcome.png',
  //     ),
  //   );
  // }
  //
  // Widget _buildWhiteSection() {
  //   return const CommonWhiteSection(
  //     width: 403,
  //     height: 639,
  //     top: 222,
  //     borderRadius: 85,
  //   );
  // }
  //
  // Widget _buildWelcomeText(CheckEmailPageController controller) {
  //   return Positioned(
  //     top: 274.w,
  //     left: 38.w,
  //     child: Text(
  //       'welcome'.t18,
  //       style: controller.theme.textStyleExtension.authPageTitle,
  //     ),
  //   );
  // }
  //
  // Widget _buildTextLabel(CheckEmailPageController controller) {
  //   return Positioned(
  //     top: 307.w,
  //     left: 38.w,
  //     child: Text(
  //       'check.email.verify.account'.t18,
  //       style: controller.theme.textStyleExtension.authPageDesc,
  //     ),
  //   );
  // }
  //
  // Widget _buildSubmitButton(CheckEmailPageController controller) {
  //   return Positioned(
  //     top: 721.w,
  //     left: 38.w,
  //     child: CommonButton(
  //       text: 'continue'.t18,
  //       onPressed: () => Get.toNamed('/signUp'),
  //       backgroundColor: controller.theme.themeData.colorScheme.secondary,
  //       textColor: controller.theme.themeData.colorScheme.onPrimary,
  //       borderColor: Colors.transparent,
  //       width: 327.w,
  //       height: 60.w,
  //     ),
  //   );
  // }
}

class CheckEmailPageController extends GetxController {
  // Theme plugin
  late ThemePlugin theme;
  final CommonButtonController buttonController = Get.put(CommonButtonController(), tag: 'sign-up-submit');
  Future<CheckEmailPageController> init() async {
    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    return this;
  }
}

import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import '../common/utils/json_util.dart';
import 'geo_i.dart';

part 'geo_reviews.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoReviews {
  List<GeoAuthorAttribution>? authorAttributions;
  String? name;
  String? rating; // rating score
  GeoReviewsText? text; //review text (normal is en)
  GeoReviewsText? originalText; //original text
  String? authorUri;
  DateTime? publishTime;
  String? flagContentUri;
  String? googleMapsUri;

  GeoReviews({
    this.authorAttributions,
    this.name,
    this.rating,
    this.text,
    this.originalText,
    this.authorUri,
    this.publishTime,
    this.flagContentUri,
    this.googleMapsUri,
  });

  factory GeoReviews.fromJson(Map<String, dynamic> json) =>
      _$GeoReviewsFromJson(json);
  Map<String, dynamic> toJson() => _$GeoReviewsToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'authorAttributions') {
        jsonData[key] = (value as List)
            .map((e) => GeoAuthorAttribution.fromApi(e))
            .toList();
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoReviews.fromApiMap(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoReviews.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'author_attributions') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            (map['author_attributions'] ?? [])
                .map((e) => GeoAuthorAttribution.fromFirestore(e))
                .toList();
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoReviews.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoReviews.fromJson(jsonData);
  }

  factory GeoReviews.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoReviews.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'authorAttributions') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            (jsonData['authorAttributions'] ?? [])
                .map((e) => GeoAuthorAttribution.toFirestoreJson(e))
                .toList();
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoPhoto? o) {
    return o?.toFirestoreData() ?? {};
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  static create({
    List<GeoAuthorAttribution>? authorAttributions,
    String? name,
    String? rating, // rating score
    GeoReviewsText? text, //review text (normal is en)
    GeoReviewsText? originalText, //original text
    String? authorUri,
    DateTime? publishTime,
    String? flagContentUri,
    String? googleMapsUri,
  }) {
    return GeoReviews(
      authorAttributions: authorAttributions,
      name: name,
      rating: rating,
      text: text,
      originalText: originalText,
      authorUri: authorUri,
      publishTime: publishTime,
      flagContentUri: flagContentUri,
      googleMapsUri: googleMapsUri,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'authorAttributions': this.authorAttributions,
      'name': this.name,
      'rating': this.rating,
      'text': this.text,
      'originalText': this.originalText,
      'authorUri': this.authorUri,
      'publishTime': this.publishTime,
      'flagContentUri': this.flagContentUri,
      'googleMapsUri': this.googleMapsUri,
    };
  }

  factory GeoReviews.fromMap(Map<String, dynamic> map) {
    return GeoReviews(
      authorAttributions:
          map['authorAttributions'] as List<GeoAuthorAttribution>,
      name: map['name'] as String,
      rating: map['rating'] as String,
      text: map['text'] as GeoReviewsText,
      originalText: map['originalText'] as GeoReviewsText,
      authorUri: map['authorUri'] as String,
      publishTime: map['publishTime'] as DateTime,
      flagContentUri: map['flagContentUri'] as String,
      googleMapsUri: map['googleMapsUri'] as String,
    );
  }
}

{"app.name": "OneNata", "common.button.ok": "OK", "common.button.confirm": "Confirm", "common.button.cancel": "Cancel", "common.button.skip": "Skip for now", "common.button.acknowledge": "Acknowledged", "common.exception.title": "Temporary error", "common.today": "Today", "common.today.is": "Today is", "common.today.back": "< Today", "common.age.year": "years", "common.age.month": "months", "common.app.init": "Initializing app. Do not quit...", "app.navigation.wellness": "Wellness", "app.navigation.shop": "Shop", "app.navigation.home": "Home", "app.navigation.community": "Community", "app.navigation.profile": "Profile", "app.navigation.record": "Record", "app.navigation.friends": "Friends", "api.exception.not_found": "Not found", "api.exception.duplicate": "Duplicate, already exists", "api.exception.network.issue.title": "Network issue", "api.exception.network.issue.timeout": "Timeout", "api.exception.network.issue.connection": "Bad connection", "api.exception.network.issue.cert": "Bad certificate", "api.exception.network.issue.unknown": "Unknown error", "api.exception.service_not_available": "Sorry, we're facing problem to handle your request, please try again later", "api.exception.not_authenticated": "Please sign in to continue", "api.exception.client.error": "Apologize for the inconvenience. Please try again later. If the problem continues you can manually input the result by clicking the button on right bottom or contact us.", "api.response.status.code.400": "400, Bad request", "api.response.status.code.401": "401, Not authorized", "api.response.status.code.403": "403, No permission", "api.response.status.code.404": "404, Not found", "api.response.status.code.405": "405, Method not allowed", "api.response.status.code.408": "408, Request timeout", "api.response.status.code.409": "409, Resource conflict", "api.response.status.code.500": "500, Server error", "api.response.status.code.501": "501, Method not implemented", "api.response.status.code.502": "502, Bad gateway", "api.response.status.code.503": "503, Service not available", "api.response.status.code.504": "504, Gateway timeout", "api.response.status.code.505": "505, HTTP version not supported", "auth.hint.email": "Email", "auth.hint.phone": "Phone", "auth.hint.password": "Password", "auth.hint.re-enter.password": "Re-enter password", "auth.hint.verification.code": "Verification code", "auth.hint.invalid.password": "Invalid email or password", "auth.hint.old.password": "Old password", "auth.hint.new.password": "New password", "auth.hint.new.password.repeat": "Re-enter new password", "auth.hint.password.rule.title": "Your password must contain:", "auth.hint.password.rule.1": " • At least 8 characters", "auth.hint.password.rule.2": " • At least 1 number", "auth.hint.password.rule.3": " • At least 1 uppercase letter", "auth.hint.password.rule.4": " • At least 1 special character including #?!@$%^&*-", "auth.label.invalid.verification.code": "Oops, invalid code", "auth.label.signup": "Signup", "auth.label.signup.phone": "Enter your phone number", "auth.label.signup.email": "Enter your email and password", "auth.label.login": "<PERSON><PERSON>", "auth.label.login.phone": "Enter your phone number", "auth.label.login.code.sent": "Verification code has been sent", "auth.label.login.code.timeout": "Code expired, click resend if needed", "auth.label.login.email": "Enter your email and password", "auth.label.login.email.sent": "Account created. Verify your email to continue", "auth.label.verify": "Welcome", "auth.label.verify.desc": "Please check your email to verify your account", "auth.label.link.phone.number": "Verify your number", "auth.label.link.email": "Link email address", "auth.label.update.phone.number": "Update your number", "auth.label.link.phone.number.desc": "Please enter your phone number", "auth.label.link.email.desc": "Email password as extra login method", "auth.label.reset.password": "Reset password", "auth.label.reset.password.enter.email": "A link has been sent to your email. Please reset password and re-login again.", "auth.label.reset.password.hello": "Hello {name}!", "auth.label.reset.password.default.name": "friend", "auth.label.reset.password.desc": "A request has been made to reset your password. If you made this request, please continue to reset your password", "auth.label.reset.password.success.title": "Great!", "auth.label.reset.password.success.desc": "You’ve successfully reset your password, please log in with your new password", "auth.button.login": "Send verification link", "auth.button.login.2": "<PERSON><PERSON>", "auth.button.send.link": "Send verification link", "auth.button.send.code": "Send verification code", "auth.button.resend": "Resend", "auth.button.resend.in.second": "Resend code in {second}s", "auth.button.verify.code": "Continue", "auth.button.change.to.phone": "Use phone number?", "auth.button.change.to.email": "Use email and password?", "auth.button.forgot.password": "Forgot password?", "auth.button.reset.password.send": "Send", "auth.button.reset.password.continue": "Continue", "auth.error.title": "Warning", "auth.error.desc": "Not success, please try again later", "auth.error.number.invalid": "The format of the phone number provided is incorrect. Please enter the phone number in a format that can be parsed into E.164 format.", "auth.error.number.exists": "Phone already already exists for that email.", "auth.error.email.exists": "Email address already exists for that email.", "auth.error.email.invalid": "Please enter a valid email address", "auth.error.password.not.match": "Two passwords do not match", "auth.error.password.not.correct": "Passwords format is not correct", "auth.error.login.failed": "<PERSON><PERSON> failed, please check your email and password", "auth.error.code.invalid": "Cannot verify, please check the code and try again", "auth.error.password.weak": "Password is too weak", "auth.error.reset.password.failed": "Cannot reset password, please try again later", "pet.info.gender.boy": "Boy", "pet.info.gender.girl": "Girl", "pet.info.type.cat": "Cat", "pet.info.type.dog": "Dog", "pet.info.type.others": "Others", "pet.visibility.public": "Nearby users can see your pet", "pet.visibility.private": "Only you can see your pet", "pet.visibility.friend": "Your friends can see your pet", "geo.place.filter.type.outdoor": "Outdoor", "geo.place.filter.type.park": "Dog parks", "geo.place.filter.type.trail": "Off-leashed trails", "geo.place.filter.type.vet": "Vets", "geo.place.filter.type.favor": "Favorites", "geo.place.filter.type.pet.store": "Pet stores", "geo.place.filter.type.dog.friendly.restaurant": "Dog friendly restaurants", "geo.search.hint": "Search places, stores or services", "device.type.feeding.machine": "Feeding machine", "device.type.locator": "Geo locator", "device.model.pifi.01": "PiFI 01", "device.model.locator.01": "Locator 01", "device.message.type.mo.provision": "Device provision", "device.message.type.mo.status": "Status update", "device.message.type.mo.alert": "<PERSON><PERSON>", "device.message.type.mo.record.geo": "Geo location record", "device.message.type.mo.record.feeding": "Feeding record", "device.message.alert.battery.low": "Low battery", "device.message.alert.gps.not.available": "GPS not available", "community.post.content.type.text": "Text", "community.post.content.type.image": "Image", "community.post.content.type.video": "Video", "community.publish.visibility.public": "Public", "community.publish.visibility.private": "Private", "community.publish.visibility.friend": "Friend", "save": "Save", "success.key": "Success", "fail.key": "Failed", "error.key": "Error occurred", "action.frequently": "Processing the data, please try again later", "app.title": "Danta & Croissant", "welcome.page.title": "Welcome To OneNata", "welcome.page.button.start": "Get Started", "welcome.page.button.login": "Log in", "phone": "Phone", "verification.code": "Verification code", "change.to.phone": "Use phone number?", "send": "Send", "email": "Email", "password": "Password", "reenter.password": "Re-enter password", "password.rule.text": "Your password must contain:", "password.rule.1": " • At least 8 characters", "password.rule.2": " • At least 1 number", "password.rule.3": " • At least 1 uppercase letter", "password.rule.4": " • At least 1 special character (e.g. #\\$!%)", "forgot.password": "Forgot password?", "reach.limit": "You've reached the maximum number of attempts,\nplease reset your password to continue", "welcome": "Welcome!", "check.email.verify.account": "Please check your email to verify your account", "enter.email": "Enter your email and password", "please.check.email": "Please check your inbox for the link to continue", "hello": "Hello", "reset.password.confirmation": "A request has been made to reset your\npassword. If you made this request, please\ncontinue to reset your password", "new.password": "New password", "great": "Great!", "reset.password.success": "You’ve successfully reset your password,\nplease log in with your new password", "select.pet": "Select your pets", "skip": "Skip for now", "dog": "Dog", "cat": "Cat", "other": "Other", "add.pet": "Add your pet", "breed": "Breed", "name": "Name", "weight": "Weight", "device.home.title": "My Devices", "device.home.search.hint": "Search device name or brand...", "device.home.stats.online": "Online Devices", "device.home.stats.offline": "Offline Devices", "device.home.stats.total": "Total Devices", "device.home.status.online": "Online", "device.home.status.offline": "Offline", "device.home.pets.count": "Associated pets: {count}", "device.home.unknown.device": "Unknown Device", "device.home.firmware.version": "Firmware: {version}", "device.home.options.title": "Device Options", "device.home.options.settings": "<PERSON><PERSON>s", "device.home.options.manage.pets": "Manage Pets", "device.home.options.device.info": "Device Information", "device.home.options.delete": "Delete Device", "device.home.info.title": "Device Information", "device.home.info.device.type": "Device Type", "device.home.info.manufacturer": "Manufacturer", "device.home.info.serial.number": "Serial Number", "device.home.info.firmware.version": "Firmware Version", "device.home.info.hardware.version": "Hardware Version", "device.home.info.sync.status": "Sync Status", "device.home.info.synced": "Synced", "device.home.info.not.synced": "Not Synced", "device.home.delete.title": "Delete Device", "device.home.delete.confirm": "Are you sure you want to delete device \"{name}\"? This action cannot be undone.", "device.home.delete.success": "Device \"{name}\" has been deleted", "device.home.close": "Close", "device.home.delete.action": "Delete", "device.add.title": "Add <PERSON>", "device.add.save": "Save", "device.add.basic.info": "Basic Information", "device.add.device.name": "Device Name", "device.add.device.name.hint": "Enter device name (optional)", "device.add.device.type": "Device Type", "device.add.device.model": "Device Model", "device.add.manufacturer.info": "Manufacturer Information", "device.add.manufacturer": "Manufacturer", "device.add.manufacturer.hint": "Enter manufacturer name", "device.add.serial.number": "Serial Number", "device.add.serial.number.hint": "Enter device serial number", "device.add.version.info": "Version Information", "device.add.firmware.version": "Firmware Version", "device.add.firmware.version.hint": "Enter firmware version (optional)", "device.add.hardware.version": "Hardware Version", "device.add.hardware.version.hint": "Enter hardware version (optional)", "device.add.associated.pets": "Associated Pets", "device.add.select.pets": "Select pets to associate", "device.add.add.device": "Add <PERSON>", "device.add.field.required": "{field} is required", "device.add.select": "Select {field}", "device.add.required.fields.missing": "Please fill in all required fields", "device.add.success": "Device added successfully", "device.add.error": "Failed to add device, please try again", "device.home.empty.title": "No devices", "device.home.empty.subtitle": "Tap the add button in the bottom right\nto start adding your first device", "geo.business.hours.unknown": "Business hours unknown", "geo.business.status.unknown": "Business status unknown", "geo.business.status.open": "Open", "geo.business.status.closed": "Closed", "geo.business.time.unknown": "Time unknown", "geo.business.opens": "opens", "geo.business.closes": "closes", "geo.business.hours.unavailable": "Business hours information unavailable", "geo.report.hours": "Report hours", "geo.report.correction": "Report correction", "geo.place.unknown": "Unknown place", "geo.report.hours.title": "Report Business Hours", "geo.report.type.title": "Select report type", "geo.report.type.business.hours.title": "Report business hours", "geo.report.type.business.hours.description": "Provide business hours information for this place", "geo.report.type.current.status.title": "Report current status", "geo.report.type.current.status.description": "Report whether this place is currently open", "geo.report.type.correction.title": "Information correction", "geo.report.type.correction.description": "Report errors in existing information", "geo.report.business.hours.form.title": "Please fill in business hours", "geo.report.current.status.title": "Please select current status", "geo.report.correction.form.title": "Information correction", "geo.report.correction.form.description": "Please provide detailed information about what needs to be corrected in the notes below", "geo.report.status.open.now": "Currently open", "geo.report.status.closed.now": "Currently closed", "geo.report.status.temporarily.closed": "Temporarily closed", "geo.report.status.permanently.closed": "Permanently closed", "geo.weekday.sunday": "Sun", "geo.weekday.monday": "Mon", "geo.weekday.tuesday": "<PERSON><PERSON>", "geo.weekday.wednesday": "Wed", "geo.weekday.thursday": "<PERSON>hu", "geo.weekday.friday": "<PERSON><PERSON>", "geo.weekday.saturday": "Sat", "geo.time.open": "Open time", "geo.time.close": "Close time", "geo.report.notes.title": "Additional notes (optional)", "geo.report.notes.hint": "Please enter any additional information...", "geo.report.submit": "Submit report", "geo.report.success.title": "Report submitted", "geo.report.success.message": "Thank you for your report! We will process the information you provided as soon as possible.", "geo.report.submitted": "Report submitted, thank you for your feedback!", "geo.report.correction.title": "Report Correction", "geo.report.current.status": "Please select current operating status", "geo.report.notes": "Additional Notes (Optional)", "unknown": "Unknown", "Open": "Open", "Closed": "Closed", "Opens": "Opens", "Closes": "Closes", "birthday": "Birthday", "gender": "Gender", "country": "Country", "step": "Step", "breed.text": "What’s your pet’s breed?", "breed.hind": "Your pet’s breed", "name.text": "What’s your pet’s name?", "name.hind": "Your pet’s name", "weight.text": "What’s your pet’s weight?", "weight.hind": "kg", "birthday.text": "What’s your pet’s birthday?", "birthday.hind": "DD/MM/YY", "gender.text": "What’s your pet’s gender?", "male": "Male", "female": "Female", "country.text": "Where does your pet live?", "country.hind": "Select a country", "complete": "Completed!", "hello, ": "Hello, ", "add.profile": "Add your profile", "your.pets": "Your Pets", "add.new": "add new", "contact.info": "Contact info", "help": "Help", "account": "Account", "save.changes": "Save changes", "save.notification": "All changes are saved", "mappage.search.hint": "Search", "mappage.filterButton.dogPark": "Dog Park", "mappage.filterButton.offLeashTrail": "Off Leash Trail", "mappage.filterButton.favourite": "Favourite", "mappage.placeInfoCard.navigationToHere": "Route", "mappage.placeInfoCard.comments": "Comments", "mappage.placeInfoCard.noAddress": "No Address Info", "mappage.placeInfoCard.unknownAddress": "Unknown Address", "place.unknown": "Unknown Place", "place.reviews": "reviews", "place.business.hours": "Business Hours", "place.hours.unknown": "Hours unknown", "place.address": "Address", "place.checkin": "Check In"}
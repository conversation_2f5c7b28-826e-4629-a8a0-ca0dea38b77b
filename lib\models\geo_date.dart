import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

part 'geo_date.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoDate {

  int? year;
  int? month;
  int? day;

  GeoDate({
    this.year,
    this.month,
    this.day,
  });

  factory GeoDate.fromJson(Map<String, dynamic> json) => _$GeoDateFromJson(json);
  Map<String, dynamic> toJson() => _$GeoDateToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory GeoDate.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoDate.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory GeoDate.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoDate.fromJson(jsonData);
  }

  factory GeoDate.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoDate.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoDate? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({

    int? year,
    int? month,
    int? day,
  }) {

    return GeoDate(
      year: year,
      month: month,
      day: day,
    );
  }

  static GeoDate copyFrom(GeoDate? other) {
    return GeoDate(
      year: other?.year,
      month: other?.month,
      day: other?.day,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/page/profile/user_setting_privacy_community_notification_page.dart';
import 'package:onenata_app/views/page/profile/user_setting_privacy_device_alert_page.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';
import 'user_setting_privacy_weather_reminder_page.dart';

class UserSettingPrivacyPage extends StatefulWidget {
  const UserSettingPrivacyPage({super.key});

  @override
  State<UserSettingPrivacyPage> createState() => _UserSettingPrivacyPageState();
}

class _UserSettingPrivacyPageState extends State<UserSettingPrivacyPage> {
  late Future<UserSettingPrivacyPageController> _controller;
  bool weatherRemindersEnabled = false;
  Map<String, bool> weatherReminderOptions = {
    "Get Severe Weather Alerts": false,
    "Get Good Weather Tips": false,
    "Use My Location": false,
    "Smart Reminder Timing": false,
  };

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => UserSettingPrivacyPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingPrivacyPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final controller = snapshot.data!;
        return CommonPage(
          theme: controller.theme,
          backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProfileWidgetBuilder.buildUserSettingTopSection(
                theme: controller.theme,
                onBack: () => Get.back(),
                topic: 'Privacy Settings',
              ),
              _buildSectionItem(controller, 'Weather Reminder'),
              _buildSectionItem(controller, 'Device Alerts'),
              _buildSectionItem(controller, 'Community Notifications'),
              _buildSectionItem(controller, 'Appointment Reminders'),
              _buildSectionItem(controller, 'Vaccine Expiry Reminders'),
              _buildSectionItem(controller, 'Health Alerts'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionItem(
      UserSettingPrivacyPageController controller, String title) {
    return ListTile(
      title: Text(title,
          style: controller.theme.textStyleExtension.userProfileSettingBody),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        if (title == 'Weather Reminder') {
          Get.to(() => const UserSettingWeatherReminderPage());
        }
        if (title == 'Device Alerts') {
          Get.to(() => const UserSettingDeviceAlertPage());
        }
        if (title == 'Community Notifications') {
          Get.to(() => const UserSettingCommunityNotificationPage());
        }
      },
    );
  }
}

class UserSettingPrivacyPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  //var userAccount = Rx<UserAccount?>(null);
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  late ThemePlugin theme;

  Future<UserSettingPrivacyPageController> init() async {
    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    //userAccount.value = _authService.userAccount.value;

    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(theme,
        userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);
    return this;
  }
}

import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

class NewPasswordPage extends StatefulWidget {
  const NewPasswordPage({super.key});

  @override
  NewPasswordPageState createState() => NewPasswordPageState();
}

class NewPasswordPageState extends State<NewPasswordPage> {
  late Future<NewPasswordPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => NewPasswordPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<NewPasswordPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Indicator for loading
          return const Center(
            child: LoadingWidget(),
          );
        } else if (snapshot.hasError) {
          // Error handling
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          // Page functions
          final controller = snapshot.data!;

          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.primary,
            body: Stack(
              children: [
                AuthWidgetBuilder.buildWhiteSection(controller.theme),
                Column(
                  children: [
                    // header
                    SizedBox(
                      height: controller.theme.layout.authAvatarHeaderLineHeight
                    ),

                    // Reset password label
                    SizedBox(
                      height: 149.w,
                      child: Column(
                        children: [
                          Expanded(child: SizedBox.shrink()),
                          AuthWidgetBuilder.buildLabel(
                            controller.theme,
                            title: CommonText(
                              'auth.label.reset.password'.t18,
                              controller.theme.textStyleExtension.authPageTitle
                            ),
                            desc: SizedBox.shrink()),
                          SizedBox(height: 40.w,),
                        ],
                      )
                    ),

                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        controller: controller.scrollController,
                        child: Column(
                          children: [

                            // old password input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.oldPasswordInputController,
                                )),
                            SizedBox(height: 10.w,),

                            // new password input box 1
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.newPasswordInputController1,
                                )),
                            SizedBox(height: 10.w,),

                            // new password input box 2
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.newPasswordInputController2,
                                )),
                            SizedBox(height: 10.w,),

                            SizedBox(
                                height: screenSize.height
                                    - controller.theme.layout.authAvatarHeaderLineHeight
                                    - 149.w
                                    - 210.w
                                    - 90.w,
                                child: Obx(()=> (controller.oldPasswordInputController.isFocused.isTrue ||
                                    controller.newPasswordInputController1.isFocused.isTrue ||
                                    controller.newPasswordInputController2.isFocused.isTrue )
                                  ? SingleChildScrollView(
                                    child: AuthWidgetBuilder.buildPasswordRequirementPanel(controller.theme))
                                  : SizedBox.shrink(),
                                )
                            ),
                            // Expanded(child: SizedBox.shrink()),

                            // submit button
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.buttonController,
                                )),

                            SizedBox(height: 30.w,),

                          ]
                        )
                      )
                    )
                  ],
                ),
                AuthWidgetBuilder.buildAvatarHeaderLine(
                  controller.theme, context,
                  avatar: controller.pageHeaderUserAvatar.value,
                  title: controller.pageHeaderUserName.value,
                ),
              ],
            ),
          );
        }
      }
    );
  }
}

class NewPasswordPageController extends GetxController {

  // Services
  final UserService _userService = UserService();
  final AuthService _authService = AuthService.instance;
  final CloudStorage _cloudStorage = CloudStorage();
  final LocalStorage _localStorage = LocalStorage.instance;

  // TextEditingController
  final TextEditingController oldPasswordInputBox = TextEditingController();
  late CommonTextField3Controller oldPasswordInputController;
  final TextEditingController newPasswordInputBox1 = TextEditingController();
  late CommonTextField3Controller newPasswordInputController1;
  final TextEditingController newPasswordInputBox2 = TextEditingController();
  late CommonTextField3Controller newPasswordInputController2;

  final ScrollController scrollController = ScrollController();
  final _oldPasswordKey = GlobalKey(debugLabel: 'oldPasswordKey${DateTime.now().millisecondsSinceEpoch}');
  final _passwordKey = GlobalKey(debugLabel: 'passwordKey${DateTime.now().millisecondsSinceEpoch}');
  final _confirmKey = GlobalKey(debugLabel: 'confirmKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;

  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  // var onPressed = Rx<AsyncCallback?>(null);

  var isOldPasswordValid = false.obs;
  var isNewPassword1Valid = false.obs;
  var isNewPassword2Valid = false.obs;

  // Theme plugin
  late ThemePlugin theme;

  Future<NewPasswordPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    // build old password input controller
    oldPasswordInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, oldPasswordInputBox.obs,
      globalKey: _oldPasswordKey.obs,
      hint: 'auth.hint.password'.t18.obs,
      onCleared: onOldPasswordCleared.obs,
      onChanged: isInputOldPasswordValid.obs,
    );
    // Listen to the focus node
    oldPasswordInputController.textFieldFocusNode.addListener(() {
      oldPasswordInputController.isFocused.value = oldPasswordInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_oldPasswordKey);
    });

    // build new password input controller 1
    newPasswordInputController1 = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, newPasswordInputBox1.obs,
      globalKey: _passwordKey.obs,
      hint: 'auth.hint.new.password'.t18.obs,
      onCleared: onNewPassword1Cleared.obs,
      onChanged: isInputNewPassword1Valid.obs,
    );
    // Listen to the focus node
    newPasswordInputController1.textFieldFocusNode.addListener(() {
      newPasswordInputController1.isFocused.value = newPasswordInputController1.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_passwordKey);
    });

    // build new password input controller 2
    newPasswordInputController2 = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, newPasswordInputBox2.obs,
      globalKey: _confirmKey.obs,
      hint: 'auth.hint.new.password.repeat'.t18.obs,
      onCleared: onNewPassword2Cleared.obs,
      onChanged: isInputNewPassword2Valid.obs,
    );
    // Listen to the focus node
    newPasswordInputController2.textFieldFocusNode.addListener(() {
      newPasswordInputController2.isFocused.value = newPasswordInputController2.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_confirmKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.reset.password.continue'.t18.obs,
      onPressed: resetPassword.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    // load avatar
    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
        theme, userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);

    // load user account
    UserAccount? userAccount = _authService.userAccount.value;
    pageHeaderUserName.value = CommonText(
      'auth.label.reset.password.hello'.t19({
        'name': userData!.firstName ?? 'auth.label.reset.password.default.name'.t18,
      }),
      theme.textStyleExtension.userAvatar,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    buttonController.isInProgress.value = false;
    oldPasswordInputController.textFieldFocusNode.unfocus();
    newPasswordInputController1.textFieldFocusNode.unfocus();
    newPasswordInputController2.textFieldFocusNode.unfocus();
    super.onClose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && (key == _oldPasswordKey && oldPasswordInputController.textFieldFocusNode.hasFocus ||
        key == _passwordKey && newPasswordInputController1.textFieldFocusNode.hasFocus ||
        key == _confirmKey && newPasswordInputController2.textFieldFocusNode.hasFocus)) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  bool checkPasswordMistake() {
    bool hasMistake = isNewPassword1Valid.isTrue && isNewPassword2Valid.isFalse && newPasswordInputBox2.text != '';
    return hasMistake;
  }

  Future<void> onOldPasswordCleared(BuildContext context) async {

    oldPasswordInputBox.text = '';
    isOldPasswordValid.value = false;
    isNewPassword1Valid.value = false;
    isNewPassword2Valid.value = false;
    FocusScope.of(context).unfocus(); // ✅ Close keyboard first
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> onNewPassword1Cleared(BuildContext context) async {

    newPasswordInputBox1.text = '';
    isNewPassword1Valid.value = false;
    isNewPassword2Valid.value = false;
    FocusScope.of(context).unfocus(); // ✅ Close keyboard first
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> onNewPassword2Cleared(BuildContext context) async {

    newPasswordInputBox2.text = '';
    isNewPassword2Valid.value = false;
    FocusScope.of(context).unfocus(); // ✅ Close keyboard first
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputOldPasswordValid({required BuildContext context, required bool result}) async {
    isOldPasswordValid.value = result;
    // FocusScope.of(context).unfocus();
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputNewPassword1Valid({required BuildContext context, required bool result}) async {
    isNewPassword1Valid.value = result;
    newPasswordInputController1.hasMistake.value = isNewPassword1Valid.isFalse && newPasswordInputBox1.text != '';
    newPasswordInputController2.hasMistake.value = isNewPasswordInput2HasMistake();

    if (isNewPassword1Valid.isTrue && isNewPassword2Valid.isTrue) {
      buttonController.isEnabled.value = true;
    } else {
      buttonController.isEnabled.value = false;
    }

    // FocusScope.of(context).unfocus();
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputNewPassword2Valid({required BuildContext context, required bool result}) async {
    isNewPassword2Valid.value = result;
    newPasswordInputController2.hasMistake.value = isNewPasswordInput2HasMistake();

    if (isNewPassword1Valid.isTrue && isNewPassword2Valid.isTrue) {
      buttonController.isEnabled.value = true;
    }else {
      buttonController.isEnabled.value = false;
    }

    // FocusScope.of(context).unfocus();
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  bool isNewPasswordInput2HasMistake() {

    if (newPasswordInputBox2.text != '') {

      if (newPasswordInputBox1.text != '' && newPasswordInputBox1.text != newPasswordInputBox2.text) {
        return true;
      } else {
        return isNewPassword2Valid.isFalse;
      }
    }

    return false;
  }

  Future<void> resetPassword() async {

    if (isNewPassword1Valid.isFalse ||
        isNewPassword2Valid.isFalse) {

      Get.snackbar('auth.error.title'.t18, 'auth.error.password.not.correct'.t18);
      return;
    }

    String oldPassword = oldPasswordInputBox.text;
    String newPassword1 = newPasswordInputBox1.text;
    String newPassword2 = newPasswordInputBox2.text;

    if (newPassword1 != newPassword2) {
      Get.snackbar('auth.error.title'.t18, 'auth.error.password.not.match'.t18);
      return;
    }

    // Login with email and password
    ServiceResponse<String> response = await _authService.updateUserPassword(oldPassword, newPassword1);

    if (response.code == 200) {
      Get.to(()=> ResetPasswordSuccessPage());
    }
    else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.reset.password.failed'.t18);
    }
  }
}

import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';

import 'package:onenata_app/models/geo_location.dart';

class JsonUtil {

  static String camelCaseToSnakeCase(String input) {
    final RegExp exp = RegExp(r'(?<=[a-z])[A-Z]');
    return input.replaceAllMapped(exp, (Match m) => ('_${m.group(0)!}')).toLowerCase();
  }

  static String snakeCaseToCamelCase(String input) {
    return input.split('_').mapIndexed((index, word) {
      if (index == 0) {
        return word;
      } else {
        return word[0].toUpperCase() + word.substring(1);
      }
    }).join('');
  }

  static bool? boolFromJson(dynamic json) {
    if (json == null) return null;
    return json == 1 || json == true;
  }

  static int? boolToJson(bool? value) {
    if (value == null) return null;
    return value ? 1 : 0;
  }

  static GeoPoint? geoPointFromJson(Map<String, dynamic>? json) {
    if (json == null || json.isEmpty) return null;
    return GeoPoint(json['latitude'] as double, json['longitude'] as double);
  }

  static Map<String, dynamic>? geoPointToJson(GeoPoint? geoPoint) {
    if (geoPoint == null) return null;
    return {
      'latitude': geoPoint.latitude,
      'longitude': geoPoint.longitude,
    };
  }

  static Timestamp? timestampFromJson(dynamic json) {
    if (json == null) return null;
    return json as Timestamp;
  }

  // TODO - exception in update operation. change to in
  static Timestamp? timestampToJson(Timestamp? value) {
    if (value == null) return null;
    return value;
  }

  static Map<String, dynamic>? mapFromJson(Map<String, dynamic>? json) {
    if (json == null || json.isEmpty) return null;
    return json;
  }

  static String listEncode(List<dynamic> list) {
    return jsonEncode(list.map((record) => record.toJson()).toList());
  }

  static String hashMapEncode(Map<dynamic, dynamic> hashMap) {
    return jsonEncode(hashMap.map((key, record) => MapEntry(key.toString(), record.toJson())));
  }

  static Map<String, dynamic>? geoFirePointToJson(GeoFirePoint? point) {
    if (point == null) return null;
    return point.data;
  }

  static GeoFirePoint? geoFirePointFromJson(Map<String, dynamic>? json) {
    if (json == null || json.isEmpty) return null;
    final geopoint = json['geopoint'] as GeoPoint;
    return GeoFirePoint(GeoPoint(geopoint.latitude, geopoint.longitude));
  }
}

extension IterableExtensions<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(int index, E e) f) {
    var index = 0;
    return map((e) => f(index++, e));
  }
}
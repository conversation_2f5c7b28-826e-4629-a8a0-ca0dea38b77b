import 'dart:io';

import 'package:flutter_timezone/flutter_timezone.dart';

import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import 'package:onenata_app/common/type/global_var.dart';

class TimezonePlugin {

  // Used after the app is initialized
  Future<void> init () async {
    await _configureLocalTimeZone();
  }

  Future<void> _configureLocalTimeZone() async {
    if (!Platform.isAndroid && !Platform.isIOS) {
      return;
    }
    tz.initializeTimeZones();
    timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }
}

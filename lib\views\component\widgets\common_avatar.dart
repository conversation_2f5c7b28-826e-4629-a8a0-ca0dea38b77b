import 'package:flutter/material.dart';

import 'package:onenata_app/views/theme/colors/colors_i.dart';

import 'common_bar.dart';
import 'common_image_asset.dart';

class CommonAvatar extends StatelessWidget {

  // Avatar background
  final bool? hasBg;
  final Widget? bg;

  // Avatar image OR icon OR text
  final bool? hasContent;
  final Widget? content;

  // Camera icon to change avatar image
  final bool? changeImgEnabled;
  final Widget? cameraIcon;
  final double? cameraIconTopMargin;


  // final String? cameraIcon;
  // final double? cameraIconWidth;
  // final double? cameraIconHeight;
  // final Color? cameraIconBgColor;
  // final double? cameraIconBgWidth;
  // final double? cameraIconBgHeight;
  // final double? cameraIconBgCircular;

  const CommonAvatar({
    super.key,
    this.hasBg = false,
    this.bg,
    this.hasContent,
    this.content,
    this.changeImgEnabled = false,
    this.cameraIcon,
    this.cameraIconTopMargin,
    // this.cameraIconWidth,
    // this.cameraIconHeight,
    // this.cameraIconBgColor,
    // this.cameraIconBgWidth,
    // this.cameraIconBgHeight,
    // this.cameraIconBgCircular,
    // required this.size,
    // this.borderSize = 0,
    // this.shadow,
    // required this.borderColor,
    // required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        !hasBg! ? SizedBox.shrink() : bg!,
        !hasContent! ? SizedBox.shrink() : content!,
        !changeImgEnabled! ? SizedBox.shrink() : Column(
          children: [
            SizedBox(height: cameraIconTopMargin!),
            cameraIcon!,
          ],
        ),
        // Avatar image
        // Container(
        //   width: size + (borderSize * 2), // TODO should not use size.w, input a value with .w to size.
        //   height: size + (borderSize * 2), // TODO use *.w instead of *.h
        //   decoration: BoxDecoration(
        //     shape: BoxShape.circle,
        //     color: borderColor,
        //     boxShadow: shadow ??
        //         [
        //           BoxShadow(
        //             // TODO change to theme.themeData.colorScheme or theme.themeData.colorExtension
        //             // TODO replace .withOpacity with .withAlpha(*.colorAlpha)
        //             color: Colors.black38
        //                 .withAlpha(0.15.colorAlpha),
        //             blurRadius: 12,
        //           ),
        //         ],
        //     border: borderSize > 0
        //         ? Border.all(color: borderColor, width: borderSize.w)
        //         : null,
        //   ),
        //   child: Padding(
        //     padding: EdgeInsets.all(borderSize.w),
        //     child: CircleAvatar(
        //       backgroundColor: Colors.transparent,
        //       backgroundImage: AssetImage(imagePath),
        //     ),
        //   ),
        // ),
        // Camera icon, clicked to replace avatar image
        // !changeImgEnabled ? SizedBox.shrink()
        // : SizedBox(
        //     width: size + (borderSize * 2), // TODO should not use size.w, input a value with .w to size.
        //     // height: size + (borderSize * 2), // TODO use *.w instead of *.h
        //   child: Column(
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       SizedBox(height: size - cameraIconHeight! / 2,),
        //       Stack(
        //         alignment: Alignment.center,
        //         children: [
        //           CommonBar(
        //             width: cameraIconBgWidth,
        //             height: cameraIconBgHeight!,
        //             color: cameraIconBgColor,
        //             circular: cameraIconBgCircular,
        //           ),
        //           CommonImageAsset(
        //             name: cameraIcon!,
        //             width: cameraIconWidth!,
        //             height: cameraIconHeight!,
        //           ),
        //         ],
        //       ),
        //     ],
        //   ),
        // )
      ],
    );
  }
}


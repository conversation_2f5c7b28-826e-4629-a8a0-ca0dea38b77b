import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';

class CommonBackButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const CommonBackButton({super.key, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      width: 46.w,
      height: 46.h,
      top: 60.h,
      left: 24.w,
      child: Container(
        decoration: BoxDecoration(
          color: OneNataClassicColors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: OneNataClassicColors.spaceCadet.withOpacity(0.07),
              offset: const Offset(4, 4),
              blurRadius: 20,
            ),
          ],
        ),
        child: IconButton(
          icon: Icon(Icons.arrow_back, size: 30, color: OneNataClassicColors.eerieBlack),
          onPressed: onPressed ?? Get.back,
        ),
      ),
    );
  }
}

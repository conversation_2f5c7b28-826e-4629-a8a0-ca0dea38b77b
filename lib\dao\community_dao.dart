import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

class CommunityDao {
  
  // Firestore connection
  final _db = FirebaseFirestore.instance;
  final _userColRef = FirebaseFirestore.instance.collection(UserData.collection);
  final _postColRef = FirebaseFirestore.instance.collectionGroup(Post.collection);
  final _mediaColRef = FirebaseFirestore.instance.collectionGroup(Media.collection);
  final _userDataCollection = FirebaseFirestore.instance.collection(UserData.collection);
  CollectionReference _userPostColRef (String uid) {
    return _userColRef.doc(uid).collection(Post.collection);
  }
  CollectionReference _userPostMediaColRef (String uid, String postId) {
    return _userColRef.doc(uid)
        .collection(Post.collection).doc(postId)
        .collection(Media.collection);
  }
  CollectionReference _userFollowingColRef (String uid) {
    return _userDataCollection.doc(uid).collection(Following.collection);
  }
  CollectionReference _userFollowerColRef (String uid) {
    return _userDataCollection.doc(uid).collection(Follower.collection);
  }
  CollectionReference _userFriendColRef (String uid) {
    return _userDataCollection.doc(uid).collection(Friend.collection);
  }
  CollectionReference _userLikeColRef (String uid) {
    return _userDataCollection.doc(uid).collection(Friend.collection);
  }
  CollectionReference _userFavorColRef (String uid) {
    return _userDataCollection.doc(uid).collection(Favorite.collection);
  }

  // Post operations
  Future<void> addPost(Post post) async {
    DocumentReference postRef = _userPostColRef(post.uid).doc(post.sid!);
    Map<String, dynamic> postDoc = post.toFirestoreData();
    postDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await postRef.set(postDoc);
  }
  Future<void> updatePost(Post post, {bool mergeOption = false}) async {

    DocumentReference postRef = _userPostColRef(post.uid).doc(post.sid!);
    Map<String, dynamic> postDoc = post.toFirestoreData();
    postDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await postRef.set(postDoc, SetOptions(merge: mergeOption));
  }
  Future<void> deletePost(String uid, String postId) async {
    DocumentReference postRef = _userPostColRef(uid).doc(postId);
    await postRef.delete();
  }
  Future<void> softDeletePost(String uid, String postId) async {
    DocumentReference postRef = _userPostColRef(uid).doc(postId);
    await postRef.update({CommonField.isValid: 0});
  }
  Future<Post?> getPostById (String uid, String postId) async {

    DocumentSnapshot doc = await _userPostColRef(uid).doc(postId).get();

    if (doc.exists) {
      return Post.fromFirestoreData(doc);
    }
    else {
      return null;
    }
  }
  Future<List<Post>?> getPosts ({PostType? type, String? uid, List<String>? pets, List<String>? keywords, bool? isValid, int? createDateFrom, int? createDateTo, int? pageSize, int? lastCreateDate}) async {

    Query query = (uid == null) ? _postColRef : _userPostColRef(uid);

    // Conditions
    if (type != null) {
      query = query.where('type', isEqualTo: type.code);
    }
    if (uid != null) {
      query = query.where('uid', isEqualTo: uid);
    }
    if (pets != null && pets.isNotEmpty) {
      query = query.where('pets', arrayContainsAny: pets);
    }
    if (keywords != null && keywords.isNotEmpty) {
      query = query.where('keywords', arrayContainsAny: keywords);
    }
    if (createDateFrom != null) {
      query = query.where('create_date', isGreaterThanOrEqualTo: createDateFrom);
    }
    if (createDateTo != null) {
      query = query.where('create_date', isLessThanOrEqualTo: createDateTo);
    }
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('create_date', descending: true);

    // Pagination start
    if (lastCreateDate != null) {
      query = query.startAfter([lastCreateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {

      List<Post> posts = querySnapshot.docs.map((doc) {
        return Post.fromFirestoreData(doc);
      }).toList();

      return posts;
    }
    else {
      return null;
    }
  }

  // Media operations
  Future<void> addMedia(Media media) async {
    DocumentReference mediaRef = _userPostMediaColRef(media.uid, media.postId!).doc(media.sid!);
    Map<String, dynamic> mediaDoc = media.toFirestoreData();
    mediaDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await mediaRef.set(mediaDoc);
  }
  Future<void> updateMedia(Media media, {bool mergeOption = false}) async {

    DocumentReference mediaRef = _userPostMediaColRef(media.uid, media.postId!).doc(media.sid!);
    Map<String, dynamic> mediaDoc = media.toFirestoreData();
    mediaDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await mediaRef.set(mediaDoc, SetOptions(merge: mergeOption));
  }
  Future<void> deleteMedia(String uid, String postId, String mediaId) async {

    CollectionReference mediaColRef = _userPostMediaColRef(uid, postId);
    DocumentSnapshot doc = await _userPostMediaColRef(uid, postId).doc(mediaId).get();
    if (doc.exists) {

      Media m = Media.fromFirestoreData(doc);
      Media? prefMedia;
      Media? nextMedia;

      // Previous media
      DocumentReference? prevRef = m.previousId != null
          ? _userPostMediaColRef(uid, postId).doc(m.previousId)
          : null;
      if (prevRef != null) {
        DocumentSnapshot prefDoc = await _userPostMediaColRef(uid, postId).doc(m.previousId).get();
        prefMedia = Media.fromFirestoreData(prefDoc);
      }

      // Next media
      DocumentReference? nextRef = m.nextId != null
          ? _userPostMediaColRef(uid, postId).doc(m.nextId)
          : null;
      if (nextRef != null) {
        DocumentSnapshot nextDoc = await _userPostMediaColRef(uid, postId).doc(m.nextId).get();
        nextMedia = Media.fromFirestoreData(nextDoc);
      }

      // transaction
      await _db.runTransaction((transaction) async {

        // update previous media's next id and next media's previous id
        if (prefMedia != null && nextMedia != null) {
          transaction.update(prevRef!, {'next_id': m.nextId});
          transaction.update(nextRef!, {'previous_id': m.previousId});
        }
        else if (prefMedia != null) {
          transaction.update(prevRef!, {'next_id': m.nextId});
        }
        else if (nextMedia != null) {
          transaction.update(nextRef!, {'previous_id': m.previousId});
        }

        transaction.delete(mediaColRef.doc(mediaId));
      });
    }
    else {
      return;
    }
  }
  Future<void> softDeleteMedia(String uid, String postId, String mediaId) async {

    CollectionReference mediaColRef = _userPostMediaColRef(uid, postId);
    DocumentSnapshot doc = await _userPostMediaColRef(uid, postId).doc(mediaId).get();

    if (doc.exists) {

      Media m = Media.fromFirestoreData(doc);
      Media? prefMedia;
      Media? nextMedia;

      // Previous media
      DocumentReference? prevRef = m.previousId != null
          ? _userPostMediaColRef(uid, postId).doc(m.previousId)
          : null;
      if (prevRef != null) {
        DocumentSnapshot prefDoc = await _userPostMediaColRef(uid, postId).doc(m.previousId).get();
        prefMedia = Media.fromFirestoreData(prefDoc);
      }

      // Next media
      DocumentReference? nextRef = m.nextId != null
          ? _userPostMediaColRef(uid, postId).doc(m.nextId)
          : null;
      if (nextRef != null) {
        DocumentSnapshot nextDoc = await _userPostMediaColRef(uid, postId).doc(m.nextId).get();
        nextMedia = Media.fromFirestoreData(nextDoc);
      }

      // transaction
      await _db.runTransaction((transaction) async {

        // update previous media's next id and next media's previous id
        if (prefMedia != null && nextMedia != null) {
          transaction.update(prevRef!, {'next_id': m.nextId});
          transaction.update(nextRef!, {'previous_id': m.previousId});
        }
        else if (prefMedia != null) {
          transaction.update(prevRef!, {'next_id': m.nextId});
        }
        else if (nextMedia != null) {
          transaction.update(nextRef!, {'previous_id': m.previousId});
        }

        transaction.update(mediaColRef.doc(postId), {CommonField.isValid: 0});
      });
    }
    else {
      return;
    }
  }
  Future<Media?> getMediaById (String uid, String postId, String mediaId) async {

    DocumentSnapshot doc = await _userPostMediaColRef(uid, postId).doc(mediaId).get();

    if (doc.exists) {
      return Media.fromFirestoreData(doc);
    }
    else {
      return null;
    }
  }
  Future<List<Media>?> getMedias (String uid, String postId, {bool? isValid}) async {

    Query query = _userPostMediaColRef(uid, postId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Query and sort
    QuerySnapshot querySnapshot = await query.get();

    if (querySnapshot.docs.isNotEmpty) {

      List<Media> ml = querySnapshot.docs.map((e)=> Media.fromFirestoreData(e)).toList();
      Media head = ml.firstWhere((e) => e.previousId == null);
      List<Media> rl = [];
      rl.add(head);
      while (head.nextId != null) {
        head = ml.firstWhere((e) => e.sid == head.nextId);
        rl.add(head);
      }
      return rl;

    } else {
      return null;
    }
  }

  // Comment operations
  // Future<void> addComment(Comment comment) async {
  //   DocumentReference commentRef = _userPostColRef(comment.uid).doc(comment.sid!);
  //   Map<String, dynamic> commentDoc = comment.toFirestoreData();
  //   commentDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();
  //
  //   await commentRef.set(commentDoc);
  // }

  // Community operations ----------------------------------------------

  Future<void> follow (Following data) async {

    DocumentReference followingDocRef = _userFollowingColRef(data.uid).doc(data.otherId);
    Map<String, dynamic> dataDoc = data.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await followingDocRef.set(dataDoc);
  }

  Future<void> unFollow (String uid, String otherId) async {

    DocumentReference followingDocRef = _userFollowingColRef(uid).doc(otherId);
    await followingDocRef.delete();
  }

  Future<void> like (Like data) async {

    DocumentReference likeDocRef = _userLikeColRef(data.uid).doc(data.otherId);
    Map<String, dynamic> dataDoc = data.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await likeDocRef.set(dataDoc);
  }

  Future<void> unLike (String uid, String otherId) async {

    DocumentReference likeDocRef = _userLikeColRef(uid).doc(otherId);
    await likeDocRef.delete();
  }

  Future<void> favor (Favorite data) async {

    DocumentReference favorDocRef = _userFavorColRef(data.uid).doc(data.otherId);
    Map<String, dynamic> dataDoc = data.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await favorDocRef.set(dataDoc);
  }

  Future<void> unFavor (String uid, String otherId) async {

    DocumentReference favorDocRef = _userFavorColRef(uid).doc(otherId);
    await favorDocRef.delete();
  }

}

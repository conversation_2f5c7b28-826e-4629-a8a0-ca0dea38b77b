{"common.button.ok": "好的", "common.button.confirm": "确认", "common.button.cancel": "取消", "common.button.acknowledge": "已知晓", "common.exception.title": "发生错误", "common.today": "今天", "common.today.is": "今天是", "common.today.back": "< 今天", "common.age.year": "年", "common.age.month": "月", "common.app.init": "正在初始化应用, 请勿退出...", "api.exception.not_found": "未发现", "api.exception.duplicate": "重复, 已存在", "api.exception.network.issue.title": "网络问题", "api.exception.network.issue.timeout": "超时错误", "api.exception.network.issue.connection": "链接错误", "api.exception.network.issue.cert": "证书失效", "api.exception.network.issue.unknown": "未知错误", "api.exception.service_not_available": "对不起, 服务暂时不可用, 给您造成不便请谅解", "api.exception.not_authenticated": "请登录后继续", "api.exception.client.error": "给您造成不便请谅解, 请稍后再试。如仍有问题, 您可以尝试手动录入结果或者联系我们。", "api.response.status.code.400": "400, Bad request", "api.response.status.code.401": "401, Not authorized", "api.response.status.code.403": "403, No permission", "api.response.status.code.404": "404, Not found", "api.response.status.code.405": "405, Method not allowed", "api.response.status.code.408": "408, Request timeout", "api.response.status.code.409": "409, Resource conflict", "api.response.status.code.500": "500, Server error", "api.response.status.code.501": "501, Method not implemented", "api.response.status.code.502": "502, Bad gateway", "api.response.status.code.503": "503, Service not available", "api.response.status.code.504": "504, Gateway timeout", "api.response.status.code.505": "505, HTTP version not supported", "save": "保存", "success.key": "成功", "fail.key": "失败", "error.key": "发生错误", "action.frequently": "正在处理中，请稍后再试", "mappage.search.hint": "搜索", "device.home.title": "我的设备", "device.home.search.hint": "搜索设备名称或品牌...", "device.home.stats.online": "在线设备", "device.home.stats.offline": "离线设备", "device.home.stats.total": "总设备", "device.home.status.online": "在线", "device.home.status.offline": "离线", "device.home.pets.count": "关联宠物: {count}只", "device.home.unknown.device": "未知设备", "device.home.firmware.version": "固件版本: {version}", "device.home.options.title": "设备选项", "device.home.options.settings": "设备设置", "device.home.options.manage.pets": "管理宠物", "device.home.options.device.info": "设备信息", "device.home.options.delete": "删除设备", "device.home.info.title": "设备信息", "device.home.info.device.type": "设备类型", "device.home.info.manufacturer": "制造商", "device.home.info.serial.number": "序列号", "device.home.info.firmware.version": "固件版本", "device.home.info.hardware.version": "硬件版本", "device.home.info.sync.status": "同步状态", "device.home.info.synced": "已同步", "device.home.info.not.synced": "未同步", "device.home.delete.title": "删除设备", "device.home.delete.confirm": "确定要删除设备 \"{name}\" 吗？此操作无法撤销。", "device.home.delete.success": "设备 \"{name}\" 已删除", "device.home.close": "关闭", "device.home.delete.action": "删除", "device.add.title": "添加设备", "device.add.save": "保存", "device.add.basic.info": "基本信息", "device.add.device.name": "设备名称", "device.add.device.name.hint": "输入设备名称（可选）", "device.add.device.type": "设备类型", "device.add.device.model": "设备型号", "device.add.manufacturer.info": "制造商信息", "device.add.manufacturer": "制造商", "device.add.manufacturer.hint": "输入制造商名称", "device.add.serial.number": "序列号", "device.add.serial.number.hint": "输入设备序列号", "device.add.version.info": "版本信息", "device.add.firmware.version": "固件版本", "device.add.firmware.version.hint": "输入固件版本（可选）", "device.add.hardware.version": "硬件版本", "device.add.hardware.version.hint": "输入硬件版本（可选）", "device.add.associated.pets": "关联宠物", "device.add.select.pets": "选择要关联的宠物", "device.add.add.device": "添加设备", "device.add.field.required": "{field} 为必填项", "device.add.select": "选择 {field}", "device.add.required.fields.missing": "请填写所有必填字段", "device.add.success": "设备添加成功", "device.add.error": "添加设备失败，请重试", "device.home.empty.title": "暂无设备", "device.home.empty.subtitle": "点击右下角的添加按钮\n开始添加您的第一个设备", "geo.search.hint": "搜索地点、商店或服务", "geo.business.hours.unknown": "营业时间未知", "geo.business.status.unknown": "营业状态未知", "geo.business.status.open": "营业中", "geo.business.status.closed": "已关闭", "geo.business.time.unknown": "时间未知", "geo.business.opens": "开门", "geo.business.closes": "关门", "geo.business.hours.unavailable": "营业时间信息不可用", "geo.report.hours": "汇报营业时间", "geo.report.correction": "汇报更正", "geo.place.unknown": "未知地点", "geo.report.hours.title": "汇报营业时间", "geo.report.type.title": "选择汇报类型", "geo.report.type.business.hours.title": "汇报营业时间", "geo.report.type.business.hours.description": "提供这个地点的营业时间信息", "geo.report.type.current.status.title": "汇报当前状态", "geo.report.type.current.status.description": "汇报这个地点现在是否营业", "geo.report.type.correction.title": "信息更正", "geo.report.type.correction.description": "汇报现有信息中的错误", "geo.report.business.hours.form.title": "请填写营业时间", "geo.report.current.status.title": "请选择当前状态", "geo.report.correction.form.title": "信息更正", "geo.report.correction.form.description": "请在下面的备注中详细说明需要更正的信息", "geo.report.status.open.now": "现在正在营业", "geo.report.status.closed.now": "现在已关闭", "geo.report.status.temporarily.closed": "暂时关闭", "geo.report.status.permanently.closed": "永久关闭", "geo.weekday.sunday": "周日", "geo.weekday.monday": "周一", "geo.weekday.tuesday": "周二", "geo.weekday.wednesday": "周三", "geo.weekday.thursday": "周四", "geo.weekday.friday": "周五", "geo.weekday.saturday": "周六", "geo.time.open": "开始营业", "geo.time.close": "结束营业", "geo.report.notes.title": "备注说明（可选）", "geo.report.notes.hint": "如有其他说明，请在此输入...", "geo.report.submit": "提交汇报", "geo.report.success.title": "汇报成功", "geo.report.success.message": "感谢您的汇报！我们会尽快处理您提供的信息。", "geo.report.submitted": "汇报已提交，感谢您的反馈！", "geo.report.correction.title": "汇报更正", "geo.report.current.status": "请选择当前营业状态", "geo.report.notes": "备注说明（可选）", "unknown": "未知", "Open": "营业中", "Closed": "已关闭", "Opens": "即将营业", "Closes": "即将关闭", "place.unknown": "未知地点", "place.reviews": "评论", "place.business.hours": "营业时间", "place.hours.unknown": "营业时间未知", "place.address": "地址", "place.checkin": "签到"}
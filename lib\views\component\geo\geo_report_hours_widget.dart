import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

class GeoReportHoursWidget extends StatefulWidget {
  final GeoPlace place;
  final VoidCallback? onReportSubmitted;
  final VoidCallback? onCancel;

  const GeoReportHoursWidget({
    Key? key,
    required this.place,
    this.onReportSubmitted,
    this.onCancel,
  }) : super(key: key);

  @override
  State<GeoReportHoursWidget> createState() => _GeoReportHoursWidgetState();
}

class _GeoReportHoursWidgetState extends State<GeoReportHoursWidget> {
  late ThemePlugin theme;

  // 报告类型
  ReportType selectedReportType = ReportType.businessHours;

  // 营业状态
  BusinessStatusReport? currentStatus;

  // 营业时间控制器
  Map<int, TimeOfDay?> openTimes = {};
  Map<int, TimeOfDay?> closeTimes = {};

  // 特殊说明
  final TextEditingController notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    theme = Get.find<ThemePlugin>();

    // 初始化一周的营业时间
    for (int i = 0; i < 7; i++) {
      openTimes[i] = null;
      closeTimes[i] = null;
    }
  }

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.w)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          _buildHeader(),

          // 内容
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 地点信息
                  _buildPlaceInfo(),

                  SizedBox(height: 20.w),

                  // 报告类型选择
                  _buildReportTypeSelection(),

                  SizedBox(height: 20.w),

                  // 根据选择的类型显示不同内容
                  _buildReportContent(),

                  SizedBox(height: 20.w),

                  // 特殊说明
                  _buildNotesSection(),

                  SizedBox(height: 30.w),

                  // 提交按钮
                  _buildSubmitButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
              color: theme.themeData.colorScheme.outline.withOpacity(0.2)),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CommonText(
            'geo.report.hours.title'.t18,
            theme.textStyleExtension.geoPlaceInfoCardTitle,
          ),
          GestureDetector(
            onTap: widget.onCancel,
            child: Icon(
              Icons.close,
              color: theme.themeData.colorScheme.outline,
              size: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.w),
        border: Border.all(
            color: theme.themeData.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on,
            color: theme.themeData.colorScheme.primary,
            size: 20.w,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonText(
                  widget.place.displayName?.text ?? 'geo.place.unknown'.t18,
                  theme.textStyleExtension.geoPlaceInfoCardTitle,
                ),
                if (widget.place.formattedAddress != null)
                  CommonText(
                    widget.place.formattedAddress!,
                    theme.textStyleExtension.geoPlaceInfoCardMileage,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'geo.report.type.title'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle,
        ),
        SizedBox(height: 12.w),
        ...ReportType.values.map((type) => _buildReportTypeOption(type)),
      ],
    );
  }

  Widget _buildReportTypeOption(ReportType type) {
    final isSelected = selectedReportType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedReportType = type;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 8.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.themeData.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            color: isSelected
                ? theme.themeData.colorScheme.primary
                : theme.themeData.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected
                  ? theme.themeData.colorScheme.primary
                  : theme.themeData.colorScheme.outline,
              size: 20.w,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonText(
                    type.title.t18,
                    theme.textStyleExtension.geoPlaceInfoCardTitle,
                  ),
                  CommonText(
                    type.description.t18,
                    theme.textStyleExtension.geoPlaceInfoCardMileage,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportContent() {
    switch (selectedReportType) {
      case ReportType.businessHours:
        return _buildBusinessHoursForm();
      case ReportType.currentStatus:
        return _buildCurrentStatusForm();
      case ReportType.correction:
        return _buildCorrectionForm();
    }
  }

  Widget _buildBusinessHoursForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'geo.report.business.hours.form.title'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle,
        ),
        SizedBox(height: 12.w),
        ...List.generate(7, (index) => _buildDayHoursRow(index)),
      ],
    );
  }

  Widget _buildDayHoursRow(int dayIndex) {
    final dayNames = [
      'geo.weekday.sunday'.t18,
      'geo.weekday.monday'.t18,
      'geo.weekday.tuesday'.t18,
      'geo.weekday.wednesday'.t18,
      'geo.weekday.thursday'.t18,
      'geo.weekday.friday'.t18,
      'geo.weekday.saturday'.t18,
    ];

    return Container(
      margin: EdgeInsets.only(bottom: 12.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: theme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 60.w,
            child: CommonText(
              dayNames[dayIndex],
              theme.textStyleExtension.geoPlaceInfoCardBusinessTime,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Row(
              children: [
                _buildTimeSelector(
                  'geo.time.open'.t18,
                  openTimes[dayIndex],
                  (time) {
                    setState(() {
                      openTimes[dayIndex] = time;
                    });
                  },
                ),
                SizedBox(width: 12.w),
                CommonText(
                    '-', theme.textStyleExtension.geoPlaceInfoCardBusinessTime),
                SizedBox(width: 12.w),
                _buildTimeSelector(
                  'geo.time.close'.t18,
                  closeTimes[dayIndex],
                  (time) {
                    setState(() {
                      closeTimes[dayIndex] = time;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(
      String hint, TimeOfDay? time, Function(TimeOfDay?) onTimeSelected) {
    return GestureDetector(
      onTap: () async {
        final selectedTime = await showTimePicker(
          context: context,
          initialTime: time ?? TimeOfDay.now(),
        );
        if (selectedTime != null) {
          onTimeSelected(selectedTime);
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
        decoration: BoxDecoration(
          border: Border.all(
              color: theme.themeData.colorScheme.outline.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(6.w),
        ),
        child: CommonText(
          time?.format(context) ?? hint,
          theme.textStyleExtension.geoPlaceInfoCardBusinessTime,
        ),
      ),
    );
  }

  Widget _buildCurrentStatusForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'geo.report.current.status.title'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle,
        ),
        SizedBox(height: 12.w),
        ...BusinessStatusReport.values
            .map((status) => _buildStatusOption(status)),
      ],
    );
  }

  Widget _buildStatusOption(BusinessStatusReport status) {
    final isSelected = currentStatus == status;

    return GestureDetector(
      onTap: () {
        setState(() {
          currentStatus = status;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 8.w),
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.themeData.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(
            color: isSelected
                ? theme.themeData.colorScheme.primary
                : theme.themeData.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: isSelected
                  ? theme.themeData.colorScheme.primary
                  : theme.themeData.colorScheme.outline,
              size: 20.w,
            ),
            SizedBox(width: 12.w),
            CommonText(
              status.title.t18,
              theme.textStyleExtension.geoPlaceInfoCardTitle,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCorrectionForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'geo.report.correction.form.title'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle,
        ),
        SizedBox(height: 12.w),
        CommonText(
          'geo.report.correction.form.description'.t18,
          theme.textStyleExtension.geoPlaceInfoCardMileage,
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(
          'geo.report.notes.title'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle,
        ),
        SizedBox(height: 8.w),
        TextField(
          controller: notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'geo.report.notes.hint'.t18,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.w),
              borderSide: BorderSide(
                  color: theme.themeData.colorScheme.outline.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.w),
              borderSide:
                  BorderSide(color: theme.themeData.colorScheme.primary),
            ),
            contentPadding: EdgeInsets.all(12.w),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _canSubmit() ? _submitReport : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.themeData.colorScheme.primary,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 16.w),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.w),
          ),
        ),
        child: CommonText(
          'geo.report.submit'.t18,
          theme.textStyleExtension.geoPlaceInfoCardTitle
              .copyWith(color: Colors.white),
        ),
      ),
    );
  }

  bool _canSubmit() {
    switch (selectedReportType) {
      case ReportType.businessHours:
        return openTimes.values.any((time) => time != null) ||
            closeTimes.values.any((time) => time != null);
      case ReportType.currentStatus:
        return currentStatus != null;
      case ReportType.correction:
        return true;
    }
  }

  void _submitReport() {
    // 这里可以添加实际的提交逻辑
    // 比如发送到后端API或保存到数据库

    if (widget.onReportSubmitted != null) {
      widget.onReportSubmitted!();
    }

    // 显示成功提示
    Get.snackbar(
      'geo.report.success.title'.t18,
      'geo.report.success.message'.t18,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.withOpacity(0.1),
      colorText: Colors.green,
      duration: Duration(seconds: 3),
    );
  }
}

// 报告类型枚举
enum ReportType {
  businessHours('geo.report.type.business.hours.title',
      'geo.report.type.business.hours.description'),
  currentStatus('geo.report.type.current.status.title',
      'geo.report.type.current.status.description'),
  correction('geo.report.type.correction.title',
      'geo.report.type.correction.description');

  final String title;
  final String description;
  const ReportType(this.title, this.description);
}

// 营业状态报告枚举
enum BusinessStatusReport {
  openNow('geo.report.status.open.now'),
  closedNow('geo.report.status.closed.now'),
  temporarilyClosed('geo.report.status.temporarily.closed'),
  permanentlyClosed('geo.report.status.permanently.closed');

  final String title;
  const BusinessStatusReport(this.title);
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_opening_hours.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoOpeningHours _$GeoOpeningHoursFromJson(Map<String, dynamic> json) =>
    GeoOpeningHours(
      periods: (json['periods'] as List<dynamic>?)
          ?.map((e) => GeoPeriod.fromJson(e as Map<String, dynamic>))
          .toList(),
      weekdayDescriptions: (json['weekdayDescriptions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      secondaryHoursType: $enumDecodeNullable(
          _$SecondaryHoursTypeEnumMap, json['secondaryHoursType']),
      specialDays: (json['specialDays'] as List<dynamic>?)
          ?.map((e) => GeoSpecialDay.fromJson(e as Map<String, dynamic>))
          .toList(),
      nextOpenTime: json['nextOpenTime'] as String?,
      nextCloseTime: json['nextCloseTime'] as String?,
      openNow: json['openNow'] as bool?,
    );

Map<String, dynamic> _$GeoOpeningHoursToJson(GeoOpeningHours instance) =>
    <String, dynamic>{
      'periods': instance.periods,
      'weekdayDescriptions': instance.weekdayDescriptions,
      'secondaryHoursType':
          _$SecondaryHoursTypeEnumMap[instance.secondaryHoursType],
      'specialDays': instance.specialDays,
      'nextOpenTime': instance.nextOpenTime,
      'nextCloseTime': instance.nextCloseTime,
      'openNow': instance.openNow,
    };

const _$SecondaryHoursTypeEnumMap = {
  SecondaryHoursType.unspecified: 'SECONDARY_HOURS_TYPE_UNSPECIFIED',
  SecondaryHoursType.driveThrough: 'DRIVE_THROUGH',
  SecondaryHoursType.happyHour: 'HAPPY_HOUR',
  SecondaryHoursType.delivery: 'DELIVERY',
  SecondaryHoursType.takeout: 'TAKEOUT',
  SecondaryHoursType.kitchen: 'KITCHEN',
  SecondaryHoursType.breakfast: 'BREAKFAST',
  SecondaryHoursType.lunch: 'LUNCH',
  SecondaryHoursType.dinner: 'DINNER',
  SecondaryHoursType.brunch: 'BRUNCH',
  SecondaryHoursType.pickup: 'PICKUP',
  SecondaryHoursType.access: 'ACCESS',
  SecondaryHoursType.seniorHours: 'SENIOR_HOURS',
  SecondaryHoursType.onlineServiceHours: 'ONLINE_SERVICE_HOURS',
};

enum SupportedAreaCode {

  northAmerica('+1'),
  cn('+86'),
  ;

  final String code; // Stored code in database
  const SupportedAreaCode(this.code);

  // Factory constructor to create a TestType object based on the code
  factory SupportedAreaCode.fromCode(String code) {
    return SupportedAreaCode.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

import 'dart:convert';
import 'dart:math';

import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';

const uuid = Uuid();

class CredentialUtil {

  static String generateHashSalt() {
    final random = Random.secure();
    final values = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Url.encode(values);
  }

  static String hashPasswordWithSalt(String password, String salt) {
    var bytes = utf8.encode(password + salt); // Combine password + salt
    var digest = sha256.convert(bytes); // Hash with SHA-256
    return digest.toString();
  }

  static String hashPassword(String password) {
    var bytes = utf8.encode(password + generateHashSalt()); // Combine password + salt
    var digest = sha256.convert(bytes); // Hash with SHA-256
    return digest.toString();
  }
}
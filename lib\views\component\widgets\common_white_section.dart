import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';

class CommonWhiteSection extends StatelessWidget {
  final double width;
  final double height;
  final double top;
  final double borderRadius;

  const CommonWhiteSection({
    super.key,
    required this.width,
    required this.height,
    required this.top,
    this.borderRadius = 85.0,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      width: width.w,
      height: height.h,
      top: top.h,
      child: Container(
        decoration: BoxDecoration(
          color: OneNataClassicColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(borderRadius.r),
          ),
          boxShadow: [
            BoxShadow(
              color: OneNataClassicColors.cadetGray.withOpacity(0.4),
              blurRadius: 12,
            ),
          ],
        ),
      ),
    );
  }
}

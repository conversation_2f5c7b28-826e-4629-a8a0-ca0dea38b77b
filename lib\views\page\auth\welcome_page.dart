import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

class WelcomePage extends StatefulWidget {
  const WelcomePage({super.key});

  @override
  WelcomePageState createState() => WelcomePageState();
}

class WelcomePageState extends State<WelcomePage>{

  late Future<WelcomePageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => WelcomePageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<WelcomePageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Indicator for loading
          return const Center(
            child: LoadingWidget(),
          );
        } else if (snapshot.hasError) {
          // Error handling
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          // Page functions
          final controller = snapshot.data!;
          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.primary,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [

                SizedBox(height: 140.w),
                SizedBox(
                  height: 120.w,
                  child: AuthWidgetBuilder.buildWelcomeAvatar(controller.theme),
                ),
                SizedBox(height: 20.w),
                SizedBox(
                  height: 40.w,
                  child: CommonText(
                    'welcome.page.title'.t18,
                    controller.theme.textStyleExtension.appAvatarLarge,
                  ),
                ),
                SizedBox(height: 50.w),
                SizedBox(
                  height: 60.w,
                  child: CommonButton(
                    text: 'welcome.page.button.start'.t18,
                    onPressed: () {
                      Get.to(SignUpPage());
                      // Get.toNamed("/signUp");
                      // Get.to(TestPage());
                    },
                    backgroundColor: controller.theme.themeData.colorScheme.secondary,
                    textColor: controller.theme.themeData.colorScheme.onPrimary,
                    isOutlined: false,
                    height: 60.w,
                  ),
                ),
                SizedBox(height: 20.w),
                SizedBox(
                  height: 60.w,
                  child: CommonButton(
                    text: 'welcome.page.button.login'.t18,
                    onPressed: () {
                      Get.to(LogInPage());
                    },
                    textColor: controller.theme.themeData.colorScheme.secondary,
                    borderColor: controller.theme.themeData.colorScheme.secondary,
                    isOutlined: true,
                    height: 60.w,
                  ),
                ),
                SizedBox(height: 58.w),
                Expanded(
                  child: SingleChildScrollView(
                      child: Column(
                        children: [
                          CommonImageAsset(
                            path: 'dog_hand.png',
                            width: 442.w,
                            height: 322.w,
                          ),
                        ],
                      )
                    // CommonImageAsset(
                    //   path: 'dog_hand.png',
                    //   width: 442.w,
                    //   height: 322.w,
                    // ),
                  )
                )
                // SingleChildScrollView(
                //   child: Column(
                //     children: [
                //       CommonImageAsset(
                //         path: 'dog_hand.png',
                //         width: 442.w,
                //         height: 322.w,
                //       ),
                //     ],
                //   )
                //   // CommonImageAsset(
                //   //   path: 'dog_hand.png',
                //   //   width: 442.w,
                //   //   height: 322.w,
                //   // ),
                // )
                // CommonImageAsset(
                //   path: 'dog_hand.png',
                //   width: 442.w,
                //   height: 322.w,
                // ),
                // SizedBox.expand(
                //   child: SingleChildScrollView(
                //     child: CommonImageAsset(
                //       path: 'dog_hand.png',
                //       width: 442.w,
                //       height: 322.w,
                //     ),
                //   )
                // )
              ],
            ),
          );
        }
      }
    );
  }
}

class WelcomePageController extends GetxController{

  // Theme plugin
  late ThemePlugin theme;

  Future<WelcomePageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    return this;
  }

}

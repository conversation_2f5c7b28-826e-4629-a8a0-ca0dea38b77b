// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_geo_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetGeoRecord _$PetGeoRecordFromJson(Map<String, dynamic> json) => PetGeoRecord(
      uid: json['uid'] as String?,
      pid: json['pid'] as String,
      deviceId: json['deviceId'] as String?,
      msgId: json['msgId'] as String?,
      location: JsonUtil.geoFirePointFromJson(
          json['location'] as Map<String, dynamic>?),
      altitude: (json['altitude'] as num?)?.toInt(),
      address: json['address'] == null
          ? null
          : GeoPostalAddress.fromJson(json['address'] as Map<String, dynamic>),
      generateDate: (json['generateDate'] as num?)?.toInt(),
      sendDate: (json['sendDate'] as num?)?.toInt(),
      receiveDate: (json['receiveDate'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$PetGeoRecordToJson(PetGeoRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'pid': instance.pid,
      'deviceId': instance.deviceId,
      'msgId': instance.msgId,
      'location': JsonUtil.geoFirePointToJson(instance.location),
      'altitude': instance.altitude,
      'address': instance.address,
      'generateDate': instance.generateDate,
      'sendDate': instance.sendDate,
      'receiveDate': instance.receiveDate,
    };

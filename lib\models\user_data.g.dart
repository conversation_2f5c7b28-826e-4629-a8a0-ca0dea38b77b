// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserData _$UserDataFromJson(Map<String, dynamic> json) => UserData(
      uid: json['uid'] as String,
      avatar: json['avatar'] as String?,
      bio: json['bio'] as String?,
      userType: json['userType'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      location: json['location'] == null
          ? null
          : GeoPostalAddress.fromJson(json['location'] as Map<String, dynamic>),
      followingCount: (json['followingCount'] as num?)?.toInt(),
      followersCount: (json['followersCount'] as num?)?.toInt(),
      friendsCount: (json['friendsCount'] as num?)?.toInt(),
      postsCount: (json['postsCount'] as num?)?.toInt(),
      likedCount: (json['likedCount'] as num?)?.toInt(),
      favoredCount: (json['favoredCount'] as num?)?.toInt(),
      publishVisibility: $enumDecodeNullable(
          _$PublishVisibilityEnumMap, json['publishVisibility']),
      publishVisibleDays: (json['publishVisibleDays'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$UserDataToJson(UserData instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'avatar': instance.avatar,
      'bio': instance.bio,
      'userType': instance.userType,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'location': instance.location,
      'followingCount': instance.followingCount,
      'followersCount': instance.followersCount,
      'friendsCount': instance.friendsCount,
      'postsCount': instance.postsCount,
      'likedCount': instance.likedCount,
      'favoredCount': instance.favoredCount,
      'publishVisibility':
          _$PublishVisibilityEnumMap[instance.publishVisibility],
      'publishVisibleDays': instance.publishVisibleDays,
    };

const _$PublishVisibilityEnumMap = {
  PublishVisibility.public: 'PUB',
  PublishVisibility.private: 'PVT',
  PublishVisibility.friend: 'FRD',
};

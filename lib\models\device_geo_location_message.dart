import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'device_message_header.dart';
import 'geo_location.dart';

part 'device_geo_location_message.g.dart';

@JsonSerializable()
class DeviceGeoLocationMessage extends DeviceMessageHeader {

  @JsonKey(fromJson: JsonUtil.geoFirePointFromJson, toJson: JsonUtil.geoFirePointToJson)
  GeoFirePoint? location;
  int? altitude;

  DeviceGeoLocationMessage({
    super.sid,
    super.pid,
    required super.deviceId,
    super.generateDate,
    super.sendDate,
    super.receiveDate,
    this.location,
    this.altitude,
  });

  factory DeviceGeoLocationMessage.fromJson(Map<String, dynamic> json) => _$DeviceGeoLocationMessageFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$DeviceGeoLocationMessageToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory DeviceGeoLocationMessage.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return DeviceGeoLocationMessage.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory DeviceGeoLocationMessage.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return DeviceGeoLocationMessage.fromJson(jsonData);
  }

  factory DeviceGeoLocationMessage.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return DeviceGeoLocationMessage.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(DeviceGeoLocationMessage? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? sid,
    String? pid,
    required String deviceId,
    int? generateDate,
    int? sendDate,
    int? receiveDate,
    GeoFirePoint? location,
    int? altitude,
  }) {

    return DeviceGeoLocationMessage(
      sid: sid?? uuid.v4(),
      pid: pid,
      deviceId: deviceId,
      generateDate: generateDate,
      sendDate: sendDate,
      receiveDate: receiveDate,
      location: location,
      altitude: altitude,
    );
  }

  static DeviceGeoLocationMessage? copyFrom(DeviceGeoLocationMessage? other) {
    return other == null ? null : DeviceGeoLocationMessage(
      sid: other.sid,
      pid: other.pid,
      deviceId: other.deviceId,
      generateDate: other.generateDate,
      sendDate: other.sendDate,
      receiveDate: other.receiveDate,
      location: other.location,
      altitude: other.altitude,
    );
  }

  static String get collection => 'device_geo_location_message';

  @override
  String toString() {
    return jsonEncode(this);
  }

}

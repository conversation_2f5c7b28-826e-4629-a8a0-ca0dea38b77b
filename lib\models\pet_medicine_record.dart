import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_medicine_record.g.dart';

@JsonSerializable()
class PetMedicineRecord{
  String sid;
  int time;
  String? medicineName;
  List<String>? dose;
  List<String>? usage;
  List<String>? images;
  String? notes;

  PetMedicineRecord({
    required this.sid,
    required this.time,
    this.medicineName,
    this.dose,
    this.usage,
    this.images,
    this.notes,
  });

  factory PetMedicineRecord.fromJson(Map<String, dynamic> json) => _$PetMedicineRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetMedicineRecordToJson(this);

  static create({
    required int time,
    String? medicineName,
    List<String>? dose,
    List<String>? usage,
    List<String>? images,
    String? notes,
  }) {
    return PetMedicineRecord(
      sid: uuid.v4(),
      time: time,
      medicineName: medicineName,
      dose: dose,
      usage: usage,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetMedicineRecord other) {
    return PetMedicineRecord(
      sid: other.sid,
      notes: other.notes,
      time: other.time,
      medicineName: other.medicineName,
      dose: other.dose,
      usage: other.usage,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

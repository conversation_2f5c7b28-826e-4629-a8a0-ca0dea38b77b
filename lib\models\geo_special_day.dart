import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'geo_i.dart';

part 'geo_special_day.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoSpecialDay {

  GeoDate? date;

  GeoSpecialDay({
    this.date
  });

  factory GeoSpecialDay.fromJson(Map<String, dynamic> json) => _$GeoSpecialDayFromJson(json);
  Map<String, dynamic> toJson() => _$GeoSpecialDayToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'date') {
        jsonData[key] = GeoDate.fromApi(value);
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoSpecialDay.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoSpecialDay.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'date') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoDate.fromFirestore(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoSpecialDay.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoSpecialDay.fromJson(jsonData);
  }

  factory GeoSpecialDay.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoSpecialDay.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'date') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoDate.toFirestoreJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoSpecialDay? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({

    GeoDate? date,
  }) {

    return GeoSpecialDay(
      date: date,
    );
  }

  static GeoSpecialDay copyFrom(GeoSpecialDay other) {
    return GeoSpecialDay(
      date: GeoDate.copyFrom(other.date),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'package:json_annotation/json_annotation.dart';

part 'geo_reviews_text.g.dart';

@JsonSerializable()
class GeoReviewsText {
  String? text;
  String? languageCode;

  @override
  String toString() {
    return 'GeoReviewsText{text: $text, languageCode: $languageCode}';
  }

  GeoReviewsText({
    this.text,
    this.languageCode,
  });

  factory GeoReviewsText.fromJson(Map<String, dynamic> json) =>
      _$GeoReviewsTextFromJson(json);
  Map<String, dynamic> toJson() => _$GeoReviewsTextToJson(this);

  static create({String? text, String? languageCode}) {
    return GeoReviewsText(
      text: text,
      languageCode: languageCode,
    );
  }
}

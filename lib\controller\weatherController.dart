import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:get/get.dart';

import '../services/geo_location_service.dart';

class WeatherNotificationController extends GetxController {
  Timer? _timer;
  DateTime? _lastBadWeatherPushTime;
  bool _hasPushedGoodWeatherToday = false;
  String _lastAlertType = '';

  Future<WeatherNotificationController> init() async {
    _startWeatherReminderLoop();
    return this;
  }

  void _startWeatherReminderLoop() {
    _checkAndSendWeatherReminder(); // first time
    _timer = Timer.periodic(Duration(minutes: 30), (_) {
      _checkAndSendWeatherReminder();
    });
  }

  void _checkAndSendWeatherReminder() async {
    final now = DateTime.now();
    final hourMinute = DateFormat('HH:mm').format(now);
    final isWeekend = now.weekday == DateTime.saturday || now.weekday == DateTime.sunday;

    final badWeatherTimePoints = isWeekend ? ['08:00', '14:00'] : ['06:00', '16:00'];
    final goodWeatherTimePoints = isWeekend ? ['08:30', '14:30'] : ['06:30', '16:30'];
    final backupTimePoints = ['12:00', '21:00'];

    final weatherType = await _getWeatherStatus();
    final userHasGoneOut = await _checkIfUserWentOut();

    if (badWeatherTimePoints.contains(hourMinute) && weatherType == 'bad') {
      if (_lastBadWeatherPushTime == null ||
          now.difference(_lastBadWeatherPushTime!).inHours >= 4) {
        final message = _getBadWeatherMessage();
        _sendPushNotification(message['title']!, message['body']!);
        _lastBadWeatherPushTime = now;
      }
    }
    else if (goodWeatherTimePoints.contains(hourMinute) && weatherType == 'good') {
      if (!_hasPushedGoodWeatherToday && !userHasGoneOut) {
        final goodTitles = ['Great weather outside', 'Feels like the perfect day'];
        final goodBodies = [
          'It’s a perfect time to head out and get some movement together.',
          'Fresh air and sunshine are calling. Check nearby spots and enjoy them.'
        ];
        final index = Random().nextInt(2);
        final message = goodTitles[index] + "\n" + goodBodies[index];
        _showInAppReminder(message);
        _hasPushedGoodWeatherToday = true;
      }
    }
    else if (backupTimePoints.contains(hourMinute)) {
      final severeSoon = await _hasSevereWeatherSoon();
      if (severeSoon) {
        _sendPushNotification("天气提醒", "请注意：未来可能有恶劣天气。");
      }
      if(!userHasGoneOut){
        _sendPushNotification("尚未出门", "请注意：今天尚未出门。");
      }
    }
  }

  Future<String> _getWeatherStatus() async {
    const apiKey = '30728a1f6d5e445596f11603253005'; // replace with your key
    final position = await GeoLocationService.getCurrentLocation();
    if (position == null) {
      print("无法获取位置，经纬度为空，返回中性天气");
      return 'neutral';
    }

    final lat = position.latitude;
    final lon = position.longitude;

    final url = Uri.parse(
        'https://api.weatherapi.com/v1/forecast.json?key=$apiKey&q=$lat,$lon&days=1&aqi=yes&alerts=yes'
    );

    final response = await http.get(url);
    if (response.statusCode != 200) return 'neutral';

    final data = json.decode(response.body);
    final hours = data['forecast']['forecastday'][0]['hour'] as List;
    final alerts = data['alerts']['alert'] as List;
    final aqi = data['current']['air_quality']['us-epa-index'];
    final current = data['current'];
    final forecastDay = data['forecast']['forecastday'][0];

    final maxTemp = forecastDay['day']['maxtemp_c'];
    final minTemp = forecastDay['day']['mintemp_c'];

    print("--- 当前天气数据 ---");
    print("位置：$lat, $lon");
    print("当前温度: ${current['temp_c']} °C");
    print("体感温度: ${current['feelslike_c']} °C");
    print("湿度: ${current['humidity']}%");
    print("风速: ${current['wind_kph']} km/h");
    print("空气质量指数 (us-epa-index): $aqi");
    print("天气描述: ${current['condition']['text']}");
    print("最高温度: $maxTemp °C");
    print("最低温度: $minTemp °C");
    print("------------------");

    double rainMax = 0;
    double windTotal = 0;
    int windCount = 0;
    bool extremeTemp = false;

    final nowHour = DateTime.now().hour;
    for (var i = 0; i < 2; i++) {
      final hourData = hours[nowHour + i];
      final rain = double.tryParse(hourData['chance_of_rain'].toString()) ?? 0;
      final feelsLike = hourData['feelslike_c'];
      final wind = hourData['wind_kph'];

      if (feelsLike < 0 || feelsLike > 30) extremeTemp = true;
      rainMax = max(rainMax, rain);
      windTotal += wind;
      windCount++;
    }

    final avgWind = windTotal / windCount;
    final hasSevereAlert = alerts.any((a) {
      final event = (a['event'] as String).toLowerCase();
      if (event.contains('thunder')) _lastAlertType = 'thunder';
      else if (event.contains('rain')) _lastAlertType = 'rain';
      else if (event.contains('snow')) _lastAlertType = 'snow';
      else _lastAlertType = 'other';
      return event.contains("storm") || event.contains("rain") || event.contains("snow") || event.contains("hail");
    });

    if (rainMax >= 60 || extremeTemp || avgWind >= 30 || hasSevereAlert || aqi > 4) return 'bad';
    if (rainMax < 20 && !extremeTemp && avgWind < 20 && alerts.isEmpty) return 'good';
    return 'neutral';
  }

  Map<String, String> _getBadWeatherMessage() {
    switch (_lastAlertType) {
      case 'thunder':
        return {
          'title': 'Thunderstorms in the area',
          'body': 'Thunder and lightning are nearby. It’s best to stay indoors until it clears'
        };
      case 'rain':
        return {
          'title': 'Heavy rain on the way',
          'body': 'Heavy rain may last for a while. Consider rescheduling outdoor time and staying dry'
        };
      case 'snow':
        return {
          'title': 'Heavy snow is falling',
          'body': 'Heavy snow may get heavier. It might not be safe to go outside right now'
        };
      default:
        return {
          'title': 'Outdoor conditions may be unfavorable',
          'body': 'Weather isn’t great right now. Indoor bonding might be a better choice today'
        };
    }
  }

  Future<bool> _checkIfUserWentOut() async {
    // TODO: replace with actual walking/play/GPS logic
    return false;
  }

  Future<bool> _hasSevereWeatherSoon() async {
    const apiKey = '30728a1f6d5e445596f11603253005'; // 你的 key
    const location = 'Toronto';
    final url = Uri.parse(
        'https://api.weatherapi.com/v1/forecast.json?key=$apiKey&q=$location&days=1&alerts=yes'
    );

    final response = await http.get(url);
    if (response.statusCode != 200) return false;

    final data = json.decode(response.body);
    final now = DateTime.now();
    final nextHour = now.hour + 1;

    final forecastHours = data['forecast']['forecastday'][0]['hour'] as List;
    if (nextHour >= forecastHours.length) return false; // 防止越界

    final hourData = forecastHours[nextHour];

    final feelsLike = hourData['feelslike_c'];
    final wind = hourData['wind_kph'];
    final rainChance = double.tryParse(hourData['chance_of_rain'].toString()) ?? 0;

    final alerts = data['alerts']['alert'] as List;
    final hasSevereAlert = alerts.any((a) {
      final event = (a['event'] as String).toLowerCase();
      return event.contains("storm") ||
          event.contains("rain") ||
          event.contains("snow") ||
          event.contains("hail") ||
          event.contains("thunder");
    });

    print("[未来天气检查] 下小时体感温度: $feelsLike°C, 风速: $wind km/h, 降雨概率: $rainChance%, 有警报: $hasSevereAlert");

    return rainChance >= 60 || feelsLike < 0 || feelsLike > 30 || wind >= 30 || hasSevereAlert;
  }


  void _sendPushNotification(String title, String message) {
    print("[推送通知] $title\n$message");
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.TOP,
      duration: Duration(seconds: 8),
      backgroundColor: const Color(0xFFFAFBFF),
      colorText: const Color(0xFF333333),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      onTap: (_) {
        Get.offAllNamed('/root');
      },
    );
  }

  void _showInAppReminder(String message) {
    Get.snackbar(
      "Weather Reminder",
      message,
      snackPosition: SnackPosition.TOP,
      duration: Duration(seconds: 6),
      backgroundColor: const Color(0xFFFAFBFF),
      colorText: const Color(0xFF333333),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
    );
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_time_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentTimeInfo _$AppointmentTimeInfoFromJson(Map<String, dynamic> json) =>
    AppointmentTimeInfo(
      date: json['date'] as String,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      duration: (json['duration'] as num).toInt(),
      timeSlots:
          (json['timeSlots'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$AppointmentTimeInfoToJson(
        AppointmentTimeInfo instance) =>
    <String, dynamic>{
      'date': instance.date,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'duration': instance.duration,
      'timeSlots': instance.timeSlots,
    };

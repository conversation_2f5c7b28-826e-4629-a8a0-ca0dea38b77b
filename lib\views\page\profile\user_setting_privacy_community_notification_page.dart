import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/profile/profile_widget_builder.dart';

import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingCommunityNotificationPage extends StatefulWidget {
  const UserSettingCommunityNotificationPage({super.key});

  @override
  State<UserSettingCommunityNotificationPage> createState() => _UserSettingCommunityNotificationPageState();
}

class _UserSettingCommunityNotificationPageState extends State<UserSettingCommunityNotificationPage> {
  late Future<UserSettingCommunityNotificationPageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => UserSettingCommunityNotificationPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingCommunityNotificationPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
        final controller = snapshot.data!;
        return CommonPage(
          theme: controller.theme,
          backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProfileWidgetBuilder.buildUserSettingTopSection(
                theme: controller.theme,
                onBack: () => Get.back(),
                topic: 'Community Notifications',
              ),
              _buildMainSwitch(controller),
              Obx(() {
                if (!controller.communityNotificationEnabled.value) return const SizedBox.shrink();
                return Column(children: _buildSubSwitches(controller));
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMainSwitch(UserSettingCommunityNotificationPageController controller) {
    return Obx(() => SwitchListTile(
      title: Text("Turn on Community Notifications", style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text(
        "Stop all social notifications at once.\nFor users under age 13, all notifications are automatically turned off and cannot be enabled.",
        style: controller.theme.textStyleExtension.petAvatar,
      ),
      value: controller.communityNotificationEnabled.value,
      onChanged: (val) => controller.communityNotificationEnabled.value = val,
    ));
  }

  List<Widget> _buildSubSwitches(UserSettingCommunityNotificationPageController controller) {
    return [
      _buildSubSwitch(controller, "Mentions", "Get notified when someone mentions you.", controller.mentionsEnabled),
      _buildSubSwitch(controller, "Comments", "Get alerts when someone comments on your post.", controller.commentsEnabled),
      _buildSubSwitch(controller, "Replies", "Know when someone replies to your comment.", controller.repliesEnabled),
      _buildSubSwitch(controller, "Likes", "Get notified when someone likes your content.", controller.likesEnabled),
      _buildSubSwitch(controller, "New Followers", "Find out when someone starts following you.", controller.followersEnabled),
    ];
  }

  Widget _buildSubSwitch(UserSettingCommunityNotificationPageController controller, String title, String subtitle, RxBool toggleValue) {
    return Obx(() => SwitchListTile(
      title: Text(title, style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text(subtitle, style: controller.theme.textStyleExtension.petAvatar),
      value: toggleValue.value,
      onChanged: (val) => toggleValue.value = val,
    ));
  }
}

class UserSettingCommunityNotificationPageController extends GetxController {
  final AuthService _authService = AuthService.instance;

  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  late ThemePlugin theme;

  final RxBool communityNotificationEnabled = false.obs;
  final RxBool mentionsEnabled = false.obs;
  final RxBool commentsEnabled = false.obs;
  final RxBool repliesEnabled = false.obs;
  final RxBool likesEnabled = false.obs;
  final RxBool followersEnabled = false.obs;

  Future<UserSettingCommunityNotificationPageController> init() async {
    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());

    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
      theme,
      userId: _authService.userAccount.value!.sid!,
      avatar: userData?.avatar,
    );

    return this;
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';

import '../../../common/enum/media_type.dart';
import '../../../common/utils/file_util.dart';
import '../../../models/pet.dart';
import '../../../models/pet_daycare_record.dart';
import '../../../models/pet_feeding_record.dart';
import '../../../models/pet_grooming_record.dart';
import '../../../models/pet_medicine_record.dart';
import '../../../models/pet_other_record.dart';
import '../../../models/pet_poop_record.dart';
import '../../../models/pet_social_record.dart';
import '../../../models/pet_vaccine_record.dart';
import '../../../models/pet_vet_record.dart';
import '../../../models/pet_walking_record.dart';
import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/user_service.dart';
import '../../../views/component/widgets/common_text.dart';
import '../../../views/theme/theme_plugin.dart';
import '../../component/progress/loading_widget.dart';
import '../../component/record/record_widget_builder.dart';
import '../../component/widgets/common_button3.dart';
import '../../component/widgets/common_page.dart';
import '../../component/widgets/common_text_field.dart';
import '../../component/widgets/common_text_field3.dart';
import '../page_interceptor.dart';
import 'record_page.dart';

class AddRecordPage extends StatefulWidget {
  final RecordPageController parentController;
  final String type;
  final String label;
  final Pet selectedPet;
  final dynamic recordToEdit;

  const AddRecordPage({
    super.key,
    required this.parentController,
    required this.type,
    required this.label,
    required this.selectedPet,
    this.recordToEdit
  });

  @override
  State<AddRecordPage> createState() => AddRecordPageState();
}

class AddRecordPageState extends State<AddRecordPage> {
  late Future<AddRecordPageController> _controller;
  late TabController _tabController;
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => AddRecordPageController(
      selectedPet: widget.selectedPet,
      type: widget.type,
      recordToEdit: widget.recordToEdit,
    ).init());
  }


  @override
  Widget build(BuildContext context) {
    return FutureBuilder<AddRecordPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingWidget());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          final controller = snapshot.data!;
          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(bottom: 24.w),
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTopSection(controller),
                        _buildRecordForm(controller),
                        RecordWidgetBuilder.buildButton2(
                          controller.theme,
                          controller.buttonController,
                          100,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

          );
        }
      },
    );
  }

  Widget _buildRecordForm(AddRecordPageController controller) {
    print(widget.type);
    switch (widget.type) {
      case 'feed':
        return _buildFeedForm(controller);
      case 'poop':
        return _buildPoopForm(controller);
      case 'walk':
        return _buildWalkingForm(controller);
      case 'social':
        return _buildSocialForm(controller);
      case 'med':
        return _buildMedForm(controller);
      case 'vaccine':
        return _buildVaccineForm(controller);
      case 'grooming':
        return _buildGroomingForm(controller);
      case 'daycare':
        return _buildDaycareForm(controller);
      case 'vet':
        return _buildVetForm(controller);
      case 'other':
        return _buildOtherForm(controller);
      default:
        return const SizedBox.shrink(); // 如果没匹配上，显示空白
    }
  }

  Widget _buildFeedForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.feedingStartDate,
          selectedTime: controller.feedingStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Food Type'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.feedBrandInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Dry food',
            'Wet food',
            'Freeze-dried raw',
            'Frozen & raw',
            'Homemade',
          ],
          targetInputController: controller.feedBrandInputController,
          singleSelect: true,
          onTap: () {
            controller.isSnack.value = false;
            controller.feedAmountInputController.textEditingController.value?.clear(); // ✅ 清空
            controller.feedAmountHint.value = 'Add quantity';
          },
        ),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Treat',
            'Meat Jerky',
            'Bully stick',
            'Bites',
          ],
          targetInputController: controller.feedBrandInputController,
          singleSelect: true,
          onTap: () {
            controller.isSnack.value = true;
            controller.feedAmountInputController.textEditingController.value?.clear(); // ✅ 清空
            controller.feedAmountHint.value = 'Numbers only'; // ✅ 更新 hint
          },
        ),
        Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: controller.isSnack.value
                ? [
              RecordWidgetBuilder.buildText(controller.theme, 24, 'Frequency(optional)'),
              RecordWidgetBuilder.buildSettingInputBox(
                  controller.theme, controller.feedAmountInputController, 24),
            ]
                : [
              RecordWidgetBuilder.buildText(controller.theme, 24, 'Leftover(optional)'),
              RecordWidgetBuilder.buildSettingInputBox(
                  controller.theme, controller.feedAmountInputController, 24),
              RecordWidgetBuilder.buildQuickInsertButtons(
                theme: controller.theme,
                options: ['10%', '25%', '50%', '75%', '100%'],
                targetInputController: controller.feedAmountInputController,
                singleSelect: true,
              ),
            ],
          );
        }),
        // RecordWidgetBuilder.buildText(controller.theme, 24, 'Leftover(optional)'),
        // RecordWidgetBuilder.buildSettingInputBox(
        //     controller.theme, controller.feedAmountInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //    theme: controller.theme,
        //    options: [
        //      '10%',
        //      '25%',
        //      '50%',
        //      '75%',
        //      '100%',
        //    ],
        //    targetInputController: controller.feedAmountInputController,
        //    singleSelect: true,
        //  ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.feedNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Finished eating',
            'Didn\'t finish eating',
            'Liked this food',
            'Didn\'t like this food',
            'Ate very quickly',
            'Ate very slowly'
          ],
          targetInputController: controller.feedNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildImagePickerGrid(
          theme: controller.theme,
          images: controller.selectedImages,
          onAddImage: () => controller.pickImage('feed'),
          onRemoveImage: (index) => controller.removeImage(index),
        ),
      ],
    );
  }

  Widget _buildPoopForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.poopStartDate,
          selectedTime: controller.poopStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Poop type'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.poopFormInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Normal',
            'Hard and dry',
            'Soft',
            'Runny',
            'With mucus',
            'Poorly formed',
            'Unknown'
          ],
          targetInputController: controller.poopFormInputController,
          singleSelect: true,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Color'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.poopColorInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Brown',
            'Black',
            'Green',
            'Yellow',
            'Gray-white',
            'Bloody'
          ],
          targetInputController: controller.poopColorInputController,
          singleSelect: true,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.poopNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Abnormal smell',
            'Difficulty in defecation',
            'Bloody stool',
            'Multiple attempts needed',
            'Defecated indoors',
            'Accompanied by vomiting'
          ],
          targetInputController: controller.poopNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildWalkingForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Start time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.walkingStartDate,
          selectedTime: controller.walkingStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'End time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.walkingEndDate,
          selectedTime: controller.walkingEndTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Location'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.walkingLocationInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Distance'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.walkingDistanceInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            '0.5km',
            '1km',
            '1.5km',
            '2km',
            '2.5km',
            '3km',
            '3.5km',
            '4km'
          ],
          targetInputController: controller.walkingDistanceInputController,
          singleSelect: true,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.walkingNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Sunny',
            'Rainy',
            'Slippery road',
            'Encountered other dogs',
            'Excited mood',
            'Tired',
            'Stopped midway'
          ],
          targetInputController: controller.walkingNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildSocialForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Start time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.socialStartDate,
          selectedTime: controller.socialStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'End time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.socialEndDate,
          selectedTime: controller.socialEndTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Social pal'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.socialObjectInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Dog',
            'Cat',
            'Human',
          ],
          targetInputController: controller.socialObjectInputController,
        ),
        // RecordWidgetBuilder.buildText(controller.theme, 24, 'Behavior'),
        // RecordWidgetBuilder.buildSettingInputBox(
        //     controller.theme, controller.socialBehaviorInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Active',
        //     'Shy',
        //     'Growling',
        //     'Friendly',
        //     'Nervous',
        //     'Sniffing',
        //     'Noisy',
        //     'Avoiding'
        //   ],
        //   targetInputController: controller.socialBehaviorInputController,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Location'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.socialLocationInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.socialNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Frequent interaction',
            'Owner accompanied',
            'Multiple greetings',
            'Short interaction duration'
          ],
          targetInputController: controller.socialNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildMedForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.medStartDate,
          selectedTime: controller.medStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Medicine name'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.medBrandInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Dosage(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.medAmountInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     '50mg',
        //     '100mg',
        //     '1 tablet',
        //     '5ml'
        //   ],
        //   targetInputController: controller.medAmountInputController,
        //   singleSelect: true,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Method'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.medUsageInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Oral',
        //     'Topical',
        //     'Injection',
        //     'Ear drops',
        //     'Eye drops',
        //     'Apply'
        //   ],
        //   targetInputController: controller.medUsageInputController,
        //   singleSelect: true,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.medNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'No abnormal reaction',
            'Mild vomiting',
            'Lethargy',
            'Decreased appetite',
            'Skin allergy',
            'Suspected insufficient dosage',
            'Drowsiness'
          ],
          targetInputController: controller.medNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildVaccineForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.vaccineStartDate,
          selectedTime: controller.vaccineStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Vaccine name'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vaccineNameInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Clinic location'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vaccineClinicInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Expired date'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDatePicker(
          theme: controller.theme,
          selectedDate: controller.vaccineExpiredDate,
          onChanged: () {
          },
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Batch number(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vaccineCodeInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vaccineNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'No adverse reaction',
            'Mild fever',
            'Local swelling',
            'Poor spirit',
            'Doctor advised observation'
          ],
          targetInputController: controller.vaccineNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildGroomingForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.groomingStartDate,
          selectedTime: controller.groomingStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Service'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.groomingTypeInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Bath',
            'Haircut',
            'Nail trimming',
            'Ear cleaning',
            'Styling',
            'Teeth cleaning',
            'Shampoo care'
          ],
          targetInputController: controller.groomingTypeInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Store name'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.groomingLocationInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.groomingNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Nervous',
            'Highly cooperative',
            'Good feedback',
            'Skin issues found',
            'Recommend changing grooming products'
          ],
          targetInputController: controller.groomingNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildDaycareForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Start time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.daycareStartDate,
          selectedTime: controller.daycareStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'End time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.daycareEndDate,
          selectedTime: controller.daycareEndTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Location'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.daycareLocationInputController, 24),
        // RecordWidgetBuilder.buildText(controller.theme, 24, 'Event type'),
        // RecordWidgetBuilder.buildSettingInputBox(
        //     controller.theme, controller.daycareTypeInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Interactive games',
        //     'Free play',
        //     'Group training',
        //     'Nap time',
        //     'Meal arrangement',
        //     'Class teaching'
        //   ],
        //   targetInputController: controller.daycareTypeInputController,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Notes(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.daycareNoteInputController, 24),
        RecordWidgetBuilder.buildQuickInsertButtons(
          theme: controller.theme,
          options: [
            'Emotionally stable',
            'Energetic',
            'Slightly anxious',
            'Initiative in interaction',
            'Good service'
          ],
          targetInputController: controller.daycareNoteInputController,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildVetForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.vetStartDate,
          selectedTime: controller.vetStartTime,
          onChanged: controller.checkFormValid,
        ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Vet location'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vetNameInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Diagnoses'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vetDescrInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Indigestion',
        //     'Ear infection',
        //     'Skin allergy',
        //     'Cough'
        //   ],
        //   targetInputController: controller.vetDescrInputController,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Treatment suggestions(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.vetTreatInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Continue medication',
        //     'Recommend follow-up visit',
        //     'Diet adjustment',
        //     'Need rest and observation'
        //   ],
        //   targetInputController: controller.vetTreatInputController,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildOtherForm(AddRecordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Category'),
        SizedBox(height: 24.w),
        _buildCategoryRow(),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Time'),
        SizedBox(height: 24.w),
        RecordWidgetBuilder.buildDateTimePicker(
          theme: controller.theme,
          selectedDate: controller.otherStartDate,
          selectedTime: controller.otherStartTime,
          onChanged: controller.checkFormValid,
        ),
        // RecordWidgetBuilder.buildText(controller.theme, 24, 'Title'),
        // RecordWidgetBuilder.buildSettingInputBox(
        //     controller.theme, controller.otherTitleInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Description'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.otherContentInputController, 24),
        // RecordWidgetBuilder.buildQuickInsertButtons(
        //   theme: controller.theme,
        //   options: [
        //     'Unexpected situation',
        //     'Behavioral change',
        //     'External stimulation',
        //     'First-time experience',
        //     'Strong reaction',
        //     'Needs follow-up'
        //   ],
        //   targetInputController: controller.otherContentInputController,
        // ),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Location(optional)'),
        RecordWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.otherLocationInputController, 24),
        RecordWidgetBuilder.buildText(controller.theme, 24, 'Photo(optional)'),
      ],
    );
  }

  Widget _buildTopSection(AddRecordPageController controller) {
    return Container(
      height: 64.w,
      padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 12.w),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(Icons.arrow_back,
                color: controller.theme.themeData.colorScheme.onSurface),
          ),
          SizedBox(width: 8.w),
          Container(
            width: 1.w,
            height: 22.w,
            color: controller.theme.themeData.colorScheme.tertiary,
          ),
          SizedBox(width: 8.w),
          CommonText(
            "Add Record",
            controller.theme.textStyleExtension.userProfileSettingBody,
          ),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildCategoryRow() {
    final iconMapping = {
      'feed': Icons.restaurant,
      'poop': Icons.cruelty_free,
      'walk': Icons.directions_walk,
      'social': Icons.group,
      'med': Icons.medication,
      'vaccine': Icons.vaccines,
      'grooming': Icons.cut,
      'daycare': Icons.home_work,
      'vet': Icons.local_hospital,
      'other': Icons.more_horiz,
    };

    final colorMapping = {
      'feed': const Color.fromRGBO(242, 211, 164, 1),
      'poop': const Color.fromRGBO(122, 41, 23, 1),
      'walk': const Color.fromRGBO(130, 196, 60, 1),
      'social': const Color.fromRGBO(56, 151, 240, 1),
      'med': const Color.fromRGBO(56, 151, 240, 1),
      'vaccine': const Color.fromRGBO(235, 195, 81, 1),
      'grooming': const Color.fromRGBO(161, 38, 255, 1),
      'daycare': const Color.fromRGBO(83, 225, 183, 1),
      'vet': const Color.fromRGBO(255, 197, 66, 1),
      'other': const Color.fromRGBO(209, 109, 106, 1),
    };

    final icon = iconMapping[widget.type];
    final iconColor = colorMapping[widget.type];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w), // ⭐️ 加padding控制左右距离
      child: Row(
        children: [
          Container(
            width: 28.w,
            height: 28.w,
            decoration: BoxDecoration(
              color: iconColor?.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 16.w,
              color: iconColor,
            ),
          ),
          SizedBox(width: 8.w),
          CommonText(
            widget.label,
            TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}

class AddRecordPageController extends GetxController {
  late Pet selectedPet;
  late String type;
  late String label;
  late final ThemePlugin theme;
  late CommonButton3Controller buttonController;
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  final PetService _petService = PetService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;
  List<Pet>? petList;
  final Map<String, Future<Widget?>> petAvatarFutures = {};
  late RecordPageController parentController;
  bool get isEditMode => recordToEdit != null;

  //late RxString selectedPet = 'Unknown'.obs;
  //late List<PetProfileVO> pets;
  //final Rx<PetProfileVO> currentPetData = PetProfileVO.empty().obs;

  // final TextEditingController timeController = TextEditingController();
  // final TextEditingController brandController = TextEditingController();
  // final TextEditingController amountController = TextEditingController();

  late Rx<DateTime> feedingStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> feedingStartTime = TimeOfDay.now().obs;
  late RxString feedNoteHint = 'What do you want to write'.obs;
  final TextEditingController feedNoteInputBox = TextEditingController();
  late CommonTextField3Controller feedNoteInputController;
  late RxString feedBrandHint = 'Add food type'.obs;
  final TextEditingController feedBrandInputBox = TextEditingController();
  late CommonTextField3Controller feedBrandInputController;
  late RxString feedAmountHint = 'Add quantity'.obs;
  final TextEditingController feedAmountInputBox = TextEditingController();
  late CommonTextField3Controller feedAmountInputController;
  RxBool isSnack = false.obs;

  late Rx<DateTime> poopStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> poopStartTime = TimeOfDay.now().obs;
  late RxString poopNoteHint = 'What do you want to write'.obs;
  final TextEditingController poopNoteInputBox = TextEditingController();
  late CommonTextField3Controller poopNoteInputController;
  late RxString poopFormHint = 'Form'.obs;
  final TextEditingController poopFormInputBox = TextEditingController();
  late CommonTextField3Controller poopFormInputController;
  late RxString poopColorHint = 'Color'.obs;
  final TextEditingController poopColorInputBox = TextEditingController();
  late CommonTextField3Controller poopColorInputController;

  late RxString walkingNoteHint = 'What do you want to write'.obs;
  final TextEditingController walkingNoteInputBox = TextEditingController();
  late CommonTextField3Controller walkingNoteInputController;
  late RxString walkingLocationHint = 'Location'.obs;
  final TextEditingController walkingLocationInputBox = TextEditingController();
  late CommonTextField3Controller walkingLocationInputController;
  late RxString walkingDistanceHint = 'Distance (km)'.obs;
  final TextEditingController walkingDistanceInputBox = TextEditingController();
  late CommonTextField3Controller walkingDistanceInputController;
  late Rx<DateTime> walkingStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> walkingStartTime = TimeOfDay.now().obs;
  late Rx<DateTime> walkingEndDate = DateTime.now().obs;
  late Rx<TimeOfDay> walkingEndTime = TimeOfDay.now().obs;

  late RxString socialNoteHint = 'What do you want to write'.obs;
  final TextEditingController socialNoteInputBox = TextEditingController();
  late CommonTextField3Controller socialNoteInputController;
  late RxString socialLocationHint = 'Location'.obs;
  final TextEditingController socialLocationInputBox = TextEditingController();
  late CommonTextField3Controller socialLocationInputController;
  late RxString socialObjectHint = 'Object type'.obs;
  final TextEditingController socialObjectInputBox = TextEditingController();
  late CommonTextField3Controller socialObjectInputController;
  late RxString socialBehaviorHint = 'Behavior'.obs;
  final TextEditingController socialBehaviorInputBox = TextEditingController();
  late CommonTextField3Controller socialBehaviorInputController;
  late Rx<DateTime> socialStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> socialStartTime = TimeOfDay.now().obs;
  late Rx<DateTime> socialEndDate = DateTime.now().obs;
  late Rx<TimeOfDay> socialEndTime = TimeOfDay.now().obs;

  late RxString medNoteHint = 'What do you want to write'.obs;
  final TextEditingController medNoteInputBox = TextEditingController();
  late CommonTextField3Controller medNoteInputController;
  late RxString medBrandHint = 'Medicine name'.obs;
  final TextEditingController medBrandInputBox = TextEditingController();
  late CommonTextField3Controller medBrandInputController;
  late RxString medAmountHint = 'Amount (mg)'.obs;
  final TextEditingController medAmountInputBox = TextEditingController();
  late CommonTextField3Controller medUsageInputController;
  late RxString medUsageHint = 'Usage'.obs;
  final TextEditingController medUsageInputBox = TextEditingController();
  late CommonTextField3Controller medAmountInputController;
  late Rx<DateTime> medStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> medStartTime = TimeOfDay.now().obs;

  late RxString vaccineNameHint = 'Vaccine name'.obs;
  final TextEditingController vaccineNameInputBox = TextEditingController();
  late CommonTextField3Controller vaccineNameInputController;
  late RxString vaccineClinicHint = 'Clinic name'.obs;
  final TextEditingController vaccineClinicInputBox = TextEditingController();
  late CommonTextField3Controller vaccineClinicInputController;
  late RxString vaccineCodeHint = 'Vaccine code'.obs;
  final TextEditingController vaccineCodeInputBox = TextEditingController();
  late CommonTextField3Controller vaccineCodeInputController;
  late RxString vaccineNoteHint = 'What do you want to write'.obs;
  final TextEditingController vaccineNoteInputBox = TextEditingController();
  late CommonTextField3Controller vaccineNoteInputController;
  late Rx<DateTime> vaccineStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> vaccineStartTime = TimeOfDay.now().obs;
  Rx<DateTime> vaccineExpiredDate = DateTime.now().obs;


  late RxString groomingTypeHint = 'Grooming type'.obs;
  final TextEditingController groomingTypeInputBox = TextEditingController();
  late CommonTextField3Controller groomingTypeInputController;
  late RxString groomingLocationHint = 'Grooming location'.obs;
  final TextEditingController groomingLocationInputBox = TextEditingController();
  late CommonTextField3Controller groomingLocationInputController;
  late RxString groomingNoteHint = 'What do you want to write'.obs;
  final TextEditingController groomingNoteInputBox = TextEditingController();
  late CommonTextField3Controller groomingNoteInputController;
  late Rx<DateTime> groomingStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> groomingStartTime = TimeOfDay.now().obs;

  late RxString daycareTypeHint = 'Daycare type'.obs;
  final TextEditingController daycareTypeInputBox = TextEditingController();
  late CommonTextField3Controller daycareTypeInputController;
  late RxString daycareLocationHint = 'Daycare location'.obs;
  final TextEditingController daycareLocationInputBox = TextEditingController();
  late CommonTextField3Controller daycareLocationInputController;
  late RxString daycareNoteHint = 'What do you want to write'.obs;
  final TextEditingController daycareNoteInputBox = TextEditingController();
  late CommonTextField3Controller daycareNoteInputController;
  late Rx<DateTime> daycareStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> daycareStartTime = TimeOfDay.now().obs;
  late Rx<DateTime> daycareEndDate = DateTime.now().obs;
  late Rx<TimeOfDay> daycareEndTime = TimeOfDay.now().obs;

  late RxString vetNameHint = 'Vet name'.obs;
  final TextEditingController vetNameInputBox = TextEditingController();
  late CommonTextField3Controller vetNameInputController;
  late RxString vetDescrHint = 'Diagnostic description'.obs;
  final TextEditingController vetDescrInputBox = TextEditingController();
  late CommonTextField3Controller vetDescrInputController;
  late RxString vetTreatHint = 'Treatment suggestion'.obs;
  final TextEditingController vetTreatInputBox = TextEditingController();
  late CommonTextField3Controller vetTreatInputController;
  late Rx<DateTime> vetStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> vetStartTime = TimeOfDay.now().obs;

  late RxString otherTitleHint = 'Title name'.obs;
  final TextEditingController otherTitleInputBox = TextEditingController();
  late CommonTextField3Controller otherTitleInputController;
  late RxString otherLocationHint = 'Location'.obs;
  final TextEditingController otherLocationInputBox = TextEditingController();
  late CommonTextField3Controller otherLocationInputController;
  late RxString otherContentHint = 'Content'.obs;
  final TextEditingController otherContentInputBox = TextEditingController();
  late CommonTextField3Controller otherContentInputController;
  late Rx<DateTime> otherStartDate = DateTime.now().obs;
  late Rx<TimeOfDay> otherStartTime = TimeOfDay.now().obs;

  AddRecordPageController({
    required this.selectedPet,
    required this.type,
    this.recordToEdit,
  });


  Future<AddRecordPageController> init() async {
    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    userAccount.value = _authService.userAccount.value;
    //userAccount.value.fid;
    //userData=await _userService.getUserDataById(userId:userAccount.value?.sid);
    UserData? userData = _authService.userData.value;
    userAccount.value = _authService.userAccount.value;
    userData = _authService.userData.value;
    petList = await _petService.getOwnedPets(userAccount.value!.sid!);


    feedNoteInputController =
        RecordWidgetBuilder.buildFeedNoteTextFieldController(
            theme, feedNoteInputBox.obs, feedNoteHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    feedBrandInputController =
        RecordWidgetBuilder.buildFeedBrandTextFieldController(
            theme, feedBrandInputBox.obs, feedBrandHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    feedAmountInputController =
        RecordWidgetBuilder.buildFeedAmountInputTextFieldController(
            theme, feedAmountInputBox.obs, feedAmountHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    poopNoteInputController =
        RecordWidgetBuilder.buildPoopNoteTextFieldController(
            theme, poopNoteInputBox.obs, poopNoteHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    poopFormInputController =
        RecordWidgetBuilder.buildPoopFormInputTextFieldController(
            theme, poopFormInputBox.obs, poopFormHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    poopColorInputController =
        RecordWidgetBuilder.buildPoopColorInputTextFieldController(
            theme, poopColorInputBox.obs, poopColorHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    walkingNoteInputController =
        RecordWidgetBuilder.buildWalkingNoteTextFieldController(
            theme, walkingNoteInputBox.obs, walkingNoteHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    walkingLocationInputController =
        RecordWidgetBuilder.buildWalkingLocationInputTextFieldController(
            theme, walkingLocationInputBox.obs, walkingLocationHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    walkingDistanceInputController =
        RecordWidgetBuilder.buildWalkingDistanceInputTextFieldController(
            theme, walkingDistanceInputBox.obs, walkingDistanceHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    socialNoteInputController =
        RecordWidgetBuilder.buildSocialNoteTextFieldController(
            theme, socialNoteInputBox.obs, socialNoteHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    socialLocationInputController =
        RecordWidgetBuilder.buildSocialLocationInputTextFieldController(
            theme, socialLocationInputBox.obs, socialLocationHint
            //onCleared: onPhoneNumberCleared.obs,
            // onChanged: isInputPhoneNumberValid.obs,
            );
    socialObjectInputController =
        RecordWidgetBuilder.buildSocialObjectTextFieldController(
            theme, socialObjectInputBox.obs, socialObjectHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    socialBehaviorInputController =
        RecordWidgetBuilder.buildSocialBehaviorInputTextFieldController(
            theme, socialBehaviorInputBox.obs, socialBehaviorHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    medNoteInputController =
        RecordWidgetBuilder.buildMedNoteTextFieldController(
            theme, medNoteInputBox.obs, medNoteHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    medBrandInputController =
        RecordWidgetBuilder.buildMedBrandTextFieldController(
            theme, medBrandInputBox.obs, medBrandHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    medAmountInputController =
        RecordWidgetBuilder.buildMedAmountInputTextFieldController(
            theme, medAmountInputBox.obs, medAmountHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    medUsageInputController =
        RecordWidgetBuilder.buildMedUsageInputTextFieldController(
            theme, medUsageInputBox.obs, medUsageHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vaccineNameInputController =
        RecordWidgetBuilder.buildVaccineNameInputTextFieldController(
            theme, vaccineNameInputBox.obs, vaccineNameHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vaccineClinicInputController =
        RecordWidgetBuilder.buildVaccineClinicInputTextFieldController(
            theme, vaccineClinicInputBox.obs, vaccineClinicHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vaccineCodeInputController =
        RecordWidgetBuilder.buildVaccineCodeInputTextFieldController(
            theme, vaccineCodeInputBox.obs, vaccineCodeHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vaccineNoteInputController =
        RecordWidgetBuilder.buildVaccineNoteInputTextFieldController(
            theme, vaccineNoteInputBox.obs, vaccineNoteHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    groomingTypeInputController =
        RecordWidgetBuilder.buildGroomingTypeInputTextFieldController(
            theme, groomingTypeInputBox.obs, groomingTypeHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    groomingLocationInputController =
        RecordWidgetBuilder.buildGroomingLocationInputTextFieldController(
            theme, groomingLocationInputBox.obs, groomingLocationHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    groomingNoteInputController =
        RecordWidgetBuilder.buildGroomingNoteInputTextFieldController(
            theme, groomingNoteInputBox.obs, groomingNoteHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    daycareTypeInputController =
        RecordWidgetBuilder.buildDaycareTypeInputTextFieldController(
            theme, daycareTypeInputBox.obs, daycareTypeHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    daycareLocationInputController =
        RecordWidgetBuilder.buildDaycareLocationInputTextFieldController(
            theme, daycareLocationInputBox.obs, daycareLocationHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    daycareNoteInputController =
        RecordWidgetBuilder.buildDaycareNoteInputTextFieldController(
            theme, daycareNoteInputBox.obs, daycareNoteHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vetNameInputController =
        RecordWidgetBuilder.buildVetNameInputTextFieldController(
            theme, vetNameInputBox.obs, vetNameHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vetDescrInputController =
        RecordWidgetBuilder.buildVetDescrInputTextFieldController(
            theme, vetDescrInputBox.obs, vetDescrHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    vetTreatInputController =
        RecordWidgetBuilder.buildVetTreatInputTextFieldController(
            theme, vetTreatInputBox.obs, vetTreatHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    otherTitleInputController =
        RecordWidgetBuilder.buildOtherTitleInputTextFieldController(
            theme, otherTitleInputBox.obs, otherTitleHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    otherLocationInputController =
        RecordWidgetBuilder.buildOtherLocationInputTextFieldController(
            theme, otherLocationInputBox.obs, otherLocationHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );
    otherContentInputController =
        RecordWidgetBuilder.buildOtherContentInputTextFieldController(
            theme, otherContentInputBox.obs, otherContentHint
          //onCleared: onPhoneNumberCleared.obs,
          // onChanged: isInputPhoneNumberValid.obs,
        );

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'save.changes'.t18.obs,
      onPressed: saveChanges.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    if (type == 'feed') {
      feedBrandInputController.textEditingController.value!.addListener(checkFormValid);
      feedAmountInputController.textEditingController.value!.addListener(checkFormValid);
      feedNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'poop') {
      poopFormInputController.textEditingController.value!.addListener(checkFormValid);
      poopColorInputController.textEditingController.value!.addListener(checkFormValid);
      poopNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'walk') {
      walkingDistanceInputController.textEditingController.value!.addListener(checkFormValid);
      walkingLocationInputController.textEditingController.value!.addListener(checkFormValid);
      walkingNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'social') {
      socialObjectInputController.textEditingController.value!.addListener(checkFormValid);
      socialBehaviorInputController.textEditingController.value!.addListener(checkFormValid);
      socialLocationInputController.textEditingController.value!.addListener(checkFormValid);
      socialNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'med') {
      medBrandInputController.textEditingController.value!.addListener(checkFormValid);
      medAmountInputController.textEditingController.value!.addListener(checkFormValid);
      medUsageInputController.textEditingController.value!.addListener(checkFormValid);
      medNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'vaccine') {
      vaccineNameInputController.textEditingController.value!.addListener(checkFormValid);
      vaccineClinicInputController.textEditingController.value!.addListener(checkFormValid);
      vaccineCodeInputController.textEditingController.value!.addListener(checkFormValid);
      vaccineNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'grooming') {
      groomingTypeInputController.textEditingController.value!.addListener(checkFormValid);
      groomingLocationInputController.textEditingController.value!.addListener(checkFormValid);
      groomingNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'daycare') {
      daycareTypeInputController.textEditingController.value!.addListener(checkFormValid);
      daycareLocationInputController.textEditingController.value!.addListener(checkFormValid);
      daycareNoteInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'vet') {
      vetNameInputController.textEditingController.value!.addListener(checkFormValid);
      vetDescrInputController.textEditingController.value!.addListener(checkFormValid);
      vetTreatInputController.textEditingController.value!.addListener(checkFormValid);
    }
    else if (type == 'other') {
      otherTitleInputController.textEditingController.value!.addListener(checkFormValid);
      otherLocationInputController.textEditingController.value!.addListener(checkFormValid);
      otherContentInputController.textEditingController.value!.addListener(checkFormValid);
    }

    checkFormValid();

    if (type == 'feed' && recordToEdit != null && recordToEdit is PetFeedingRecord) {
      final r = recordToEdit as PetFeedingRecord;
      feedingStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.feedTime!);
      feedingStartTime.value = TimeOfDay.fromDateTime(feedingStartDate.value);
      feedBrandInputController.textEditingController.value?.text = r.foodTypes?.join(', ') ?? '';
      feedAmountInputController.textEditingController.value?.text = r.amount ?? '';
      feedNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      isSnack.value = r.isSnack ?? false;
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'poop' && recordToEdit != null && recordToEdit is PetPoopRecord) {
      final r = recordToEdit as PetPoopRecord;
      poopStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.poopTime!);
      poopStartTime.value = TimeOfDay.fromDateTime(poopStartDate.value);
      poopFormInputController.textEditingController.value?.text =
          r.shapeTags?.join(', ') ?? '';
      poopColorInputController.textEditingController.value?.text =
          r.colorTags?.join(', ') ?? '';
      poopNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'walk' && recordToEdit != null && recordToEdit is PetWalkingRecord) {
      final r = recordToEdit as PetWalkingRecord;
      walkingStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.startTime!);
      walkingStartTime.value = TimeOfDay.fromDateTime(walkingStartDate.value);
      if (r.endTime != null) {
        walkingEndDate.value = DateTime.fromMillisecondsSinceEpoch(r.endTime!);
        walkingEndTime.value = TimeOfDay.fromDateTime(walkingEndDate.value);
      }
      walkingDistanceInputController.textEditingController.value?.text =
          r.distanceTags?.join(', ') ?? '';
      walkingLocationInputController.textEditingController.value?.text = r.location ?? '';
      walkingNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'social' && recordToEdit != null && recordToEdit is PetSocialRecord) {
      final r = recordToEdit as PetSocialRecord;
      socialStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.startTime!);
      socialStartTime.value = TimeOfDay.fromDateTime(socialStartDate.value);
      if (r.endTime != null) {
        socialEndDate.value = DateTime.fromMillisecondsSinceEpoch(r.endTime!);
        socialEndTime.value = TimeOfDay.fromDateTime(socialEndDate.value);
      }
      socialObjectInputController.textEditingController.value?.text =
          r.targetTypeTags?.join(', ') ?? '';
      socialBehaviorInputController.textEditingController.value?.text =
          r.behaviorTags?.join(', ') ?? '';
      socialLocationInputController.textEditingController.value?.text = r.location ?? '';
      socialNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'grooming' && recordToEdit != null && recordToEdit is PetGroomingRecord) {
      final r = recordToEdit as PetGroomingRecord;
      groomingStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.time!);
      groomingStartTime.value = TimeOfDay.fromDateTime(groomingStartDate.value);
      groomingTypeInputController.textEditingController.value?.text =
          r.serviceType?.join(', ') ?? '';
      groomingLocationInputController.textEditingController.value?.text = r.location ?? '';
      groomingNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'med' && recordToEdit != null && recordToEdit is PetMedicineRecord) {
      final r = recordToEdit as PetMedicineRecord;
      medStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.time);
      medStartTime.value = TimeOfDay.fromDateTime(medStartDate.value);
      medBrandInputController.textEditingController.value?.text = r.medicineName ?? '';
      medAmountInputController.textEditingController.value?.text =
          r.dose?.join(', ') ?? '';
      medUsageInputController.textEditingController.value?.text =
          r.usage?.join(', ') ?? '';
      medNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'vet' && recordToEdit != null && recordToEdit is PetVetRecord) {
      final r = recordToEdit as PetVetRecord;
      if (r.time != null) {
        vetStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.time!);
        vetStartTime.value = TimeOfDay.fromDateTime(vetStartDate.value);
      }
      vetNameInputController.textEditingController.value?.text = r.location ?? '';
      vetDescrInputController.textEditingController.value?.text = r.description ?? '';
      vetTreatInputController.textEditingController.value?.text = r.treat ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'daycare' && recordToEdit != null && recordToEdit is PetDaycareRecord) {
      final r = recordToEdit as PetDaycareRecord;
      daycareStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.startTime!);
      daycareStartTime.value = TimeOfDay.fromDateTime(daycareStartDate.value);
      if (r.endTime != null) {
        daycareEndDate.value = DateTime.fromMillisecondsSinceEpoch(r.endTime!);
        daycareEndTime.value = TimeOfDay.fromDateTime(daycareEndDate.value);
      }
      daycareLocationInputController.textEditingController.value?.text = r.location ?? '';
      daycareNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.images ?? [];
      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'other' && recordToEdit != null && recordToEdit is PetOtherRecord) {
      final r = recordToEdit as PetOtherRecord;

      otherStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.time!);
      otherStartTime.value = TimeOfDay.fromDateTime(otherStartDate.value);

      otherLocationInputController.textEditingController.value?.text = r.location ?? '';
      otherContentInputController.textEditingController.value?.text = r.content ?? '';
      otherTitleInputController.textEditingController.value?.text = r.title ?? '';
      uploadedImageNames.value = r.images ?? [];

      selectedImages.value = r.images?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    if (type == 'vaccine' && recordToEdit != null && recordToEdit is PetVaccineRecord) {
      final r = recordToEdit as PetVaccineRecord;

      vaccineStartDate.value = DateTime.fromMillisecondsSinceEpoch(r.time);
      vaccineStartTime.value = TimeOfDay.fromDateTime(vaccineStartDate.value);

      vaccineNameInputController.textEditingController.value?.text = r.vaccineName ?? '';
      vaccineClinicInputController.textEditingController.value?.text = r.location ?? '';
      if (r.expiredDate != null) {
        vaccineExpiredDate.value = DateTime.fromMillisecondsSinceEpoch(r.expiredDate!);
      }
      vaccineCodeInputController.textEditingController.value?.text = r.batchNumber ?? '';
      vaccineNoteInputController.textEditingController.value?.text = r.notes ?? '';
      uploadedImageNames.value = r.photos ?? [];

      selectedImages.value = r.photos?.map((name) {
        final path = FileUtil.buildUserResourcePath(MediaType.image, selectedPet.sid!);
        return XFile('$path/$name');
      }).toList() ?? [];
    }

    return this;
  }

  Future<void> saveChanges() async {
    try {
      final uid = userAccount.value!.sid!;
      final pid = selectedPet.sid!;
      // if (type == 'feed') {
      //   final startTime = DateTime(
      //     feedingStartDate.value.year,
      //     feedingStartDate.value.month,
      //     feedingStartDate.value.day,
      //     feedingStartTime.value.hour,
      //     feedingStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetFeedingRecord.create(
      //     feedTime: startTime,
      //     foodTypes: feedBrandInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     amount: feedAmountInputController.textEditingController.value!.text,
      //     notes: feedNoteInputController.textEditingController.value!.text,
      //     images: uploadedImageNames.toList(),
      //   );
      //   await _petService.recordPetFeeding(selectedPet.sid!, record);
      // }
      if (type == 'feed') {
        final date = feedingStartDate.value;
        final time = feedingStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);

        final amount = feedAmountInputController.textEditingController.value?.text ?? '';
        final note = feedNoteInputController.textEditingController.value?.text ?? '';
        final foodTypeString = feedBrandInputController.textEditingController.value?.text ?? '';
        final foodTypes = foodTypeString.split(',').map((e) => e.trim()).toList();
        final isSnackMode = isSnack.value;

        if (isEditMode && recordToEdit is PetFeedingRecord) {
          final r = recordToEdit as PetFeedingRecord;
          r.isSnack = isSnackMode;
          r.feedTime = fullDateTime.millisecondsSinceEpoch;
          r.foodTypes = foodTypes;
          r.amount = amount;
          r.notes = note;
          r.images = uploadedImageNames.toList();

          await _petService.updatePetFeedingRecord(selectedPet.sid!, r);
        } else {
          final record = PetFeedingRecord.create(
            feedTime: fullDateTime.millisecondsSinceEpoch,
            foodTypes: foodTypes,
            amount: amount,
            notes: note,
            isSnack: isSnackMode,
            images: uploadedImageNames.toList(),
          );

          await _petService.recordPetFeeding(selectedPet.sid!, record);
        }

        //Get.back(result: true);
      }
      if (type == 'poop') {
        final date = poopStartDate.value;
        final time = poopStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final shapeStr = poopFormInputController.textEditingController.value?.text ?? '';
        final colorStr = poopColorInputController.textEditingController.value?.text ?? '';
        final note = poopNoteInputController.textEditingController.value?.text ?? '';
        final shapeTags = shapeStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        final colorTags = colorStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();

        if (isEditMode && recordToEdit is PetPoopRecord) {
          final r = recordToEdit as PetPoopRecord;
          r.poopTime = fullDateTime.millisecondsSinceEpoch;
          r.shapeTags = shapeTags;
          r.colorTags = colorTags;
          r.notes = note;
          r.images = uploadedImageNames.toList();

          await _petService.updatePetPoopRecord(selectedPet.sid!, r);
        } else {
          final record = PetPoopRecord.create(
            poopTime: fullDateTime.millisecondsSinceEpoch,
            shapeTags: shapeTags,
            colorTags: colorTags,
            notes: note,
            images: uploadedImageNames.toList(),
          );

          await _petService.recordPetPoop(selectedPet.sid!, record);
        }

        Get.back(result: true);
      }
      if (type == 'walk') {
        final startDate = walkingStartDate.value;
        final startTime = walkingStartTime.value;
        final endDate = walkingEndDate.value;
        final endTime = walkingEndTime.value;

        final fullStart = DateTime(startDate.year, startDate.month, startDate.day, startTime.hour, startTime.minute);
        final fullEnd = DateTime(endDate.year, endDate.month, endDate.day, endTime.hour, endTime.minute);

        final distanceStr = walkingDistanceInputController.textEditingController.value?.text ?? '';
        final location = walkingLocationInputController.textEditingController.value?.text ?? '';
        final note = walkingNoteInputController.textEditingController.value?.text ?? '';
        final distanceTags = distanceStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        if (isEditMode && recordToEdit is PetWalkingRecord) {
          final r = recordToEdit as PetWalkingRecord;
          r.startTime = fullStart.millisecondsSinceEpoch;
          r.endTime = fullEnd.millisecondsSinceEpoch;
          r.distanceTags = distanceTags;
          r.location = location;
          r.notes = note;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetWalkingRecord(selectedPet.sid!, r);
        } else {
          final record = PetWalkingRecord.create(
            startTime: fullStart.millisecondsSinceEpoch,
            endTime: fullEnd.millisecondsSinceEpoch,
            distanceTags: distanceTags,
            location: location,
            notes: note,
            images: uploadedImageNames.toList(),
          );
          await _petService.recordPetWalking(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      if (type == 'social') {
        final start = socialStartDate.value;
        final startTime = socialStartTime.value;
        final end = socialEndDate.value;
        final endTime = socialEndTime.value;
        final fullStart = DateTime(start.year, start.month, start.day, startTime.hour, startTime.minute);
        final fullEnd = DateTime(end.year, end.month, end.day, endTime.hour, endTime.minute);
        final objectStr = socialObjectInputController.textEditingController.value?.text ?? '';
        final behaviorStr = socialBehaviorInputController.textEditingController.value?.text ?? '';
        final location = socialLocationInputController.textEditingController.value?.text ?? '';
        final note = socialNoteInputController.textEditingController.value?.text ?? '';
        final objectTags = objectStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        final behaviorTags = behaviorStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        if (isEditMode && recordToEdit is PetSocialRecord) {
          final r = recordToEdit as PetSocialRecord;
          r.startTime = fullStart.millisecondsSinceEpoch;
          r.endTime = fullEnd.millisecondsSinceEpoch;
          r.targetTypeTags = objectTags;
          r.behaviorTags = behaviorTags;
          r.location = location;
          r.notes = note;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetSocialRecord(selectedPet.sid!, r);
        } else {
          final record = PetSocialRecord.create(
            startTime: fullStart.millisecondsSinceEpoch,
            endTime: fullEnd.millisecondsSinceEpoch,
            targetTypeTags: objectTags,
            behaviorTags: behaviorTags,
            location: location,
            notes: note,
            images: uploadedImageNames.toList(),
          );
          await _petService.recordPetSocial(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      if (type == 'grooming') {
        final date = groomingStartDate.value;
        final time = groomingStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final serviceStr = groomingTypeInputController.textEditingController.value?.text ?? '';
        final location = groomingLocationInputController.textEditingController.value?.text ?? '';
        final note = groomingNoteInputController.textEditingController.value?.text ?? '';
        final serviceTags = serviceStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        if (isEditMode && recordToEdit is PetGroomingRecord) {
          final r = recordToEdit as PetGroomingRecord;
          r.time = fullDateTime.millisecondsSinceEpoch;
          r.serviceType = serviceTags;
          r.location = location;
          r.notes = note;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetGroomingRecord(selectedPet.sid!, r);
        } else {
          final record = PetGroomingRecord.create(
            time: fullDateTime.millisecondsSinceEpoch,
            serviceType: serviceTags,
            location: location,
            notes: note,
            images: uploadedImageNames.toList(),
          );
          await _petService.recordPetGrooming(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      if (type == 'med') {
        final date = medStartDate.value;
        final time = medStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final name = medBrandInputController.textEditingController.value?.text ?? '';
        final doseStr = medAmountInputController.textEditingController.value?.text ?? '';
        final usageStr = medUsageInputController.textEditingController.value?.text ?? '';
        final note = medNoteInputController.textEditingController.value?.text ?? '';
        final doseTags = doseStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        final usageTags = usageStr.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
        if (isEditMode && recordToEdit is PetMedicineRecord) {
          final r = recordToEdit as PetMedicineRecord;
          r.time = fullDateTime.millisecondsSinceEpoch;
          r.medicineName = name;
          r.dose = doseTags;
          r.usage = usageTags;
          r.notes = note;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetMedicineRecord(selectedPet.sid!, r);
        } else {
          final record = PetMedicineRecord.create(
            time: fullDateTime.millisecondsSinceEpoch,
            medicineName: name,
            dose: doseTags,
            usage: usageTags,
            notes: note,
            images: uploadedImageNames.toList(),
          );

          await _petService.recordPetMedicine(selectedPet.sid!, record);
        }

        Get.back(result: true);
      }
      if (type == 'vet') {
        final date = vetStartDate.value;
        final time = vetStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final location = vetNameInputController.textEditingController.value?.text ?? '';
        final description = vetDescrInputController.textEditingController.value?.text ?? '';
        final treat = vetTreatInputController.textEditingController.value?.text ?? '';
        if (isEditMode && recordToEdit is PetVetRecord) {
          final r = recordToEdit as PetVetRecord;
          r.time = fullDateTime.millisecondsSinceEpoch;
          r.location = location;
          r.description = description;
          r.treat = treat;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetVetRecord(selectedPet.sid!, r);
        } else {
          final record = PetVetRecord.create(
            time: fullDateTime.millisecondsSinceEpoch,
            location: location,
            description: description,
            treat: treat,
            images: uploadedImageNames.toList(),
          );
          await _petService.recordPetVet(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      if (type == 'daycare') {
        final start = daycareStartDate.value;
        final startTime = daycareStartTime.value;
        final end = daycareEndDate.value;
        final endTime = daycareEndTime.value;

        final fullStart = DateTime(start.year, start.month, start.day, startTime.hour, startTime.minute);
        final fullEnd = DateTime(end.year, end.month, end.day, endTime.hour, endTime.minute);

        final location = daycareLocationInputController.textEditingController.value?.text ?? '';
        final note = daycareNoteInputController.textEditingController.value?.text ?? '';

        if (isEditMode && recordToEdit is PetDaycareRecord) {
          final r = recordToEdit as PetDaycareRecord;
          r.startTime = fullStart.millisecondsSinceEpoch;
          r.endTime = fullEnd.millisecondsSinceEpoch;
          r.location = location;
          r.notes = note;
          r.images = uploadedImageNames.toList();

          await _petService.updatePetDaycareRecord(selectedPet.sid!, r);
        } else {
          final record = PetDaycareRecord.create(
            uid: uid,
            pid: selectedPet.sid!,
            startTime: fullStart.millisecondsSinceEpoch,
            endTime: fullEnd.millisecondsSinceEpoch,
            location: location,
            notes: note,
            images: uploadedImageNames.toList(),
          );

          await _petService.recordPetDaycare(selectedPet.sid!, record);
        }

        Get.back(result: true);
      }
      if (type == 'vaccine') {
        final date = vaccineStartDate.value;
        final time = vaccineStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final name = vaccineNameInputController.textEditingController.value?.text ?? '';
        final location = vaccineClinicInputController.textEditingController.value?.text ?? '';
        final batch = vaccineCodeInputController.textEditingController.value?.text ?? '';
        final note = vaccineNoteInputController.textEditingController.value?.text ?? '';
        final expiredMillis = vaccineExpiredDate.value.millisecondsSinceEpoch;


        if (isEditMode && recordToEdit is PetVaccineRecord) {
          final r = recordToEdit as PetVaccineRecord;
          r.time = fullDateTime.millisecondsSinceEpoch;
          r.vaccineName = name;
          r.location = location;
          r.expiredDate = expiredMillis;
          r.batchNumber = batch;
          r.notes = note;
          r.photos = uploadedImageNames.toList();
          await _petService.updatePetVaccineRecord(selectedPet.sid!, r);
        } else {
          final record = PetVaccineRecord.create(
            time: fullDateTime.millisecondsSinceEpoch,
            vaccineName: name,
            location: location,
            expiredDate: expiredMillis,
            batchNumber: batch,
            notes: note,
            photos: uploadedImageNames.toList(),
          );
          await _petService.recordPetVaccine(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      if (type == 'other') {
        final date = otherStartDate.value;
        final time = otherStartTime.value;
        final fullDateTime = DateTime(date.year, date.month, date.day, time.hour, time.minute);
        final location = otherLocationInputController.textEditingController.value?.text ?? '';
        final content = otherContentInputController.textEditingController.value?.text ?? '';
        final title = otherTitleInputController.textEditingController.value?.text ?? '';
        if (isEditMode && recordToEdit is PetOtherRecord) {
          final r = recordToEdit as PetOtherRecord;
          r.time = fullDateTime.millisecondsSinceEpoch;
          r.location = location;
          r.content = content;
          r.title = title;
          r.images = uploadedImageNames.toList();
          await _petService.updatePetOtherRecord(selectedPet.sid!, r);
        } else {
          final record = PetOtherRecord.create(
            time: fullDateTime.millisecondsSinceEpoch,
            location: location,
            content: content,
            title: title,
            images: uploadedImageNames.toList(),
          );
          await _petService.recordPetOther(selectedPet.sid!, record);
        }
        Get.back(result: true);
      }
      // else if (type == 'poop') {
      //   final startTime = DateTime(
      //     poopStartDate.value.year,
      //     poopStartDate.value.month,
      //     poopStartDate.value.day,
      //     poopStartTime.value.hour,
      //     poopStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetPoopRecord.create(
      //     poopTime: startTime,
      //     shapeTags: poopFormInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     colorTags: poopColorInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     notes: poopNoteInputController.textEditingController.value!.text,
      //     images: uploadedImageNames.toList(),
      //   );
      //   //await _petService.recordPetPoop(pid,record);
      //   await _petService.recordPetPoop(selectedPet.sid!, record);
      // }
      // else if (type == 'walk') {
      //   final startTime = DateTime(
      //     walkingStartDate.value.year,
      //     walkingStartDate.value.month,
      //     walkingStartDate.value.day,
      //     walkingStartTime.value.hour,
      //     walkingStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final endTime = DateTime(
      //     walkingEndDate.value.year,
      //     walkingEndDate.value.month,
      //     walkingEndDate.value.day,
      //     walkingEndTime.value.hour,
      //     walkingEndTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetWalkingRecord.create(
      //     startTime: startTime,
      //     endTime: endTime,
      //     distanceTags: walkingDistanceInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     location: walkingLocationInputController.textEditingController.value!.text,
      //     notes: walkingNoteInputController.textEditingController.value!.text,
      //     images: [],
      //   );
      //   //await _petService.recordPetWalking(pid,record);
      //   await _petService.recordPetWalking(selectedPet.sid!, record);
      // }
      // else if (type == 'social') {
      //   final startTime = DateTime(
      //     socialStartDate.value.year,
      //     socialStartDate.value.month,
      //     socialStartDate.value.day,
      //     socialStartTime.value.hour,
      //     socialStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final endTime = DateTime(
      //     socialEndDate.value.year,
      //     socialEndDate.value.month,
      //     socialEndDate.value.day,
      //     socialEndTime.value.hour,
      //     socialEndTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetSocialRecord.create(
      //     startTime: startTime,
      //     endTime: endTime,
      //     location: socialLocationInputController.textEditingController.value!.text,
      //     targetTypeTags: socialObjectInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     behaviorTags: socialBehaviorInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     notes: socialNoteInputController.textEditingController.value!.text,
      //     images: [],
      //   );
      //   //await _petService.recordPetSocial(pid,record);
      //   await _petService.recordPetSocial(selectedPet.sid!, record);
      // }
      // else if (type == 'grooming') {
      //   final startTime = DateTime(
      //     groomingStartDate.value.year,
      //     groomingStartDate.value.month,
      //     groomingStartDate.value.day,
      //     groomingStartTime.value.hour,
      //     groomingStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetGroomingRecord.create(
      //     time: startTime,
      //     serviceType: groomingTypeInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     location: groomingLocationInputController.textEditingController.value!.text,
      //     notes: groomingNoteInputController.textEditingController.value!.text,
      //     images: [],
      //   );
      //   await _petService.recordPetGrooming(selectedPet.sid!, record);
      //   //await _petService.recordPetGrooming(pid,record);
      // }
      // else if (type == 'med') {
      //   final startTime = DateTime(
      //     medStartDate.value.year,
      //     medStartDate.value.month,
      //     medStartDate.value.day,
      //     medStartTime.value.hour,
      //     medStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetMedicineRecord.create(
      //     time: startTime,
      //     medicineName: medBrandInputController.textEditingController.value!.text,
      //     dose: medAmountInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     usage: medUsageInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     notes: medNoteInputController.textEditingController.value!.text,
      //     images: [],
      //   );
      //   //await _petService.recordPetMedicine(pid,record);
      //   await _petService.recordPetMedicine(selectedPet.sid!, record);
      // }
      // else if (type == 'vet') {
      //   final startTime = DateTime(
      //     vetStartDate.value.year,
      //     vetStartDate.value.month,
      //     vetStartDate.value.day,
      //     vetStartTime.value.hour,
      //     vetStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //
      //   final record = PetVetRecord.create(
      //     time: startTime,
      //     location: vetNameInputController.textEditingController.value!.text,
      //     description: vetDescrInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList()
      //         .join(', '),
      //     treat: vetTreatInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList()
      //         .join(', '),
      //     notes: '', // 没单独 note输入框
      //     images: [],
      //   );
      //   await _petService.recordPetVet(selectedPet.sid!, record);
      //   //await _petService.recordPetVet(pid,record);
      // }
      // else if (type == 'daycare') {
      //   final startTime = DateTime(
      //     daycareStartDate.value.year,
      //     daycareStartDate.value.month,
      //     daycareStartDate.value.day,
      //     daycareStartTime.value.hour,
      //     daycareStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final endTime = DateTime(
      //     daycareEndDate.value.year,
      //     daycareEndDate.value.month,
      //     daycareEndDate.value.day,
      //     daycareEndTime.value.hour,
      //     daycareEndTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetDaycareRecord.create(
      //     uid: uid,
      //     pid: pid,
      //     startTime: startTime,
      //     endTime: endTime,
      //     location: daycareLocationInputController.textEditingController.value!.text,
      //     content: daycareTypeInputController.textEditingController.value!.text
      //         .split(', ')
      //         .where((e) => e.trim().isNotEmpty)
      //         .toList(),
      //     notes: daycareNoteInputController.textEditingController.value!.text,
      //     images: [],
      //   );
      //   await _petService.recordPetDaycare(selectedPet.sid!, record);
      //   //await _petService.recordPetDaycare(pid,record);
      // }
      // else if (type == 'vaccine') {
      //   final startTime = DateTime(
      //     vaccineStartDate.value.year,
      //     vaccineStartDate.value.month,
      //     vaccineStartDate.value.day,
      //     vaccineStartTime.value.hour,
      //     vaccineStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetVaccineRecord.create(
      //     time: startTime,
      //     vaccineName: vaccineNameInputController.textEditingController.value!.text,
      //     location: vaccineClinicInputController.textEditingController.value!.text,
      //     batchNumber: vaccineCodeInputController.textEditingController.value!.text,
      //     photos: [],
      //     notes: vaccineNoteInputController.textEditingController.value!.text,
      //   );
      //   await _petService.recordPetVaccine(selectedPet.sid!, record);
      //   //await _petService.recordPetVaccine(pid,record);
      // }
      // else if (type == 'other') {
      //   final startTime = DateTime(
      //     otherStartDate.value.year,
      //     otherStartDate.value.month,
      //     otherStartDate.value.day,
      //     otherStartTime.value.hour,
      //     otherStartTime.value.minute,
      //   ).millisecondsSinceEpoch;
      //   final record = PetOtherRecord.create(
      //     time: startTime,
      //     location: otherLocationInputController.textEditingController.value!.text,
      //     title: otherTitleInputController.textEditingController.value!.text,
      //     content: otherContentInputController.textEditingController.value!.text,
      //     images: [],
      //     //notes: '',
      //   );
      //   await _petService.recordPetOther(selectedPet.sid!, record);
      //   //await _petService.recordPetOther(pid,record);
      // }
      //parentController.refreshAllActivities();
      Get.back(result: true);
      Get.snackbar("Saved", "Record saved successfully");
    } catch (e) {
      Get.snackbar("Error", "Failed to save record: $e");
    }
  }

  void checkFormValid() {
    if (type == 'feed') {
      final hasFeedTime = feedingStartDate.value != null && feedingStartTime.value != null;
      final hasFoodTypes = feedBrandInputController.textEditingController.value!.text.trim().isNotEmpty;
      //final hasAmount = feedAmountInputController.textEditingController.value!.text.trim().isNotEmpty;
      final amountText = feedAmountInputController.textEditingController.value!.text.trim();
      bool isAmountValid = true;

      if (isSnack.value && amountText.isNotEmpty) {
        isAmountValid = double.tryParse(amountText) != null;
      }
      buttonController.isEnabled.value = hasFeedTime && hasFoodTypes && isAmountValid;
    }
    else if (type == 'poop') {
      final hasPoopTime = poopStartDate.value != null && poopStartTime.value != null;
      final hasForm = poopFormInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasColor = poopColorInputController.textEditingController.value!.text.trim().isNotEmpty;

      buttonController.isEnabled.value = hasPoopTime && hasForm && hasColor;
    }
    else if (type == 'walk') {
      final hasStart = walkingStartDate.value != null && walkingStartTime.value != null;
      final hasEnd = walkingEndDate.value != null && walkingEndTime.value != null;
      final hasDistance = walkingDistanceInputController.textEditingController.value!.text.trim().isNotEmpty;
      final isEndAfterStart = _isEndTimeValid(walkingStartDate.value, walkingStartTime.value, walkingEndDate.value, walkingEndTime.value);

      buttonController.isEnabled.value = hasStart && hasEnd && hasDistance && isEndAfterStart;
    }
    else if (type == 'social') {
      final hasStart = socialStartDate.value != null && socialStartTime.value != null;
      final hasEnd = socialEndDate.value != null && socialEndTime.value != null;
      final hasTarget = socialObjectInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasBehavior = socialBehaviorInputController.textEditingController.value!.text.trim().isNotEmpty;
      final isEndAfterStart = _isEndTimeValid(socialStartDate.value, socialStartTime.value, socialEndDate.value, socialEndTime.value);

      buttonController.isEnabled.value = hasStart && hasEnd && hasTarget && hasBehavior && isEndAfterStart;
    }
    else if (type == 'med') {
      final hasTime = medStartDate.value != null && medStartTime.value != null;
      final hasBrand = medBrandInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasDose = medAmountInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasUsage = medUsageInputController.textEditingController.value!.text.trim().isNotEmpty;

      buttonController.isEnabled.value = hasTime && hasBrand && hasDose && hasUsage;
    }
    else if (type == 'vaccine') {
      final hasTime = vaccineStartDate.value != null && vaccineStartTime.value != null;
      final hasName = vaccineNameInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasClinic = vaccineClinicInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasExpired = vaccineExpiredDate.value !=null;
      buttonController.isEnabled.value = hasTime && hasName && hasClinic && hasExpired;
    }
    else if (type == 'grooming') {
      final hasTime = groomingStartDate.value != null && groomingStartTime.value != null;
      final hasType = groomingTypeInputController.textEditingController.value!.text.trim().isNotEmpty;

      buttonController.isEnabled.value = hasTime && hasType;
    }
    else if (type == 'daycare') {
      final hasStart = daycareStartDate.value != null && daycareStartTime.value != null;
      final hasEnd = daycareEndDate.value != null && daycareEndTime.value != null;
      //final hasType = daycareTypeInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasLocation = daycareLocationInputController.textEditingController.value!.text.trim().isNotEmpty;
      final isEndAfterStart = _isEndTimeValid(daycareStartDate.value, daycareStartTime.value, daycareEndDate.value, daycareEndTime.value);

      buttonController.isEnabled.value = hasStart && hasEnd && hasLocation && isEndAfterStart;
    }
    else if (type == 'vet') {
      final hasTime = vetStartDate.value != null && vetStartTime.value != null;
      final hasName = vetNameInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasDescr = vetDescrInputController.textEditingController.value!.text.trim().isNotEmpty;

      buttonController.isEnabled.value = hasTime && hasName && hasDescr;
    }
    else if (type == 'other') {
      final hasTime = otherStartDate.value != null && otherStartTime.value != null;
      final hasTitle = otherTitleInputController.textEditingController.value!.text.trim().isNotEmpty;
      final hasContent = otherContentInputController.textEditingController.value!.text.trim().isNotEmpty;

      buttonController.isEnabled.value = hasTime && hasTitle && hasContent;
    }
    else {
      buttonController.isEnabled.value = false;
    }
  }

  bool _isEndTimeValid(DateTime startDate, TimeOfDay startTime, DateTime endDate, TimeOfDay endTime) {
    final start = DateTime(startDate.year, startDate.month, startDate.day, startTime.hour, startTime.minute);
    final end = DateTime(endDate.year, endDate.month, endDate.day, endTime.hour, endTime.minute);
    return end.isAfter(start) || end.isAtSameMomentAs(start);
  }

  final RxList<XFile> selectedImages = <XFile>[].obs;      // UI显示用
  final RxList<String> uploadedImageNames = <String>[].obs;

  final dynamic recordToEdit;
  // void pickImage() async {
  //   final picker = ImagePicker();
  //   final XFile? image = await picker.pickImage(source: ImageSource.gallery);
  //   if (image != null) selectedImages.add(image);
  // }

  Future<void> pickImage(String recordType) async {
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      String? imageName = await _petService.uploadRecordImage(
        uid: uid,
        pid: selectedPet.sid!,
        recordType: recordType,
        image: image,
      );

      if (imageName != null) {
        uploadedImageNames.add(imageName);
        selectedImages.add(image);
      }
    }
  }


  void removeImage(int index) {
    selectedImages.removeAt(index);
    uploadedImageNames.removeAt(index);
  }

}

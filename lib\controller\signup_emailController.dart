import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class SignUpEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController reenterPasswordController =
  TextEditingController();

  final RxBool isEmailEntered = false.obs;
  final RxBool isPasswordEntered = false.obs;
  final RxBool isReenterPasswordEntered = false.obs;
  final RxBool enableButton = false.obs;
  final RxBool isEmailValid = false.obs;
  final RxBool isPasswordValid = false.obs;
  final RxBool isReenterPasswordValid = false.obs;
  final RxString buttonText = 'Send verification code'.obs;
  final RxBool isPasswordVisible = false.obs;
  final RxBool isReenterPasswordVisible = false.obs;
  final String validCode = '123';

  @override
  void onInit() {
    super.onInit();
    resetState();
  }
  @override
  void dispose() {
    resetState();
    super.dispose();
  }
  void resetState() {
    emailController.clear();
    passwordController.clear();
    reenterPasswordController.clear();
    isEmailEntered.value = false;
    isPasswordEntered.value = false;
    isReenterPasswordEntered.value = false;
    enableButton.value = false;
    isEmailValid.value = false;
    isPasswordValid.value = false;
    isReenterPasswordValid.value = false;
    isPasswordVisible.value = false;
    isReenterPasswordVisible.value = false;
    buttonText.value = 'Send verification code';
  }

  bool validateEmail(String email) {
    return RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        .hasMatch(email);
  }

  bool validatePassword(String password) {
    return password.length >= 8 &&
        RegExp(r"[0-9]").hasMatch(password) &&
        RegExp(r"[A-Z]").hasMatch(password) &&
        RegExp(r"[#\$!%]").hasMatch(password);
  }

  void updateButtonState() {
    enableButton.value = isEmailEntered.value &&
        isPasswordEntered.value &&
        isReenterPasswordEntered.value &&
        isEmailValid.value &&
        isPasswordValid.value &&
        isReenterPasswordValid.value;
  }
}

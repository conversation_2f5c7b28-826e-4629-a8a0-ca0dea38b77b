// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_comment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Comment _$CommentFromJson(Map<String, dynamic> json) => Comment(
      uid: json['uid'] as String,
      author: json['author'] as String,
      postId: json['postId'] as String,
      commentId: json['commentId'] as String?,
      visibility: $enumDecode(_$PublishVisibilityEnumMap, json['visibility']),
      content: json['content'] as String,
      location: JsonUtil.geoFirePointFromJson(
          json['location'] as Map<String, dynamic>?),
      altitude: (json['altitude'] as num?)?.toDouble(),
      country: json['country'] as String?,
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$CommentToJson(Comment instance) => <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'uid': instance.uid,
      'author': instance.author,
      'postId': instance.postId,
      'commentId': instance.commentId,
      'visibility': _$PublishVisibilityEnumMap[instance.visibility]!,
      'content': instance.content,
      'location': JsonUtil.geoFirePointToJson(instance.location),
      'altitude': instance.altitude,
      'country': instance.country,
    };

const _$PublishVisibilityEnumMap = {
  PublishVisibility.public: 'PUB',
  PublishVisibility.private: 'PVT',
  PublishVisibility.friend: 'FRD',
};

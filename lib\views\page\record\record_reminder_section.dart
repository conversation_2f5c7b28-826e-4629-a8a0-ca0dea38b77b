import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../views/component/widgets/common_text.dart';
import '../../../views/component/widgets/common_record_widget.dart';
import '../../../views/theme/theme_plugin.dart';
import '../../../views/theme/colors/color_extension.dart';
import 'record_page.dart';

class RecordReminderSection extends StatefulWidget {
  final RecordPageController controller;

  const RecordReminderSection({
    super.key,
    required this.controller,
  });

  @override
  State<RecordReminderSection> createState() => _RecordReminderSectionState();
}

class _RecordReminderSectionState extends State<RecordReminderSection> {
  final List<Map<String, dynamic>> allTypes = [
    {'label': 'All', 'value': 'all', 'icon': Icons.grid_view},
    {'label': 'Feed', 'value': 'feed', 'icon': Icons.restaurant},
    {'label': 'Poop', 'value': 'poop', 'icon': Icons.cruelty_free},
    {'label': 'Walking', 'value': 'walk', 'icon': Icons.directions_walk},
    {'label': 'Social', 'value': 'social', 'icon': Icons.group},
    {'label': 'Medicine', 'value': 'med', 'icon': Icons.medication},
    {'label': 'Vaccine', 'value': 'vaccine', 'icon': Icons.vaccines},
    {'label': 'Grooming', 'value': 'grooming', 'icon': Icons.cut},
    {'label': 'Day care', 'value': 'daycare', 'icon': Icons.home_work},
    {'label': 'Vet', 'value': 'vet', 'icon': Icons.local_hospital},
    {'label': 'Other', 'value': 'other', 'icon': Icons.more_horiz},
    {'label': 'Note', 'value': 'note', 'icon': Icons.edit_note},
    {'label': 'Event', 'value': 'event', 'icon': Icons.event},
  ];


  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildWhiteTopSection(),
        SizedBox(height: 16.w),
        _buildFilterTypeList(),
        SizedBox(height: 16.w),
        Expanded(child: _buildAllRecordList()),
      ],
    );
  }
  String selectedType = 'all';

  Widget _buildFilterTypeList() {
    final Map<String, Map<String, dynamic>> typeStyleMap = {
      'feed': {
        'iconColor': Color.fromRGBO(242, 211, 164, 1),
        'circleColor': Color.fromRGBO(242, 211, 164, 0.2),
      },
      'poop': {
        'iconColor': Color.fromRGBO(122, 41, 23, 1),
        'circleColor': Color.fromRGBO(122, 41, 23, 0.2),
      },
      'walk': {
        'iconColor': Color.fromRGBO(130, 196, 60, 1),
        'circleColor': Color.fromRGBO(130, 196, 60, 0.2),
      },
      'social': {
        'iconColor': Color.fromRGBO(56, 151, 240, 1),
        'circleColor': Color.fromRGBO(56, 151, 240, 0.2),
      },
      'med': {
        'iconColor': Color.fromRGBO(56, 151, 240, 1),
        'circleColor': Color.fromRGBO(56, 151, 240, 0.2),
      },
      'vaccine': {
        'iconColor': Color.fromRGBO(235, 195, 81, 1),
        'circleColor': Color.fromRGBO(235, 195, 81, 0.2),
      },
      'grooming': {
        'iconColor': Color.fromRGBO(161, 38, 255, 1),
        'circleColor': Color.fromRGBO(161, 38, 255, 0.2),
      },
      'daycare': {
        'iconColor': Color.fromRGBO(83, 225, 183, 1),
        'circleColor': Color.fromRGBO(83, 225, 183, 0.2),
      },
      'vet': {
        'iconColor': Color.fromRGBO(255, 197, 66, 1),
        'circleColor': Color.fromRGBO(255, 197, 66, 0.2),
      },
      'other': {
        'iconColor': Color.fromRGBO(209, 109, 106, 1),
        'circleColor': Color.fromRGBO(209, 109, 106, 0.2),
      },
      'all': {
        'iconColor': Color.fromRGBO(253, 236, 206, 1),
        'circleColor': Color.fromRGBO(253, 236, 206, 0.2),
      },
    };

    return SizedBox(
      height: 90.w,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        itemCount: allTypes.length,
        separatorBuilder: (_, __) => SizedBox(width: 12.w),
        itemBuilder: (context, index) {
          final item = allTypes[index];
          final value = item['value'];
          final isSelected = selectedType == value;

          final style = typeStyleMap[value] ?? {};
          final iconColor = style['circleColor'];
          final circleColor = isSelected ? widget.controller.theme.themeData.colorScheme.surface : style['circleColor'];
          final textColor = widget.controller.theme.themeData.colorScheme.onSurface;

          return TypeFilterItem(
            label: item['label'],
            icon: item['icon'],
            count: _getTypeCount(value),
            isSelected: isSelected,
            onTap: () {
              setState(() {
                selectedType = value;
              });
            },
            selectedBackgroundColor: style['circleColor'],
            iconCircleColor: circleColor,
            iconColor: iconColor,
            textColor: textColor,
          );
        },
      ),
    );
  }

  Widget _buildAllRecordList() {
    final todoRecords = _getFilteredTodoRecords();

    final filteredRecords = selectedType == 'all'
        ? todoRecords
        : todoRecords.where((e) => e['type'] == selectedType).toList();

    return ListView.builder(
      padding: EdgeInsets.only(bottom: 100.w),
      itemCount: filteredRecords.length,
      itemBuilder: (context, index) {
        final activity = filteredRecords[index];
        return CommonRecordWidget(
          type: activity['type'] ?? 'note',
          time: activity['time'] ?? '',
          description: activity['description'] ?? '',
          theme: widget.controller.theme,
          onDelete: () {},
        );
      },
    );
  }

  Widget _buildWhiteTopSection() {
    return Container(
      width: double.infinity,
      height: 6.w,
      decoration: BoxDecoration(
        color: widget.controller.theme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.only(bottomRight: Radius.circular(25.r)),
        boxShadow: [
          BoxShadow(
            color: widget.controller.theme.themeData.colorScheme.onSurface.withOpacity(0.15),
            offset: const Offset(0, 0),
            blurRadius: 18,
          ),
        ],
      ),
    );
  }

  int _getTypeCount(String type) {
    final todoRecords = _getFilteredTodoRecords();

    if (type == 'all') return todoRecords.length;
    return todoRecords.where((e) => e['type'] == type).length;
  }

  List<Map<String, String>> _getFilteredTodoRecords() {
    final now = DateTime.now();
    final allData = widget.controller.allActivities;

    final result = <Map<String, String>>[];

    allData.forEach((dateStr, records) {
      final recordDate = DateFormat('yyyy-MM-dd').parse(dateStr);
      if (!recordDate.isBefore(DateTime(now.year, now.month, now.day))) {
        result.addAll(
          records.where((e) => e['remind'] == 'todo'),
        );
      }
    });

    return result;
  }

}

class TypeFilterItem extends StatelessWidget {
  final String label;
  final IconData icon;
  final int count;
  final bool isSelected;
  final VoidCallback onTap;
  final Color selectedBackgroundColor;
  final Color iconCircleColor;
  final Color iconColor;
  final Color textColor;

  const TypeFilterItem({
    super.key,
    required this.label,
    required this.icon,
    required this.count,
    required this.isSelected,
    required this.onTap,
    required this.selectedBackgroundColor,
    required this.iconCircleColor,
    required this.iconColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 55.w,
        height: 75.w,
        decoration: BoxDecoration(
          color: isSelected ? selectedBackgroundColor : context.theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(
            color: isSelected ? selectedBackgroundColor : context.theme.colorScheme.tertiary,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // icon 背后圆形背景
            Container(
              width: 36.w,
              height: 36.w,
              decoration: BoxDecoration(
                color: iconCircleColor,
                shape: BoxShape.circle,
              ),
              child: Icon(icon, size: 20.w, color: iconColor),
            ),
            SizedBox(height: 6.w),
            CommonText(
              label,
              TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
            ),
            CommonText(
              count.toString(),
              TextStyle(
                fontSize: 12.sp,
                color: textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}




import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfoUtil {

  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  static Future<String> getDeviceOs() async {

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await DeviceInfoPlugin().androidInfo;
      return 'Android ${androidInfo.version.release} (SDK ${androidInfo.version.sdkInt})';
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await DeviceInfoPlugin().iosInfo;
      return '${iosInfo.systemName} ${iosInfo.systemVersion}';
    } else {
      return "Unsupported platform";
    }
  }

  static Future<String> getDeviceModel() async {

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await DeviceInfoPlugin().androidInfo;
      return androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await DeviceInfoPlugin().iosInfo;
      return iosInfo.utsname.machine;
    } else {
      return "Unsupported platform";
    }
  }

  static Future<String?> getDeviceIp(InternetAddressType type) async{
    if (type != InternetAddressType.IPv4 &&
        type != InternetAddressType.IPv6 &&
        type != InternetAddressType.unix) {
      return null;
    }
    for (var interface in await NetworkInterface.list()) {
      for (var address in interface.addresses) {
        if (address.type == type) {
          return address.address;
        }
      }
    }

    return null;
  }

  static Future<String?> getDeviceId() async {

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await _deviceInfo.androidInfo;
      return androidInfo.id; // Android device ID rather than serial number to avoid permission issue
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await _deviceInfo.iosInfo;
      return iosInfo.identifierForVendor; // iOS device ID
    }

    return null;
  }
}

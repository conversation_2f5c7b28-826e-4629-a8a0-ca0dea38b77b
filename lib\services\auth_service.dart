import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';


class AuthService extends GetxService {

  // Firebase services
  final FirebaseAuth _fbAuth = FirebaseAuth.instance;

  // Local storage
  final SecureStorage _secureStorage = SecureStorage.instance;
  final Storage _storage = Storage.instance;

  // User dao
  final UserDao _userDao = UserDao();
  final PetDao _petDao = PetDao();

  // user info
  var currentUser = Rx<User?>(null);
  var userAccount = Rx<UserAccount?>(null);
  var userAuthRecord = Rx<UserAuthRecord?>(null);
  var userData = Rx<UserData?>(null);
  var ownedPets = Rx<List<Pet>?>(null);
  var defaultPet = Rx<Pet?>(null);

  late StreamSubscription<User?>? _authStateChangesSubscription;

  static AuthService get instance => Get.find();

  Future<AuthService> init() async {

    // Load current user from firebase auth
    ServiceResponse<User> response = await getCurrentSignUpUser();
    if (response.code == 200) {
      currentUser.value = response.data;
    } else {
      currentUser.value = null;
    }

    // if user is logged in, load user and pet info

    bool hasUserAccount = await _secureStorage.hasData(StorageKeys.userAccount);

    // Load user account from secure storage if exists
    if (hasUserAccount) {
      dynamic account = await _secureStorage.read(StorageKeys.userAccount);
      Map<String, dynamic> storedAccount = jsonDecode(account);
      userAccount.value = UserAccount.fromJson(storedAccount);
    }
    // Load user account from firestore if not exists in secure storage
    else {

      if (currentUser.value != null) {
        userAccount.value = await _userDao.getUserAccountById(fid: currentUser.value!.uid);
        if (userAccount.value != null) {
          await _secureStorage.write(
              StorageKeys.userAccount, jsonEncode(userAccount.value!.toJson()));
        }
      }
    }

    bool hasUserAuthRecord = await _secureStorage.hasData(StorageKeys.userAuthRecord);

    // Load user auth record from secure storage
    if (hasUserAuthRecord) {
      dynamic authRecord = await _secureStorage.read(StorageKeys.userAuthRecord);
      Map<String, dynamic> storedAuthRecord = jsonDecode(authRecord);
      userAuthRecord.value = UserAuthRecord.fromJson(storedAuthRecord);
    }
    // Load user auth record from firestore if not exists in secure storage
    else {
      if (userAccount.value != null) {
        userAuthRecord.value = await _userDao.getLatestUserAuthRecordById(userId: userAccount.value!.sid);
        if (userAuthRecord.value != null) {
          await _secureStorage.write(StorageKeys.userAuthRecord, jsonEncode(userAuthRecord.value!.toJson()));
        }
      }
    }

    bool hasUserData = await _secureStorage.hasData(StorageKeys.userData);

    // Load user data from secure storage
    if (hasUserData) {
      dynamic ud = await _secureStorage.read(StorageKeys.userData);
      Map<String, dynamic> storedData = jsonDecode(ud);
      userData.value = UserData.fromJson(storedData);
    }
    // Load user data from firestore if not exists in secure storage
    else {
      if (userAccount.value != null) {
        userData.value = await _userDao.getUserDataById(userId: userAccount.value!.sid);
        if (userData.value != null) {
          await _secureStorage.write(StorageKeys.userData, jsonEncode(userData.value!.toJson()));
        }
      }
    }

    bool hasOwnedPets = _storage.hasData(StorageKeys.ownedPets);

    // Load owned pets from storage
    if (hasOwnedPets) {
      dynamic op = await _storage.read(StorageKeys.ownedPets);
      List<dynamic> storedData = jsonDecode(op);
      ownedPets.value = storedData.map((e) => Pet.fromJson(e)).toList();
    }
    // Load user data from firestore if not exists in secure storage
    else {
      if (userAccount.value != null) {
        ownedPets.value = await _petDao.getPetsByUser(
          uid: userAccount.value!.sid!,
          isValid: true,
        );
        if (ownedPets.value != null) {
          await _storage.write(StorageKeys.ownedPets, jsonEncode(ownedPets.value!.map((e) => e.toJson()).toList()));
        }
      }
    }

    bool hasDefaultPet = _storage.hasData(StorageKeys.defaultPet);

    // Load default pet from storage
    if (hasDefaultPet) {
      dynamic dp = await _storage.read(StorageKeys.defaultPet);
      Map<String, dynamic> storedData = jsonDecode(dp);
      defaultPet.value = Pet.fromJson(storedData);
    } else {
      defaultPet.value = ownedPets.value?.first;
    }

    // Subscribe to auth state changes
    await subscribeAuthStateChanges(authStateChanged);

    return this;
  }

  Future<void> subscribeAuthStateChanges(Function(User?) callbackAuthStateChanged) async {
    _authStateChangesSubscription = _fbAuth.authStateChanges().listen((User? user) {
      callbackAuthStateChanged(user);
    });
  }

  Future<void> unSubscribeIdTokenChanges() async {
    await _authStateChangesSubscription?.cancel();
  }

  Future<User?> authStateChanged(User? user) async {
    // logout or id token expired
    if (user == null) {
      logger.i('User is currently signed out!');
      currentUser.value = null;
    }
    // new login
    else {
      logger.i('User is signed in!');
      currentUser.value = user;
    }
    return user;
  }

  Future<ServiceResponse<String>> sendVerificationCodePhoneNumber(
      String phoneNumber) async {
    final Completer<ServiceResponse<String>> completer = Completer();
    await _fbAuth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: (PhoneAuthCredential credential) async {

        // Ignore auto verification, keep manual verification only
        // // for sign in
        // await _fbAuth.signInWithCredential(credential);
        // // for link phone number to email account
        // await _fbAuth.currentUser?.linkWithCredential(credential);
        // currentUser.value = _fbAuth.currentUser;
        logger.d('Auto verification successfully, ignored.');
        if(!completer.isCompleted) {
          completer.complete(ServiceResponse(
            code: 201,
            data: null,
            msg: 'Verification completed',
          ));
        }
      },
      verificationFailed: (FirebaseAuthException e) {
        completer.complete(ServiceResponse(
          code: 400,
          data: null,
          msg: 'Phone number verification failed: ${e.message}',
        ));
      },
      codeSent: (String verificationId, int? resendToken) {
        currentUser.value = _fbAuth.currentUser;
        completer.complete(ServiceResponse(
          code: 200,
          data: verificationId,
          msg: 'Verification code sent to the phone number.',
        ));
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        logger.w('Auto verification timeout');
        if(!completer.isCompleted) {
          completer.complete(ServiceResponse(
            code: 408,
            data: verificationId,
            msg: 'Verification code auto retrieval timeout.',
          ));
        }
      },
    );
    return completer.future;
  }

  /// Link email address + password credential to existing user account registered with phone number + sms
  Future<ServiceResponse<String>> linkEmailAddress(String email, String password) async {
    try {
      final credential = EmailAuthProvider.credential(
        email: email,
        password: password,
      );
      final result = await _fbAuth.currentUser?.linkWithCredential(credential);
      currentUser.value = _fbAuth.currentUser;
      logger.i('Linked email address successfully.');

      // Send verification email
      await _fbAuth.currentUser?.sendEmailVerification();

      // Assume user is successfully signed in and you might want to return user id or other info
      return ServiceResponse(
        code: 200,
        msg: 'Email address has been linked to the account and pending on verification.',
      );
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      switch (e.code) {
        case 'provider-already-linked':
          errorMessage = 'There is already email linked but not verified.';
          errorCode = 400;
          break;
        case 'email-already-in-use':
          errorMessage = 'The provided email has been already used by others.';
          errorCode = 409;
          break;
        case 'user-disabled':
          errorMessage =
          'The user corresponding to the given credential has been disabled.';
          errorCode = 403;
          break;
        default:
          errorMessage = 'Verification failed with error code: ${e.code}';
          errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      logger.e('Failed to link phone number: $e');
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  /// Verify email address before updating
  Future<ServiceResponse<String>> verifyBeforeUpdateEmail(String newEmail) async {

    try {

      if (currentUser.value != null) {
        await currentUser.value!.verifyBeforeUpdateEmail(newEmail);

        return ServiceResponse(
          code: 200,
          msg: 'Email has been updated.',
        );
      } else {

        return ServiceResponse(
          code: 401,
          msg: 'Not authenticated. Please log in to send a verification email.',
        );
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      if (e.code == 'email-already-in-use') {
        errorMessage = 'The account already exists for that email.';
        errorCode = 409;
      } else if (e.code == 'missing-email') {
        errorMessage = 'There\'s no linked email address before.';
        errorCode = 400;
      } else if (e.code == 'user-token-expired') {
        errorMessage = 'Please log in again.';
        errorCode = 401;
      } else {
        errorMessage = 'Failed with error code: ${e.code}';
        errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  Future<ServiceResponse<User>> signUpWithEmailAndPassword(
      String email, String password) async {
    try {
      final UserCredential credential =
      await _fbAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      currentUser.value = _fbAuth.currentUser;

      User? user = credential.user;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }

      return ServiceResponse(
        code: 200,
        data: user,
        msg: 'Verification email has been sent. Please check your inbox.',
      );
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      if (e.code == 'weak-password') {
        errorMessage = 'The password provided is too weak.';
        errorCode = 400;
      } else if (e.code == 'email-already-in-use') {
        errorMessage = 'The account already exists for that email.';
        errorCode = 409;
      } else {
        errorMessage = 'Failed with error code: ${e.code}';
        errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  Future<ServiceResponse<User>> getCurrentSignUpUser() async {
    try {
      await _fbAuth.currentUser?.reload();
      currentUser.value = _fbAuth.currentUser;
      if (_fbAuth.currentUser != null) {

        return ServiceResponse(
          code: 200,
          data: _fbAuth.currentUser,
          msg: 'User is already logged in.',
        );
      } else {
        return ServiceResponse(
          code: 404,
          data: null,
          msg: 'No user is currently logged in.',
        );
      }
    } on FirebaseAuthException catch (e) {
      return ServiceResponse(
        code: e.code.hashCode,
        data: null,
        msg: e.message ?? 'An error occurred',
      );
    } catch (e) {
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unexpected error occurred',
      );
    }
  }

  /// Update phone number for the current user registered with phone number
  Future<ServiceResponse<User>> updatePhoneNumber(
      String verificationId, String smsCode) async {
    PhoneAuthCredential credential = PhoneAuthProvider.credential(
      verificationId: verificationId,
      smsCode: smsCode,
    );

    try {
      await _fbAuth.currentUser?.updatePhoneNumber(credential);
      currentUser.value = _fbAuth.currentUser;
      logger.i('Phone number updated successfully.');

      return ServiceResponse(
        code: 200,
        data: null,
        msg: 'Phone number updated successfully.',
      );
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      switch (e.code) {
        case 'invalid-verification-code':
          errorMessage = 'The provided verification code is invalid.';
          errorCode = 400;
          break;
        case 'user-disabled':
          errorMessage =
          'The user corresponding to the given credential has been disabled.';
          errorCode = 403;
          break;
        default:
          errorMessage = 'Verification failed with error code: ${e.code}';
          errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      logger.e('Failed to update phone number: $e');
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  /// Link phone number to existing user account registered with email and password
  Future<ServiceResponse<User>> linkPhoneNumber(
      String verificationId, String smsCode) async {
    try {
      // Create a PhoneAuthCredential with the code
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: verificationId, smsCode: smsCode);

      await _fbAuth.currentUser?.linkWithCredential(credential);
      currentUser.value = _fbAuth.currentUser;
      logger.i('Linked phone number successfully.');

      // Assume user is successfully signed in and you might want to return user id or other info
      return ServiceResponse(
        code: 200,
        data: null,
        msg: 'Phone number has been linked to the account',
      );
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      switch (e.code) {
        case 'invalid-verification-code':
          errorMessage = 'The provided verification code is invalid.';
          errorCode = 400;
          break;
        case 'user-disabled':
          errorMessage =
          'The user corresponding to the given credential has been disabled.';
          errorCode = 403;
          break;
        default:
          errorMessage = 'Verification failed with error code: ${e.code}';
          errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      logger.e('Failed to link phone number: $e');
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  Future<ServiceResponse<User>> verifyCodeLogIn(
      String phoneNumber, String? verificationId, String smsCode) async {
    try {
      // Create a PhoneAuthCredential with the code
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: verificationId ?? "", smsCode: smsCode);

      User? user = (await _fbAuth.signInWithCredential(credential)).user;
      currentUser.value = _fbAuth.currentUser;

      // Assume user is successfully signed in and you might want to return user id or other info
      return ServiceResponse(
        code: 200,
        data: user,
        msg: 'Verification successful and user signed in.',
      );
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      switch (e.code) {
        case 'invalid-verification-code':
          errorMessage = 'The provided verification code is invalid.';
          errorCode = 400;
          break;
        case 'user-disabled':
          errorMessage =
          'The user corresponding to the given credential has been disabled.';
          errorCode = 403;
          break;
        default:
          errorMessage = 'Verification failed with error code: ${e.code}';
          errorCode = 500;
      }
      return ServiceResponse(
        code: errorCode,
        data: null,
        msg: errorMessage,
      );
    } catch (e) {
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unknown error occurred: $e',
      );
    }
  }

  Future<ServiceResponse<User>> loginInWithEmailAndPassword(
      String email, String password) async {
    try {
      final UserCredential credential = await _fbAuth.signInWithEmailAndPassword(
          email: email, password: password);
      // print("id Token: ${_authService.currentUser?.getIdToken().toString()} ");
      User? user = credential.user;
      currentUser.value = _fbAuth.currentUser;

      return ServiceResponse(
          code: 200,
          data: user,
          msg:
          "log in successful, ${credential.user?.email} has been logged in");
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      int errorCode;
      if (e.code == 'invalid-email' || e.code == 'user-not-found') {
        errorMessage =
        'The email address you have entered is not valid or not registed with us.';
        errorCode = 400;
      } else if (e.code == 'user-disabled:') {
        errorMessage = 'The account has been disabled.';
        errorCode = 409;
      } else if (e.code == 'wrong-password') {
        errorMessage = 'The password is not correct.';
        errorCode = 401;
      } else {
        errorMessage = 'Failed with error code: ${e.code}';
        errorCode = 500;
      }
      return ServiceResponse(code: errorCode, data: null, msg: errorMessage);
    } catch (e) {
      return ServiceResponse(
          code: 500, data: null, msg: 'An unknown error occurred: $e');
    }

    // var userData =
    // await FireStoreService().getDoc("users/${credential.user?.uid}");
    // return UserSchema.fromJson(userData.data() as Map<String, dynamic>);
  }

  Future<void> signOut() async {
    await _fbAuth.signOut();
    currentUser.value = null;
    userAccount.value = null;
    userAuthRecord.value = null;
    userData.value = null;
    ownedPets.value = null;
    defaultPet.value = null;
  }

  Future<bool?> checkEmailVerified() async {
    await _fbAuth.currentUser?.reload();
    return _fbAuth.currentUser?.emailVerified;
  }

  Future<ServiceResponse<String>> resetPassword(String email) async {
    try {
      ServiceResponse response =
      await loginInWithEmailAndPassword(email, "password");
      if (response.code == 400) {
        return ServiceResponse(
            code: 400,
            msg:
            "Your $email address is not yet registered. Please follow the provided instructions to complete the registration process before attempting to reset your password");
      } else {
        await _fbAuth.sendPasswordResetEmail(email: email);
        return ServiceResponse(
            code: 200,
            msg:
            "A reset password email has been sent to: $email. Please follow the instructions to complete resetting your password.");
      }
    } on FirebaseAuthException catch (e) {
      return ServiceResponse(
        code: e.code.hashCode,
        data: null,
        msg: e.message ?? 'An error occurred',
      );
    } catch (e) {
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unexpected error occurred',
      );
    }
  }

  Future<ServiceResponse<String>> updateUserPassword(
      String oldPassword, String password) async {
    User? user = _fbAuth.currentUser;
    if (user == null) {
      return ServiceResponse(
        code: 401,
        msg: "No user is currently signed in",
        data: null,
      );
    }

    AuthCredential credential = EmailAuthProvider.credential(
      email: user.email ?? '',
      password: oldPassword,
    );

    try {
      // Re-authenticate the user
      await user.reauthenticateWithCredential(credential);

      // Update the password
      await user.updatePassword(password);

      currentUser.value = _fbAuth.currentUser;

      return ServiceResponse(
        code: 200,
        msg:
        "The new password has been updated for account ${user.email} successfully",
      );
    } on FirebaseAuthException catch (error) {
      String errorMessage = 'Password change failed';

      if (error.code == 'wrong-password') {
        errorMessage = 'The old password is incorrect';
      } else if (error.code == 'weak-password') {
        errorMessage = 'The new password is too weak';
      } else if (error.code == 'requires-recent-login') {
        errorMessage =
        'This operation is sensitive and requires recent authentication. Log in again before retrying this request.';
      } else {
        errorMessage = 'An unexpected error occurred: ${error.message}';
      }

      return ServiceResponse(
        code: error.code.hashCode,
        data: null,
        msg: errorMessage,
      );
    } catch (error) {
      logger.e("An unexpected error occurred: $error");
      return ServiceResponse(
        code: 500,
        data: null,
        msg: 'An unexpected error occurred',
      );
    }
  }

  Future<void> deleteAuthUser() async {
    await _fbAuth.currentUser?.delete();
    currentUser.value = null;
  }

}

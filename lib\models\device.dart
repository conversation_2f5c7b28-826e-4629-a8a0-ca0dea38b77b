import 'dart:convert';

//import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'base_full_model.dart';

part 'device.g.dart';

@JsonSerializable()
class Device extends BaseFullModel {

  DeviceType? deviceType;
  DeviceModel? deviceModel;
  String? manufacturer;
  String? manufacturerSerialNumber;
  String? firmwareVersion;
  String? hardwareVersion;
  String uid; // User id
  List<String>? pets; // Pet id list

  Device({
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
    this.deviceType,
    this.deviceModel,
    this.manufacturer,
    this.manufacturerSerialNumber,
    this.firmwareVersion,
    this.hardwareVersion,
    required this.uid,
    this.pets,
  });

  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$DeviceToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory Device.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return Device.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Device.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Device.fromJson(jsonData);
  }

  factory Device.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Device.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(Device? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    required DeviceType deviceType,
    required DeviceModel deviceModel,
    required String manufacturer,
    required String manufacturerSerialNumber,
    String? firmwareVersion,
    String? hardwareVersion,
    required String uid,
    List<String>? pets,
  }) {

    return Device(
      sid: uuid.v4(),
      deviceType: deviceType,
      deviceModel: deviceModel,
      manufacturer: manufacturer,
      manufacturerSerialNumber: manufacturerSerialNumber,
      firmwareVersion: firmwareVersion,
      hardwareVersion: hardwareVersion,
      uid: uid,
      pets: pets,
    );
  }

  static Device? copyFrom(Device? other) {
    return other == null ? null : Device(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      deviceType: other.deviceType,
      deviceModel: other.deviceModel,
      manufacturer: other.manufacturer,
      manufacturerSerialNumber: other.manufacturerSerialNumber,
      firmwareVersion: other.firmwareVersion,
      hardwareVersion: other.hardwareVersion,
      uid: other.uid,
      pets: other.pets,
    );
  }

  static String get collection => 'device';

  @override
  String toString() {
    return jsonEncode(this);
  }

}

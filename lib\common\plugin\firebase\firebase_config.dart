import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import 'package:onenata_app/common/const/const_i.dart';

class FirebaseConfig {

  static Future<void> enableFirebaseEmulators() async {

    if (Env.firebaseEmulatorAuthEnabled) {
      await FirebaseAuth.instance.useAuthEmulator(Env.firebaseEmulatorAuthHost, Env.firebaseEmulatorAuthPort);
    }

    if (Env.firebaseEmulatorFirestoreEnabled) {
      FirebaseFirestore.instance.useFirestoreEmulator(Env.firebaseEmulatorFirestoreHost, Env.firebaseEmulatorFirestorePort);
    }

    if (Env.firebaseEmulatorStorageEnabled) {
      await FirebaseStorage.instance.useStorageEmulator(Env.firebaseEmulatorStorageHost, Env.firebaseEmulatorStoragePort);
    }
  }
}
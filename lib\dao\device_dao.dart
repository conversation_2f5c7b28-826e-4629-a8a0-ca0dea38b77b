import 'dart:async';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/models/models_i.dart';

class DeviceDao {

  // Collection reference
  final CollectionReference _deviceCollection = FirebaseFirestore.instance.collection(Device.collection);

  // Device operations ----------------------------------------------
  Future<void> addDevice (Device device) async {

    DocumentReference deviceRef = _deviceCollection.doc(device.sid);
    Map<String, dynamic> dataDoc = device.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await deviceRef.set(dataDoc);
  }

  Future<void> updateDevice (Device device, {bool mergeOption = false}) async {

    DocumentReference deviceRef = _deviceCollection.doc(device.sid!);
    Map<String, dynamic> dataDoc = device.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await deviceRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteDevice (String deviceId) async {

    DocumentReference deviceRef = _deviceCollection.doc(deviceId);
    await deviceRef.delete();
  }

  Future<void> softDeleteDevice (String deviceId) async {

    DocumentReference deviceRef = _deviceCollection.doc(deviceId);
    await deviceRef.set({CommonField.isValid: 0});
  }

  Future<Device?> getDeviceById (String deviceId) async {

    DocumentSnapshot doc = await _deviceCollection.doc(deviceId).get();

    if (doc.exists) {
      return Device.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastCreateDate] is the create_date of the last message in the previous page
  Future<List<Device>?> getDeviceList ({String? uid, int? pageSize = 20, int? lastCreateDate, bool? isValid}) async {

    Query query = _deviceCollection;

    // Conditions
    if (uid != null) {
      query = query.where('uid', isEqualTo: uid);
    }
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('create_date', descending: true);

    // Pagination start
    if (lastCreateDate != null) {
      query = query.startAfter([lastCreateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((p)=> Device.fromFirestoreData(p)).toList();
    } else {
      return null;
    }
  }

  // Device status message operations ----------------------------------------------
  CollectionReference statusMsgColRef (String deviceId) {
    return _deviceCollection.doc(deviceId).collection(DeviceStatusMessage.collection);
  }

  Future<void> addDeviceStatusMessage (DeviceStatusMessage message) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc);
  }

  Future<void> updateDeviceStatusMessage (DeviceStatusMessage message, {bool mergeOption = false}) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteDeviceStatusMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.delete();
  }

  Future<void> softDeleteDeviceStatusMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.set({CommonField.isValid: 0});
  }

  Future<DeviceStatusMessage?> getDeviceStatusMessageById (String deviceId, String msgId) async {

    DocumentSnapshot doc = await statusMsgColRef(deviceId).doc(msgId).get();

    if (doc.exists) {
      return DeviceStatusMessage.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastGenerateDate] is the create_date of the last message in the previous page
  Future<List<DeviceStatusMessage>?> getDeviceStatusMessageList ({required String deviceId, int? pageSize = 20, int? lastGenerateDate, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('generate_date', descending: true);

    // Pagination start
    if (lastGenerateDate != null) {
      query = query.startAfter([lastGenerateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> DeviceStatusMessage.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<DeviceStatusMessage?> getLatestDeviceStatusMessageByDevice ({required String deviceId, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('generate_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return DeviceStatusMessage.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

  // Device alert message operations ----------------------------------------------
  CollectionReference alertMsgColRef (String deviceId) {
    return _deviceCollection.doc(deviceId).collection(DeviceAlertMessage.collection);
  }

  Future<void> addDeviceAlertMessage (DeviceAlertMessage message) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc);
  }

  Future<void> updateDeviceAlertMessage (DeviceAlertMessage message, {bool mergeOption = false}) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteDeviceAlertMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.delete();
  }

  Future<void> softDeleteDeviceAlertMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.set({CommonField.isValid: 0});
  }

  Future<DeviceAlertMessage?> getDeviceAlertMessageById (String deviceId, String msgId) async {

    DocumentSnapshot doc = await statusMsgColRef(deviceId).doc(msgId).get();

    if (doc.exists) {
      return DeviceAlertMessage.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastGenerateDate] is the create_date of the last message in the previous page
  Future<List<DeviceAlertMessage>?> getDeviceAlertMessageList ({required String deviceId, int? pageSize = 20, int? lastGenerateDate, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('generate_date', descending: true);

    // Pagination start
    if (lastGenerateDate != null) {
      query = query.startAfter([lastGenerateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> DeviceAlertMessage.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<DeviceAlertMessage?> getLatestDeviceAlertMessageByDevice ({required String deviceId, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('generate_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return DeviceAlertMessage.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

  // Device geo location message operations ----------------------------------------------
  CollectionReference geoLocationMsgColRef (String deviceId) {
    return _deviceCollection.doc(deviceId).collection(DeviceGeoLocationMessage.collection);
  }

  Future<void> addDeviceGeoLocationMessage (DeviceGeoLocationMessage message) async {

    DocumentReference msgRef = geoLocationMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc);
  }

  Future<void> updateDeviceGeoLocationMessage (DeviceGeoLocationMessage message, {bool mergeOption = false}) async {

    DocumentReference msgRef = geoLocationMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteDeviceGeoLocationMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = geoLocationMsgColRef(deviceId).doc(msgId);
    await msgRef.delete();
  }

  Future<void> softDeleteDeviceGeoLocationMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = geoLocationMsgColRef(deviceId).doc(msgId);
    await msgRef.set({CommonField.isValid: 0});
  }

  Future<DeviceGeoLocationMessage?> getDeviceGeoLocationMessageById (String deviceId, String msgId) async {

    DocumentSnapshot doc = await geoLocationMsgColRef(deviceId).doc(msgId).get();

    if (doc.exists) {
      return DeviceGeoLocationMessage.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastGenerateDate] is the create_date of the last message in the previous page
  Future<List<DeviceGeoLocationMessage>?> getDeviceGeoLocationMessageList ({required String deviceId, int? pageSize = 20, int? lastGenerateDate, bool? isValid}) async {

    Query query = geoLocationMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('generate_date', descending: true);

    // Pagination start
    if (lastGenerateDate != null) {
      query = query.startAfter([lastGenerateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> DeviceGeoLocationMessage.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<DeviceGeoLocationMessage?> getLatestDeviceGeoLocationMessageByDevice ({required String deviceId, bool? isValid}) async {

    Query query = geoLocationMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('generate_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return DeviceGeoLocationMessage.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

  // Device feeding message operations ----------------------------------------------
  CollectionReference feedingMsgColRef (String deviceId) {
    return _deviceCollection.doc(deviceId).collection(DeviceFeedingMessage.collection);
  }

  Future<void> addDeviceFeedingMessage (DeviceFeedingMessage message) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc);
  }

  Future<void> updateDeviceFeedingMessage (DeviceFeedingMessage message, {bool mergeOption = false}) async {

    DocumentReference msgRef = statusMsgColRef(message.deviceId).doc(message.sid);
    Map<String, dynamic> dataDoc = message.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await msgRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteDeviceFeedingMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.delete();
  }

  Future<void> softDeleteDeviceFeedingMessage (String deviceId, String msgId) async {

    DocumentReference msgRef = statusMsgColRef(deviceId).doc(msgId);
    await msgRef.set({CommonField.isValid: 0});
  }

  Future<DeviceFeedingMessage?> getDeviceFeedingMessageById (String deviceId, String msgId) async {

    DocumentSnapshot doc = await statusMsgColRef(deviceId).doc(msgId).get();

    if (doc.exists) {
      return DeviceFeedingMessage.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastGenerateDate] is the create_date of the last message in the previous page
  Future<List<DeviceFeedingMessage>?> getDeviceFeedingMessageList ({required String deviceId, int? pageSize = 20, int? lastGenerateDate, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('generate_date', descending: true);

    // Pagination start
    if (lastGenerateDate != null) {
      query = query.startAfter([lastGenerateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> DeviceFeedingMessage.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<DeviceFeedingMessage?> getLatestDeviceFeedingMessageByDevice ({required String deviceId, bool? isValid}) async {

    Query query = statusMsgColRef(deviceId);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('generate_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return DeviceFeedingMessage.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_alert_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceAlertMessage _$DeviceAlertMessageFromJson(Map<String, dynamic> json) =>
    DeviceAlertMessage(
      sid: json['sid'] as String?,
      pid: json['pid'] as String?,
      deviceId: json['deviceId'] as String,
      generateDate: (json['generateDate'] as num?)?.toInt(),
      sendDate: (json['sendDate'] as num?)?.toInt(),
      receiveDate: (json['receiveDate'] as num?)?.toInt(),
      alertType: $enumDecodeNullable(
          _$DeviceAlertMessageTypeEnumMap, json['alertType']),
      description: json['description'] as String?,
    );

Map<String, dynamic> _$DeviceAlertMessageToJson(DeviceAlertMessage instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'pid': instance.pid,
      'deviceId': instance.deviceId,
      'generateDate': instance.generateDate,
      'sendDate': instance.sendDate,
      'receiveDate': instance.receiveDate,
      'alertType': _$DeviceAlertMessageTypeEnumMap[instance.alertType],
      'description': instance.description,
    };

const _$DeviceAlertMessageTypeEnumMap = {
  DeviceAlertMessageType.moAlertLowBattery: 'MO91_01',
  DeviceAlertMessageType.moAlertNoGps: 'MO91_02',
};

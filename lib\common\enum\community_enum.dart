import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum PostType {

  moment('MOM'),
  reHome('REH'),
  help('HELP'),
  event('EVENT'),
  ;

  final String code; // Stored code in database
  const PostType(this.code);

  // Factory constructor to create a TestType object based on the code
  factory PostType.fromCode(String code) {
    return PostType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

@JsonEnum(valueField: 'code')
enum PostContentType {

  onlyText('TXT', 'community.post.content.type.text'),
  withImage('IMG', 'community.post.content.type.image'),
  withVideo('VID', 'community.post.content.type.video'),
  ;

  final String code;
  final String t18key; // Stored code in database
  const PostContentType(this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory PostContentType.fromCode(String code) {
    return PostContentType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

@JsonEnum(valueField: 'code')
enum PublishVisibility {

  public('PUB', 'community.publish.visibility.public'),
  private('PVT', 'community.publish.visibility.private'),
  friend('FRD', 'community.publish.visibility.friend'),
  ;

  final String code; // Stored code in database
  final String t18key; // Translation key
  const PublishVisibility(this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory PublishVisibility.fromCode(String code) {
    return PublishVisibility.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';
import '../health/health_pages_i.dart';

class PendingVerifyPage extends StatefulWidget {
  const PendingVerifyPage({super.key});

  @override
  PendingVerifyPageState createState() => PendingVerifyPageState();
}

class PendingVerifyPageState extends State<PendingVerifyPage>{

  late Future<PendingVerifyPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => PendingVerifyPageController().init());
    // controller = Get.put(PendingVerifyPageController() as Future<PendingVerifyPageController>);
  }
  @override
  Widget build(BuildContext context) {

    return FutureBuilder<PendingVerifyPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Indicator for loading
          return const Center(
            child: LoadingWidget(),
          );
        } else if (snapshot.hasError) {
          // Error handling
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          // Page functions
          final controller = snapshot.data!;

          // Label title
          Widget pageLabelTitle = CommonText(
            'auth.label.verify'.t18,
            controller.theme.textStyleExtension.authPageTitle,
          );

          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.primary,
            body: Stack(
              alignment: Alignment.topCenter,
              children: [
                SizedBox(
                  child: AuthWidgetBuilder.buildLightRing(controller.theme),
                ),
                AuthWidgetBuilder.buildWhiteSection(controller.theme),
                Column(
                  children: [
                    SizedBox(
                      height: controller.theme.layout.authAvatarHeaderLineHeight +
                          controller.theme.layout.authAvatarTitleMarginTop + controller.theme.layout.authWhiteSectionCircular / 2
                    ),
                    SizedBox(
                      height: 85.w,
                      child: Obx(()=> AuthWidgetBuilder.buildLabel(controller.theme, title: pageLabelTitle, desc: controller.pageLabelDesc.value)),
                    ),
                    Expanded(child: SizedBox.shrink()),
                    SizedBox(
                      height: 60.w,
                      child: AuthWidgetBuilder.buildButton(
                        controller.theme, controller.buttonController,
                        text: 'auth.button.verify.code'.t18,
                        isEnabled: true,
                        onPressed: controller.onPressed,
                      ),
                    ),
                    SizedBox(height: 35.w,)
                  ],
                ),
                Positioned(
                  top: 60.w,
                  left: 24.w,
                  child: AuthWidgetBuilder.buildBackButton(controller.theme, context),
                ),
              ],
            )
          );
        }
      }
    );
  }
}

class PendingVerifyPageController extends GetxController{

  final AuthService authService = AuthService.instance;
  final UserService userService = UserService();

  // Theme plugin
  late ThemePlugin theme;

  final CommonButtonController buttonController = Get.put(CommonButtonController());
  var pageLabelDesc = Rx<CommonText?>(null);

  Future<PendingVerifyPageController> init() async {

    if (authService.currentUser.value == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAll(HealthPage());
        return;
      });
    }

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    // Init page label
    pageLabelDesc.value = CommonText(
      'auth.label.verify.desc'.t18,
      theme.textStyleExtension.authPageDesc,
    );

    return this;
  }

  Future<void> onPressed() async {

    // Reload email verified status
    bool? isEmailVerified = await authService.checkEmailVerified();

    if (isEmailVerified != null && isEmailVerified == true) {

      // query user account from firestore
      String fid = authService.currentUser.value!.uid;
      UserAccount? account = await userService.getUserAccountById(fid: fid);

      // account exists => set email verified to true
      if (account != null) {

        // for updating email
        account.email = authService.currentUser.value!.email;
        account.isEmailVerified = true;

        // update user account
        await userService.updateUserAccount(account);
      }
      // account not exists => create user account
      else {
        await userService.createUserAccount(authService.currentUser.value!, AuthChannel.emailPassword);
      }

      // Not to add add auth record since it will be created in the login email page
      // Not to create user data since it will be added in sign up email page

      // Link phone number
      Get.offAll(RootPage(page: PageNavigation.profile));
      return;
    }
    else {
      Get.snackbar('auth.label.verify'.t18, 'auth.label.verify.desc'.t18);
    }
  }

}

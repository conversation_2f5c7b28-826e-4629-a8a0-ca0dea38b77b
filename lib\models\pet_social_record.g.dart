// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_social_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetSocialRecord _$PetSocialRecordFromJson(Map<String, dynamic> json) =>
    PetSocialRecord(
      sid: json['sid'] as String,
      startTime: (json['startTime'] as num?)?.toInt(),
      endTime: (json['endTime'] as num?)?.toInt(),
      location: json['location'] as String?,
      targetTypeTags: (json['targetTypeTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      behaviorTags: (json['behaviorTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PetSocialRecordToJson(PetSocialRecord instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'location': instance.location,
      'targetTypeTags': instance.targetTypeTags,
      'behaviorTags': instance.behaviorTags,
      'images': instance.images,
      'notes': instance.notes,
    };

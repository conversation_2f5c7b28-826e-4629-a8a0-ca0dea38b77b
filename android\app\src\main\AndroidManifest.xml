<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature android:name="android.hardware.camera" android:required="true" />

    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>

    <!-- for Android 13- -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- for Android 13+ -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />-->

    <!--    Notification-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <application
        android:label="onenata_app"
        android:icon="@mipmap/ic_launcher">

        <!-- 1 Firebase Messaging Service (Handles FCM Messages) -->
<!--        <service-->
<!--            android:name=".java.MyFirebaseMessagingService"-->
<!--            android:exported="false">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.google.firebase.MESSAGING_EVENT" />-->
<!--            </intent-filter>-->
<!--        </service>-->

        <!-- 2 Firebase Component Discovery Service (Auto-Registers Firebase Services) -->
<!--        <service-->
<!--            android:name="com.google.firebase.components.ComponentDiscoveryService"-->
<!--            android:exported="false">-->
<!--            <meta-data-->
<!--                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"-->
<!--                android:value="com.google.firebase.messaging.FirebaseMessagingRegistrar" />-->
<!--        </service>-->

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- Fix: Add tools:replace="android:value" to override duplicate entry -->
<!--        <meta-data-->
<!--            android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"-->
<!--            android:value="com.google.firebase.messaging.FirebaseMessagingRegistrar"-->
<!--            tools:replace="android:value" />-->
<!--        <meta-data-->
<!--            android:name="com.google.firebase.messaging.default_notification_icon"-->
<!--            android:resource="@drawable/ic_stat_ic_notification" />-->
<!--        &lt;!&ndash; Set color used with incoming notification messages. This is used when no color is set for the incoming-->
<!--             notification message. See README(https://goo.gl/6BKBk7) for more. &ndash;&gt;-->
<!--        <meta-data-->
<!--            android:name="com.google.firebase.messaging.default_notification_color"-->
<!--            android:resource="@color/colorAccent" />-->
<!--        &lt;!&ndash; [END fcm_default_icon] &ndash;&gt;-->
<!--        &lt;!&ndash; [START fcm_default_channel] &ndash;&gt;-->
<!--        <meta-data-->
<!--            android:name="com.google.firebase.messaging.default_notification_channel_id"-->
<!--            android:value="@string/default_notification_channel_id" />-->
<!--        <meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyD5Pq7gLWaVXvsB3jc2Qbo0t2LiQO5LFpU"/>-->
        <meta-data android:name="com.google.android.geo.API_KEY" android:value="${GOOGLE_MAP_API_KEY}"/>
<!--        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version"/>-->
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>

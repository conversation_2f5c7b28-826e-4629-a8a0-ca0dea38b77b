import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum PetGender {

  boy('M', 'pet.info.gender.boy'),
  girl('F', 'pet.info.gender.girl'),
  ;

  final String code; // Stored code in database
  final String t18key; // Stored code in database
  const PetGender(this.code, this.t18key);

  factory PetGender.fromCode(String code) {
    return PetGender.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

@JsonEnum(valueField: 'code')
enum PetType {

  cat('CAT', 'pet.info.type.cat'),
  dog('DOG', 'pet.info.type.dog'),
  others('OTH', 'pet.info.type.others'),
  ;

  final String code; // Stored code in database
  final String t18key; // Stored code in database
  const PetType(this.code, this.t18key);

  factory PetType.fromCode(String code) {
    return PetType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

@JsonEnum(valueField: 'code')
enum PetVisibility {

  public('PUB', 'pet.visibility.public'),
  friend('FRD', 'pet.visibility.friend'),
  private('PVT', 'pet.visibility.private'),
  ;

  final String code; // Stored code in database
  final String t18key; // Translation key
  const PetVisibility(this.code, this.t18key);

  // Factory constructor to create a TestType object
  factory PetVisibility.fromCode(String code) {
    return PetVisibility.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
  factory PetVisibility.fromT18Key(String t18key) {
    return PetVisibility.values.firstWhere((element) => element.t18key == t18key, orElse: () => throw ArgumentError('Invalid t18 key: $t18key'));
  }
}

import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'pet_social_record.g.dart';

@JsonSerializable()
class PetSocialRecord{
  String sid;
  int? startTime;
  int? endTime;
  String? location;
  List<String>? targetTypeTags;
  List<String>? behaviorTags;
  List<String>? images;
  String? notes;

  PetSocialRecord({
    required this.sid,
    this.startTime,
    this.endTime,
    this.location,
    this.targetTypeTags,
    this.behaviorTags,
    this.images,
    this.notes,
  });

  factory PetSocialRecord.fromJson(Map<String, dynamic> json) =>
      _$PetSocialRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetSocialRecordToJson(this);

  static create({
    int? startTime,
    int? endTime,
    String? location,
    List<String>? targetTypeTags,
    List<String>? behaviorTags,
    List<String>? images,
    String? notes,
  }) {
    return PetSocialRecord(
      sid: uuid.v4(),
      startTime: startTime,
      endTime: endTime,
      location: location,
      targetTypeTags: targetTypeTags,
      behaviorTags: behaviorTags,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetSocialRecord other) {
    return PetSocialRecord(
      sid: other.sid,
      notes: other.notes,
      startTime: other.startTime,
      endTime: other.endTime,
      location: other.location,
      targetTypeTags: other.targetTypeTags,
      behaviorTags: other.behaviorTags,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

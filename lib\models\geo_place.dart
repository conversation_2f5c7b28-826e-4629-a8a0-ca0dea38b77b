import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/geo_reviews.dart';

import 'base_full_model.dart';
import 'geo_i.dart';

part 'geo_place.g.dart';

@JsonSerializable()
class GeoPlace extends BaseFullModel {
  String? placeId; // id in google map
  String? internationalPhoneNumber;
  List<String>? types;
  bool? allowsDogs;
  String? formattedAddress;
  GeoDisplayName? displayName;
  GeoPostalAddress? postalAddress;
  BusinessStatus? businessStatus;
  GeoLocation? location;
  GeoOpeningHours? regularOpeningHours;
  List<GeoPhoto>? photos;
  double? rating;
  List<GeoReviews>? reviews;

  GeoPlace({
    required this.placeId,
    this.internationalPhoneNumber,
    this.types,
    this.allowsDogs,
    this.formattedAddress,
    this.displayName,
    this.postalAddress,
    this.businessStatus,
    this.location,
    this.regularOpeningHours,
    this.photos,
    this.rating,
    this.reviews,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory GeoPlace.fromJson(Map<String, dynamic> json) =>
      _$GeoPlaceFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GeoPlaceToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = {};
    // print("************** places: data: ************* \n  ${apiJson}");
    apiJson.forEach((key, value) {
      if (key == 'id') {
        jsonData['placeId'] = value;
        return;
      } else if (key == 'displayName') {
        jsonData[key] = GeoDisplayName.fromApi(value);
      } else if (key == 'postalAddress') {
        jsonData[key] = GeoPostalAddress.fromApi(value);
      } else if (key == 'location') {
        jsonData[key] = GeoLocation.fromApi(value);
      } else if (key == 'regularOpeningHours') {
        jsonData[key] = GeoOpeningHours.fromApi(value);
      } else if (key == 'photos') {
        jsonData[key] =
            (value as List).map((e) => GeoPhoto.fromApi(e)).toList();
      } else if (key == 'reviews') {
        jsonData[key] =
            (value as List).map((e) => GeoReviews.fromApi(e)).toList();
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoPlace.fromApiMap(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoPlace.fromJson(jsonData);
  }

  factory GeoPlace.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;

    final Map<String, dynamic> jsonData = {};
    dbData.forEach((key, value) {
      if (key == 'display_name') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            GeoDisplayName.fromFirestore(value);
      } else if (key == 'postal_address') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            GeoLocation.fromFirestore(value);
      } else if (key == 'location') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            GeoLocation.fromFirestore(value);
      } else if (key == 'regular_opening_hours') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            GeoOpeningHours.fromFirestore(value);
      } else if (key == 'photos') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = (dbData['photos'] ?? [])
            .map((e) => GeoPhoto.fromFirestore(e))
            .toList();
      } else if (key == 'reviews') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = (dbData['reviews'] ?? [])
            .map((e) => GeoPhoto.fromFirestore(e))
            .toList();
      } else if (key == 'allows_dogs') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            JsonUtil.boolFromJson(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return GeoPlace.fromJson(jsonData);
  }

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'id') {
        dbData[JsonUtil.camelCaseToSnakeCase('placeId')] = value;
      } else if (key == 'displayName') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            GeoDisplayName.toFirestoreJson(value);
      } else if (key == 'postalAddress') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            GeoLocation.toFirestoreJson(value);
      } else if (key == 'location') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            GeoLocation.toFirestoreJson(value);
      } else if (key == 'regularOpeningHours') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            GeoOpeningHours.toFirestoreJson(value);
      } else if (key == 'photos') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = (jsonData['photos'] ?? [])
            .map((e) => GeoPhoto.toFirestoreJson(e))
            .toList();
      } else if (key == 'reviews') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = (jsonData['reviews'] ?? [])
            .map((e) => GeoPhoto.toFirestoreJson(e))
            .toList();
      } else if (key == 'allowsDogs') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = JsonUtil.boolToJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static String collection() => 'geo_place';

  static create(
      {String? placeId,
      String? name,
      String? internationalPhoneNumber,
      List<String>? types,
      bool? allowsDogs,
      String? formattedAddress,
      GeoDisplayName? displayName,
      GeoPostalAddress? postalAddress,
      BusinessStatus? businessStatus,
      GeoLocation? location,
      GeoOpeningHours? regularOpeningHours,
      List<GeoPhoto>? photos,
      double? rating,
      List<GeoReviews>? reviews}) {
    return GeoPlace(
      sid: uuid.v4(),
      placeId: placeId,
      name: name,
      internationalPhoneNumber: internationalPhoneNumber,
      types: types,
      allowsDogs: allowsDogs,
      formattedAddress: formattedAddress,
      displayName: displayName,
      postalAddress: postalAddress,
      businessStatus: businessStatus,
      location: location,
      regularOpeningHours: regularOpeningHours,
      photos: photos,
      rating: rating,
      isValid: true,
      reviews: reviews,
    );
  }

  static GeoPlace copyFrom(GeoPlace other) {
    return GeoPlace(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      placeId: other.placeId,
      internationalPhoneNumber: other.internationalPhoneNumber,
      types: other.types,
      allowsDogs: other.allowsDogs,
      formattedAddress: other.formattedAddress,
      displayName: GeoDisplayName.copyFrom(other.displayName),
      postalAddress: GeoPostalAddress.copyFrom(other.postalAddress),
      businessStatus: other.businessStatus,
      location: GeoLocation.copyFrom(other.location),
      regularOpeningHours: GeoOpeningHours.copyFrom(other.regularOpeningHours),
      photos: other.photos?.map((e) => GeoPhoto.copyFrom(e)).toList(),
      rating: other.rating,
      reviews: other.reviews,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonEnum(valueField: 'code')
enum BusinessStatus {
  unspecified('BUSINESS_STATUS_UNSPECIFIED', 'geo.business.status.unspecified'),
  operational('OPERATIONAL', 'geo.business.status.operational'),
  closedTemporarily(
      'CLOSED_TEMPORARILY', 'geo.business.status.closed.temporary'),
  closedPermanently(
      'CLOSED_PERMANENTLY', 'geo.business.status.closed.permanent');

  final String code;
  final String t18key;
  const BusinessStatus(this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory BusinessStatus.fromCode(String code) {
    return BusinessStatus.values.firstWhere((element) => element.code == code,
        orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/pet.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/service_response.dart';
import '../../../views/component/profile/profile_widget_builder.dart';
import '../../component/auth/auth_widget_builder.dart';

class CelebrationPage extends StatefulWidget {
  const CelebrationPage({super.key});

  @override
  CelebrationPageState createState() => CelebrationPageState();
}

class CelebrationPageState extends State<CelebrationPage> {
  late Future<CelebrationPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => CelebrationPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CelebrationPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                alignment: Alignment.center,
                children: [
                  _buildWhiteSection(),
                  Positioned(
                    top: 60.w,
                    child: Text(
                      "complete".t18,
                      style:
                      controller.theme.textStyleExtension.appAvatarSelect2,
                    ),
                  ),
                  _buildProgressIndicator(controller),
                  Positioned(
                    top: 0,
                    child: ConfettiWidget(
                      confettiController:
                          controller.confettiController,
                      blastDirectionality: BlastDirectionality.explosive,
                      blastDirection: 3.14 / 2,
                      minBlastForce: 30,
                      maxBlastForce: 50,
                      emissionFrequency: 0.05,
                      numberOfParticles: 40,
                      gravity: 0.8,
                      particleDrag: 0.15,
                    ),
                  ),
                  Positioned(
                    top: 260.w,
                    child: Container(
                      width: 186.w,
                      height: 186.w,
                      child: FutureBuilder<Widget>(
                        future: _buildAvatar(controller),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return const CircularProgressIndicator(); // or SizedBox(height: 125.w);
                          } else if (snapshot.hasError) {
                            return Text('Error loading avatar');
                          } else {
                            return snapshot.data!;
                          }
                        },
                      ),
                      // child: ClipOval(
                      //   child: Image.asset(
                      //     "assets/images/pet_placeholder.png",
                      //     width: 186.w,
                      //     height: 186.w,
                      //     fit: BoxFit.cover,
                      //   ),
                      // ),
                    ),
                  ),
                  Positioned(
                    top: 500.w,
                    child: Text(
                      "welcome".t18,
                      style: controller.theme.textStyleExtension.authPageCelebration,
                    ),
                  ),
                ],
              ),
            );
          }
        });
  }

  Future<Widget> _buildAvatar(CelebrationPageController controller) async {
    return Column(
      children: [
        Center(
          child: GestureDetector(
            child: AuthWidgetBuilder.buildAvatar(
              controller.theme,
              avatar: await ProfileWidgetBuilder.buildPetAvatar(
                  controller.theme,
                  userId: controller._authService.userAccount.value!.sid!,
                  petId: controller.pet.sid!,
                  avatar: controller.pet.avatar,
                  size: 125
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWhiteSection() {
    return const CommonWhiteSection(
      width: 403,
      height: 149,
      top: 0,
      borderRadius: 0,
    );
  }

  Widget _buildPetAvatar(CelebrationPageController controller) {
    return GestureDetector(
      onTap: controller.pickPetImage,
      child: Obx(() => ProfileWidgetBuilder.buildAvatar1(
        controller.theme,
        avatar: controller.pageHeaderPetAvatar.value,
        editable: true,
      )),
    );
  }

  Widget _buildProgressIndicator(CelebrationPageController controller) {
    return Positioned(
      width: 327.w,
      height: 4.w,
      top: 120.w,
      left: 38.w,
      child: Container(
        width: 327.w,
        height: 4.w,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              controller.theme.themeData.colorScheme.secondary,
              controller.theme.themeData.colorScheme.primary,
            ],
          ),
        ),
      ),
    );
  }
}

class CelebrationPageController extends GetxController {
  late ConfettiController confettiController;
  final PetService _petService = PetService();
  final AuthService _authService = AuthService.instance;
  late Pet pet;
  Rx<Widget?> pageHeaderPetAvatar = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;

  Future<CelebrationPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    final dynamic arg = Get.arguments;
    if (arg is Pet) {
      pet = arg;
    } else {
      throw Exception('Pet not passed to CelebrationPage');
    }

    Future.delayed(const Duration(seconds: 2), () {
      Get.offAllNamed('/root');
    });

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    confettiController =
        ConfettiController(duration: const Duration(seconds: 3));
    _startConfetti();
  }

  void _startConfetti() {
    Future.delayed(const Duration(milliseconds: 200), () {
      confettiController.play();
    });
  }

  @override
  void onClose() {
    confettiController.dispose();
    super.onClose();
  }

  Future<void> pickPetImage() async {
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      String pid = pet.sid!;
      ServiceResponse<String> res = await _petService.uploadPetAvatar(uid: uid, pid: pid, image: image);
      if (res.code == 200) {
        pageHeaderPetAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
            theme,
            userId: uid,
            petId: pid,
            avatar: res.data,
            size: 186
        );
      }
    }
  }
}

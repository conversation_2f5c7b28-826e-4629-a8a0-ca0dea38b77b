// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerInfo _$CustomerInfoFromJson(Map<String, dynamic> json) => CustomerInfo(
      name: json['name'] as String,
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
    );

Map<String, dynamic> _$CustomerInfoToJson(CustomerInfo instance) =>
    <String, dynamic>{
      'name': instance.name,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
    };

ServiceInfo _$ServiceInfoFromJson(Map<String, dynamic> json) => ServiceInfo(
      serviceName: json['serviceName'] as String,
      serviceCategory:
          $enumDecode(_$ServiceCategoryEnumMap, json['serviceCategory']),
      serviceBreed: $enumDecode(_$ServiceBreedEnumMap, json['serviceBreed']),
      duration: (json['duration'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      currency: $enumDecode(_$CurrencyEnumMap, json['currency']),
    );

Map<String, dynamic> _$ServiceInfoToJson(ServiceInfo instance) =>
    <String, dynamic>{
      'serviceName': instance.serviceName,
      'serviceCategory': _$ServiceCategoryEnumMap[instance.serviceCategory]!,
      'serviceBreed': _$ServiceBreedEnumMap[instance.serviceBreed]!,
      'duration': instance.duration,
      'price': instance.price,
      'currency': _$CurrencyEnumMap[instance.currency]!,
    };

const _$ServiceCategoryEnumMap = {
  ServiceCategory.grooming: 'GROOMING',
  ServiceCategory.veterinary: 'VETERINARY',
  ServiceCategory.daycare: 'DAYCARE',
  ServiceCategory.boarding: 'BOARDING',
  ServiceCategory.training: 'TRAINING',
  ServiceCategory.other: 'OTHER',
};

const _$ServiceBreedEnumMap = {
  ServiceBreed.dog: 'DOG',
  ServiceBreed.cat: 'CAT',
  ServiceBreed.other: 'OTHER',
};

const _$CurrencyEnumMap = {
  Currency.cad: 'CAD',
  Currency.usd: 'USD',
  Currency.eur: 'EUR',
};

StaffInfo _$StaffInfoFromJson(Map<String, dynamic> json) => StaffInfo(
      staffName: json['staffName'] as String,
      staffEmail: json['staffEmail'] as String?,
    );

Map<String, dynamic> _$StaffInfoToJson(StaffInfo instance) => <String, dynamic>{
      'staffName': instance.staffName,
      'staffEmail': instance.staffEmail,
    };

Appointment _$AppointmentFromJson(Map<String, dynamic> json) => Appointment(
      appointmentId: json['appointmentId'] as String,
      storeId: json['storeId'] as String,
      customerId: json['customerId'] as String,
      staffId: json['staffId'] as String,
      serviceId: json['serviceId'] as String,
      status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
      source: $enumDecode(_$AppointmentSourceEnumMap, json['source']),
      timeInfo: AppointmentTimeInfo.fromJson(
          json['timeInfo'] as Map<String, dynamic>),
      customerInfo:
          CustomerInfo.fromJson(json['customerInfo'] as Map<String, dynamic>),
      serviceInfo:
          ServiceInfo.fromJson(json['serviceInfo'] as Map<String, dynamic>),
      staffInfo: StaffInfo.fromJson(json['staffInfo'] as Map<String, dynamic>),
      notes: json['notes'] as String?,
      customerNotes: json['customerNotes'] as String?,
      staffNotes: json['staffNotes'] as String?,
      cancellationReason: json['cancellationReason'] as String?,
      completionNotes: json['completionNotes'] as String?,
      remindersSent: (json['remindersSent'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      createdBy: json['createdBy'] as String,
      confirmedAt: (json['confirmedAt'] as num?)?.toInt(),
      startedAt: (json['startedAt'] as num?)?.toInt(),
      completedAt: (json['completedAt'] as num?)?.toInt(),
      cancelledAt: (json['cancelledAt'] as num?)?.toInt(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$AppointmentToJson(Appointment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'tags': instance.tags,
      'appointmentId': instance.appointmentId,
      'storeId': instance.storeId,
      'customerId': instance.customerId,
      'staffId': instance.staffId,
      'serviceId': instance.serviceId,
      'status': _$AppointmentStatusEnumMap[instance.status]!,
      'source': _$AppointmentSourceEnumMap[instance.source]!,
      'timeInfo': instance.timeInfo,
      'customerInfo': instance.customerInfo,
      'serviceInfo': instance.serviceInfo,
      'staffInfo': instance.staffInfo,
      'notes': instance.notes,
      'customerNotes': instance.customerNotes,
      'staffNotes': instance.staffNotes,
      'cancellationReason': instance.cancellationReason,
      'completionNotes': instance.completionNotes,
      'remindersSent': instance.remindersSent,
      'createdBy': instance.createdBy,
      'confirmedAt': instance.confirmedAt,
      'startedAt': instance.startedAt,
      'completedAt': instance.completedAt,
      'cancelledAt': instance.cancelledAt,
    };

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.draft: 'DRAFT',
  AppointmentStatus.confirmed: 'CONFIRMED',
  AppointmentStatus.inProgress: 'IN_PROGRESS',
  AppointmentStatus.completed: 'COMPLETED',
  AppointmentStatus.cancelled: 'CANCELLED',
  AppointmentStatus.noShow: 'NO_SHOW',
};

const _$AppointmentSourceEnumMap = {
  AppointmentSource.portal: 'PORTAL',
  AppointmentSource.phone: 'PHONE',
  AppointmentSource.walkIn: 'WALK_IN',
  AppointmentSource.admin: 'ADMIN',
};

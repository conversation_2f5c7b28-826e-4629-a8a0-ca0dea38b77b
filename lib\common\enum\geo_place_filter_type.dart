import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum GeoPlaceFilterType {

  outdoor('OUT', 'geo.place.filter.type.outdoor'),
  park('PRK', 'geo.place.filter.type.park'),
  trail('TRL', 'geo.place.filter.type.trail'),
  vet('VET', 'geo.place.filter.type.vet'),
  petStore('GRM', 'geo.place.filter.type.pet.store'),
  dogFriendlyRestaurant('DFR', 'geo.place.filter.type.dog.friendly.restaurant'),
  favor('FAV', 'geo.place.filter.type.favor'), // should not be used as the favor place query
  ;

  final String code; // Stored code in database
  final String t18key; // Translation key
  const GeoPlaceFilterType(this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory GeoPlaceFilterType.fromCode(String code) {
    return GeoPlaceFilterType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

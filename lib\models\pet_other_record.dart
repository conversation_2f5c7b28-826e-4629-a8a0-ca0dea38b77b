import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'pet_other_record.g.dart';

@JsonSerializable()
class PetOtherRecord{
  String sid;
  int? time;
  String? location;
  String? title;
  String? content;
  List<String>? images;
  String? notes;

  PetOtherRecord({
    required this.sid,
    this.time,
    this.location,
    this.title,
    this.content,
    this.images,
    this.notes,
  });

  factory PetOtherRecord.fromJson(Map<String, dynamic> json) =>
      _$PetOtherRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetOtherRecordToJson(this);

  static create({
    int? time,
    String? location,
    String? title,
    String? content,
    List<String>? images,
    String? notes,
  }) {
    return PetOtherRecord(
      sid: uuid.v4(),
      time: time,
      location: location,
      title: title,
      content: content,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetOtherRecord other) {
    return PetOtherRecord(
      sid: other.sid,
      notes: other.notes,
      time: other.time,
      location: other.location,
      title: other.title,
      content: other.content,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

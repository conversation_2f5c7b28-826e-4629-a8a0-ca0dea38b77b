import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';

import '../page_interceptor.dart';
import '../root_page.dart';
import 'user_setting_page.dart';

class UserSettingNamePage extends StatefulWidget {
  const UserSettingNamePage({super.key});

  @override
  UserSettingNamePageState createState() => UserSettingNamePageState();
}

class UserSettingNamePageState extends State<UserSettingNamePage> {
  late Future<UserSettingNamePageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserSettingNamePageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingNamePageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  ProfileWidgetBuilder.buildUserSettingTopSection(
                    theme: controller.theme,
                    onBack: () {
                      Get.back();
                    },
                    topic: "Profile",
                    showAvatar: false,
                  ),
                  _buildAvatar(controller),
                  ProfileWidgetBuilder.buildText(controller.theme, 246,'First name'),
                  ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.firstnameInputController,286),
                  ProfileWidgetBuilder.buildText(controller.theme, 366,'Last name'),
                  ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController,406),
                  ProfileWidgetBuilder.buildText(controller.theme, 486,'Display name'),
                  ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.displaynameInputController,526),
                  ProfileWidgetBuilder.buildChangedNotification(
                    isVisible: controller.isChangedNotificationVisible,
                    theme: controller.theme,
                    top: 606,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                      controller.theme,
                      controller.buttonController,
                    721,
                  ),
                  ProfileWidgetBuilder.buildButton2(
                    controller.theme,
                    controller.alterButtonController,
                    781,
                  ),
                ],
              ),
            );
          }
        });
  }

  Widget _buildAvatar(UserSettingNamePageController controller) {
    return Column(
      children: [
        SizedBox(height: 90.w), // 原来的 top 距离
        Center(
          child: GestureDetector(
            onTap: controller.pickImage,
            child: AuthWidgetBuilder.buildAvatar(
              controller.theme,
              avatar: controller.pageHeaderUserAvatar.value,
              editable: true,
            ),
          ),
        ),
      ],
    );
  }

}

class UserSettingNamePageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final TextEditingController firstnameInputBox = TextEditingController();
  late CommonTextField3Controller firstnameInputController;
  final TextEditingController lastnameInputBox = TextEditingController();
  late CommonTextField3Controller lastnameInputController;
  final TextEditingController displaynameInputBox = TextEditingController();
  late CommonTextField3Controller displaynameInputController;

  // final RxBool isFirstNameEntered = false.obs;
  // final RxBool isLastNameEntered = false.obs;
  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  late RxString displayNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  Future<UserSettingNamePageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    //userAccount.value.fid;
    //userData=await _userService.getUserDataById(userId:userAccount.value?.sid);
    UserData? userData = _authService.userData.value;
    firstNameHint=(userData?.firstName?? 'userFirstName').obs;
    lastNameHint=(userData?.lastName?? 'userLastName').obs;
    displayNameHint=(userData?.name?? 'userDisplayName').obs;

    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
        theme, userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);

    firstnameInputController = ProfileWidgetBuilder.buildFirstNameTextFieldController(
      theme, firstnameInputBox.obs,firstNameHint
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
     );
    lastnameInputController = ProfileWidgetBuilder.buildLastNameTextFieldController(
        theme, lastnameInputBox.obs,lastNameHint
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    displaynameInputController = ProfileWidgetBuilder.buildDisplayNameTextFieldController(
        theme, displaynameInputBox.obs,displayNameHint
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'save.changes'.t18.obs,
      onPressed: saveChanges.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'common.button.skip'.t18.obs,
      onPressed: onCancelPressed.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    return this;
  }

  //void saveChanges() {
  Future<void> saveChanges() async{
    String firstName = firstnameInputBox.text.trim();
    String lastName = lastnameInputBox.text.trim();
    String displayName = displaynameInputBox.text.trim();

    if (firstName.isEmpty && lastName.isEmpty && displayName.isEmpty) {
      return;
    }

    UserData? data = UserData.copyFrom(_authService.userData.value!);

    if (firstName.isNotEmpty) {
      firstNameHint.value = firstName;
      data?.firstName=firstName;
    }
    if (lastName.isNotEmpty) {
      lastNameHint.value = lastName;
      data?.lastName=lastName;
    }
    if (displayName.isNotEmpty) {
      displayNameHint.value = displayName;
      data?.name=displayName;
    }

    await _userService.updateUserData(data!);

    firstnameInputBox.clear();
    lastnameInputBox.clear();
    displaynameInputBox.clear();
    // isLastNameEntered.value = false;
    // isFirstNameEntered.value = false;
    // isDisplayNameEntered.value = false;
    isChangedNotificationVisible.value = true;
    Future.delayed(Duration(seconds: 3), () {
      isChangedNotificationVisible.value = false;
    });

    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const UserProfilePage());
    } else {
      Get.back();
    }
  }

  Future<void> onCancelPressed() async {
    // To support sign-up to here process
    // Get.offAll(UserProfilePage());

    // Skip user address setting
    // await Storage.instance.write(StorageKeys.userDataNameSkipped, true);
    userDataNameSkippedThisTime = true;

    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const UserProfilePage());
    } else {
      Get.back();
    }
  }

  Future<void> pickImage() async {
    // upload image
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      await _userService.uploadUserAvatar(uid: uid, image: image);
    }
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_walking_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetWalkingRecord _$PetWalkingRecordFromJson(Map<String, dynamic> json) =>
    PetWalkingRecord(
      sid: json['sid'] as String,
      startTime: (json['startTime'] as num?)?.toInt(),
      endTime: (json['endTime'] as num?)?.toInt(),
      distanceTags: (json['distanceTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      location: json['location'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PetWalkingRecordToJson(PetWalkingRecord instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'distanceTags': instance.distanceTags,
      'location': instance.location,
      'images': instance.images,
      'notes': instance.notes,
    };

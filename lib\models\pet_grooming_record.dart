import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'pet_grooming_record.g.dart';

@JsonSerializable()
class PetGroomingRecord{
  String sid;
  int? time;
  List<String>? serviceType;
  String? location;
  List<String>? images;
  String? notes;

  PetGroomingRecord({
    required this.sid,
    this.time,
    this.serviceType,
    this.location,
    this.images,
    this.notes,
  });

  factory PetGroomingRecord.fromJson(Map<String, dynamic> json) => _$PetGroomingRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetGroomingRecordToJson(this);

  static create({
    int? time,
    List<String>? serviceType,
    String? location,
    List<String>? images,
    String? notes,
  }) {
    return PetGroomingRecord(
      sid: uuid.v4(),
      time: time,
      serviceType: serviceType,
      location: location,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetGroomingRecord other) {
    return PetGroomingRecord(
      sid: other.sid,
      time: other.time,
      serviceType: other.serviceType,
      location: other.location,
      notes: other.notes,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

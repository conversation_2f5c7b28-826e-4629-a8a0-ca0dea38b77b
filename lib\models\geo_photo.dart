import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

import 'geo_i.dart';

part 'geo_photo.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoPhoto {
  String? name;
  int? widthPx;
  int? heightPx;
  List<GeoAuthorAttribution>? authorAttributions;
  String? flagContentUri;
  String? googleMapsUri;

  GeoPhoto({
    this.name,
    this.widthPx,
    this.heightPx,
    this.authorAttributions,
    this.flagContentUri,
    this.googleMapsUri,
  });

  factory GeoPhoto.fromJson(Map<String, dynamic> json) =>
      _$GeoPhotoFromJson(json);
  Map<String, dynamic> toJson() => _$GeoPhotoToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'authorAttributions') {
        jsonData[key] = (value as List)
            .map((e) => GeoAuthorAttribution.fromApi(e))
            .toList();
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoPhoto.fromApiMap(Map<String, dynamic> apiJson) {
    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoPhoto.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'author_attributions') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] =
            (map['author_attributions'] ?? [])
                .map((e) => GeoAuthorAttribution.fromFirestore(e))
                .toList();
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoPhoto.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoPhoto.fromJson(jsonData);
  }

  factory GeoPhoto.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoPhoto.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'authorAttributions') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] =
            (jsonData['authorAttributions'] ?? [])
                .map((e) => GeoAuthorAttribution.toFirestoreJson(e))
                .toList();
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoPhoto? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create({
    String? name,
    int? widthPx,
    int? heightPx,
    List<GeoAuthorAttribution>? authorAttributions,
    String? flagContentUri,
    String? googleMapsUri,
  }) {
    return GeoPhoto(
      name: name,
      widthPx: widthPx,
      heightPx: heightPx,
      authorAttributions: authorAttributions,
      flagContentUri: flagContentUri,
      googleMapsUri: googleMapsUri,
    );
  }

  static GeoPhoto copyFrom(GeoPhoto other) {
    return GeoPhoto(
      name: other.name,
      widthPx: other.widthPx,
      heightPx: other.heightPx,
      authorAttributions: other.authorAttributions
          ?.map((e) => GeoAuthorAttribution.copyFrom(e))
          .toList(),
      flagContentUri: other.flagContentUri,
      googleMapsUri: other.googleMapsUri,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

import 'package:flutter/material.dart';

/// Colors of theme OneNata Classic
class OneNataClassicColors {

  /// The primary color and swatch of Sunset
  /// for welcome page background, buttons
  /// primary of color scheme
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.sunset[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor sunset = MaterialColor(
    _sunsetPrimaryValue,
    <int, Color>{
      50: Color(0xFFfefbf6),
      100: Color(0xFFfcf6ed),
      200: Color(0xFFfaeddb),
      300: Color(0xFFf7e5c8),
      400: Color(0xFFf5dcb6),
      500: Color(0xFFF2D3A4), // _sunsetPrimaryValue
      600: Color(0xFFc2a983),
      700: Color(0xFF917f62),
      800: Color(0xFF615442),
      900: Color(0xFF302a21),
    },
  );
  static const int _sunsetPrimaryValue = 0xFFF2D3A4;

  /// The primary color and swatch of Sunset Mild
  /// for other page background
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.sunsetMild[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor sunsetMild = MaterialColor(
    _sunsetMildPrimaryValue,
    <int, Color>{
      50: Color(0xFFFFFDFA),
      100: Color(0xFFFFFBF5),
      200: Color(0xFFFEF7EB),
      300: Color(0xFFFEF4E2),
      400: Color(0xFFFDF0D8),
      500: Color(0xFFFDECCE), // _sunsetMildPrimaryValue
      600: Color(0xFFCABDA5),
      700: Color(0xFF988E7C),
      800: Color(0xFF655E52),
      900: Color(0xFF332F29),
    },
  );
  static const int _sunsetMildPrimaryValue = 0xFFFDECCE;

  /// The primary color and swatch of Veronica
  /// for button outlines, buttons, text
  /// second of color scheme
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.veronica[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor veronica = MaterialColor(
    _veronicaPrimaryValue,
    <int, Color>{
      50: Color(0xFFF6E9FF),
      100: Color(0xFFECD4FF),
      200: Color(0xFFD9A8FF),
      300: Color(0xFFC77DFF),
      400: Color(0xFFB451FF),
      500: Color(0xFFA126FF), // _veronicaPrimaryValue
      600: Color(0xFF811ECC),
      700: Color(0xFF611799),
      800: Color(0xFF400F66),
      900: Color(0xFF200833),
    },
  );
  static const int _veronicaPrimaryValue = 0xFFA126FF;

  /// The primary color and swatch of unitedNationBlue
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.unitedNationBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor unitedNationBlue = MaterialColor(
    _unitedNationBluePrimaryValue,
    <int, Color>{
      50: Color(0xFFEBF5FE),
      100: Color(0xFFD7EAFC),
      200: Color(0xFFAFD5F9),
      300: Color(0xFF88C1F6),
      400: Color(0xFF60ACF3),
      500: Color(0xFF3897F0), // _unitedNationBluePrimaryValue
      600: Color(0xFF2D79C0),
      700: Color(0xFF225B90),
      800: Color(0xFF163C60),
      900: Color(0xFF0B1E30),
    },
  );
  static const int _unitedNationBluePrimaryValue = 0xFF3897F0;

  /// The primary color and swatch of yellowGreen
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.yellowGreen[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor yellowGreen = MaterialColor(
    _yellowGreenPrimaryValue,
    <int, Color>{
      50: Color(0xFFF3F9EC),
      100: Color(0xFFE6F3D8),
      200: Color(0xFFCDE7B1),
      300: Color(0xFFB4DC8A),
      400: Color(0xFF9BD063),
      500: Color(0xFF82C43C), // _yellowGreenPrimaryValue
      600: Color(0xFF689D30),
      700: Color(0xFF4E7624),
      800: Color(0xFF344E18),
      900: Color(0xFF1A270C),
    },
  );
  static const int _yellowGreenPrimaryValue = 0xFF82C43C;

  /// The primary color and swatch of imperialRed
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.imperialRed[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor imperialRed = MaterialColor(
    _imperialRedPrimaryValue,
    <int, Color>{
      50: Color(0xFFFDECEC),
      100: Color(0xFFFCD8D8),
      200: Color(0xFFF8B1B1),
      300: Color(0xFFF58B8B),
      400: Color(0xFFF16464),
      500: Color(0xFFEE3D3D), // _imperialRedPrimaryValue
      600: Color(0xFFBE3131),
      700: Color(0xFF8F2525),
      800: Color(0xFF5F1818),
      900: Color(0xFF300C0C),
    },
  );
  static const int _imperialRedPrimaryValue = 0xFFEE3D3D;

  /// The primary color and swatch of crimson
  /// for error, warning, danger
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.crimson[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor crimson = MaterialColor(
    _crimsonPrimaryValue,
    <int, Color>{
      50: Color(0xFFFCE6EB),
      100: Color(0xFFF9CDD7),
      200: Color(0xFFF39AAF),
      300: Color(0xFFEE6887),
      400: Color(0xFFE8355F),
      500: Color(0xFFE20337), // _crimsonPrimaryValue
      600: Color(0xFFB5022C),
      700: Color(0xFF880221),
      800: Color(0xFF5A0116),
      900: Color(0xFF2D010B),
    },
  );
  static const int _crimsonPrimaryValue = 0xFFE20337;

  /// The primary color and swatch of frenchRose
  /// for like button, heart icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.frenchRose[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor frenchRose = MaterialColor(
    _frenchRosePrimaryValue,
    <int, Color>{
      500: Color(0xFFFF5794), // _frenchRosePrimaryValue
    },
  );
  static const int _frenchRosePrimaryValue = 0xFFFF5794;

  /// The primary color and swatch of skyBlue
  /// for pet avatar background
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.skyBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor skyBlue = MaterialColor(
    _skyBluePrimaryValue,
    <int, Color>{
      500: Color(0xFF69D4F8), // _skyBluePrimaryValue
    },
  );
  static const int _skyBluePrimaryValue = 0xFF69D4F8;

  /// The primary color and swatch of spaceCadet
  /// for shadow
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.spaceCadet[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor spaceCadet = MaterialColor(
    _spaceCadetPrimaryValue,
    <int, Color>{
      500: Color(0xFF323247), // _spaceCadetPrimaryValue
    },
  );
  static const int _spaceCadetPrimaryValue = 0xFF323247;

  /// The primary color and swatch of cadetGray
  /// for shadow
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.cadetGray[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor cadetGray = MaterialColor(
    _cadetGrayPrimaryValue,
    <int, Color>{
      500: Color(0xFF8B9EB8), // _cadetGrayPrimaryValue
    },
  );
  static const int _cadetGrayPrimaryValue = 0xFF8B9EB8;

  /// The primary color and swatch of pennBlue
  /// for shadow
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.pennBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor pennBlue = MaterialColor(
    _pennBluePrimaryValue,
    <int, Color>{
      500: Color(0xFF0C1A4B), // _pennBluePrimaryValue
    },
  );
  static const int _pennBluePrimaryValue = 0xFF0C1A4B;

  /// The primary color and swatch of oxfordBlue
  /// for shadow
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.oxfordBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor oxfordBlue = MaterialColor(
    _oxfordBluePrimaryValue,
    <int, Color>{
      500: Color(0xFF162233), // _oxfordBluePrimaryValue
    },
  );
  static const int _oxfordBluePrimaryValue = 0xFF162233;

  /// The primary color and swatch of eerieBlack
  /// for card title, text, arrow icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.eerieBlack[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor eerieBlack = MaterialColor(
    _eerieBlackPrimaryValue,
    <int, Color>{
      500: Color(0xFF262626), // _eerieBlackPrimaryValue
    },
  );
  static const int _eerieBlackPrimaryValue = 0xFF262626;

  /// The primary color and swatch of eerieBlack2
  /// for card title, text, arrow icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.eerieBlack2[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor eerieBlack2 = MaterialColor(
    _eerieBlack2PrimaryValue,
    <int, Color>{
      500: Color(0xFF1E1E1E), // _eerieBlack2PrimaryValue
    },
  );
  static const int _eerieBlack2PrimaryValue = 0xFF1E1E1E;

  /// The primary color and swatch of silver
  /// for icon, title of item tile
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.silver[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor silver = MaterialColor(
    _silverPrimaryValue,
    <int, Color>{
      500: Color(0xFFC6C6C6), // _silverPrimaryValue
    },
  );
  static const int _silverPrimaryValue = 0xFFC6C6C6;

  /// The primary color and swatch of azure
  /// for gender icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.azure[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor azure = MaterialColor(
    _azurePrimaryValue,
    <int, Color>{
      500: Color(0xFF1B85F3), // _azurePrimaryValue
    },
  );
  static const int _azurePrimaryValue = 0xFF1B85F3;

  /// The primary color and swatch of carnationPink
  /// for gender icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.carnationPink[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor carnationPink = MaterialColor(
    _carnationPinkPrimaryValue,
    <int, Color>{
      500: Color(0xFFFF9AD5), // _carnationPinkPrimaryValue
    },
  );
  static const int _carnationPinkPrimaryValue = 0xFFFF9AD5;

  /// The primary color and swatch of white
  /// for background, text, icon
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.white[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor white = MaterialColor(
    _whitePrimaryValue,
    <int, Color>{
      500: Color(0xFFFFFFFF), // _whitePrimaryValue
    },
  );
  static const int _whitePrimaryValue = 0xFFFFFFFF;

  /// The primary color and swatch of white2
  /// for icon, title of item tile
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.white2[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor white2 = MaterialColor(
    _white2PrimaryValue,
    <int, Color>{
      500: Color(0xFFFCFCFC), // _white2PrimaryValue
    },
  );
  static const int _white2PrimaryValue = 0xFFFCFCFC;

  /// The primary color and swatch of gold
  /// for icon, title of item tile
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.gold[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor gold = MaterialColor(
    _goldPrimaryValue,
    <int, Color>{
      500: Color(0xFFDEBB68), // _goldPrimaryValue
    },
  );
  static const int _goldPrimaryValue = 0xFFDEBB68;

  /// The primary color and swatch of jasmine
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.jasmine[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor jasmine = MaterialColor(
    _jasminePrimaryValue,
    <int, Color>{
      500: Color(0xFFF8D976), // _jasminePrimaryValue
    },
  );
  static const int _jasminePrimaryValue = 0xFFF8D976;

  /// The primary color and swatch of atomicTangerine
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.atomicTangerine[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor atomicTangerine = MaterialColor(
    _atomicTangerinePrimaryValue,
    <int, Color>{
      500: Color(0xFFF58F5C), // _atomicTangerinePrimaryValue
    },
  );
  static const int _atomicTangerinePrimaryValue = 0xFFF58F5C;

  /// The primary color and swatch of mediumSlateBlue
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.mediumSlateBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor mediumSlateBlue = MaterialColor(
    _mediumSlateBluePrimaryValue,
    <int, Color>{
      500: Color(0xFF755EEC), // _mediumSlateBluePrimaryValue
    },
  );
  static const int _mediumSlateBluePrimaryValue = 0xFF755EEC;

  /// The primary color and swatch of cyclamen
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.cyclamen[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor cyclamen = MaterialColor(
    _cyclamenPrimaryValue,
    <int, Color>{
      500: Color(0xFFF876A5), // _cyclamenPrimaryValue
    },
  );
  static const int _cyclamenPrimaryValue = 0xFFF876A5;

  /// The primary color and swatch of rosePompadour
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.rosePompadour[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor rosePompadour = MaterialColor(
    _rosePompadourPrimaryValue,
    <int, Color>{
      500: Color(0xFFF87C9A), // _rosePompadourPrimaryValue
    },
  );
  static const int _rosePompadourPrimaryValue = 0xFFF87C9A;

  /// The primary color and swatch of atomicTangerine2
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.atomicTangerine2[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor atomicTangerine2 = MaterialColor(
    _atomicTangerine2PrimaryValue,
    <int, Color>{
      500: Color(0xFFF89C69), // _atomicTangerine2PrimaryValue
    },
  );
  static const int _atomicTangerine2PrimaryValue = 0xFFF89C69;

  /// The primary color and swatch of burntSienna
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.burntSienna[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor burntSienna = MaterialColor(
    _burntSiennaPrimaryValue,
    <int, Color>{
      500: Color(0xFFEC805E), // _burntSiennaPrimaryValue
    },
  );
  static const int _burntSiennaPrimaryValue = 0xFFEC805E;

  /// The primary color and swatch of celticBlue
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.celticBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor celticBlue = MaterialColor(
    _celticBluePrimaryValue,
    <int, Color>{
      500: Color(0xFF3771C8), // _celticBluePrimaryValue
    },
  );
  static const int _celticBluePrimaryValue = 0xFF3771C8;

  /// The primary color and swatch of lightBlue
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.lightBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor lightBlue = MaterialColor(
    _lightBluePrimaryValue,
    <int, Color>{
      500: Color(0xFFBFEDFF), // _lightBluePrimaryValue
    },
  );
  static const int _lightBluePrimaryValue = 0xFFBFEDFF;

  /// The primary color and swatch of lightGreen
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.lightGreen[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor lightGreen = MaterialColor(
    _lightGreenPrimaryValue,
    <int, Color>{
      500: Color(0xFFB1FFA2), // _lightGreenPrimaryValue
    },
  );
  static const int _lightGreenPrimaryValue = 0xFFB1FFA2;

  /// The primary color and swatch of celadon
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.celadon[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor celadon = MaterialColor(
    _celadonPrimaryValue,
    <int, Color>{
      500: Color(0xFFA9D7A2), // _celadonPrimaryValue
    },
  );
  static const int _celadonPrimaryValue = 0xFFA9D7A2;

  /// The primary color and swatch of silver2
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.silver2[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor silver2 = MaterialColor(
    _silver2PrimaryValue,
    <int, Color>{
      500: Color(0xFFC1C1C1), // _silver2PrimaryValue
    },
  );
  static const int _silver2PrimaryValue = 0xFFC1C1C1;

  /// The primary color and swatch of cadetGray2
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.cadetGray2[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor cadetGray2 = MaterialColor(
    _cadetGray2PrimaryValue,
    <int, Color>{
      500: Color(0xFFA0AEC0), // _cadetGray2PrimaryValue
    },
  );
  static const int _cadetGray2PrimaryValue = 0xFFA0AEC0;

  /// The primary color and swatch of payneGray
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.payneGray[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor payneGray = MaterialColor(
    _payneGrayPrimaryValue,
    <int, Color>{
      500: Color(0xFF606873), // _payneGrayPrimaryValue
    },
  );
  static const int _payneGrayPrimaryValue = 0xFF606873;

  /// The primary color and swatch of eerieBlack3
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.eerieBlack3[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor eerieBlack3 = MaterialColor(
    _eerieBlack3PrimaryValue,
    <int, Color>{
      500: Color(0xFF202326), // _eerieBlack3PrimaryValue
    },
  );
  static const int _eerieBlack3PrimaryValue = 0xFF202326;

  /// The primary color and swatch of Cosmic latte
  /// for pet breed ball gradient
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.cosmicLatte[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor cosmicLatte = MaterialColor(
    _cosmicLattePrimaryValue,
    <int, Color>{
      500: Color(0xFFF8F4E1), // _cosmicLattePrimaryValue
    },
  );
  static const int _cosmicLattePrimaryValue = 0xFFF8F4E1;

  /// The primary color and swatch of mikadoYellow
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.mikadoYellow[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor mikadoYellow = MaterialColor(
    _mikadoYellowPrimaryValue,
    <int, Color>{
      50: Color(0xFFFFF9EC),
      100: Color(0xFFFFF3D9),
      200: Color(0xFFFFE8B3),
      300: Color(0xFFFFDC8E),
      400: Color(0xFFFFD168),
      500: Color(0xFFFFC542), // _mikadoYellowPrimaryValue
      600: Color(0xFFCC9E35),
      700: Color(0xFF997628),
      800: Color(0xFF664F1A),
      900: Color(0xFF33270D),
    },
  );
  static const int _mikadoYellowPrimaryValue = 0xFFFFC542;

  /// The primary color and swatch of amethyst
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.amethyst[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor amethyst = MaterialColor(
    _amethystPrimaryValue,
    <int, Color>{
      50: Color(0xFFF6EFFB),
      100: Color(0xFFEDDFF7),
      200: Color(0xFFDBC0EF),
      300: Color(0xFFC8A0E8),
      400: Color(0xFFB681E0),
      500: Color(0xFFA461D8), // _amethystPrimaryValue
      600: Color(0xFF834EAD),
      700: Color(0xFF623A82),
      800: Color(0xFF422756),
      900: Color(0xFF21132B),
    },
  );
  static const int _amethystPrimaryValue = 0xFFA461D8;

  /// The primary color and swatch of majorelleBlue
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.majorelleBlue[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor majorelleBlue = MaterialColor(
    _majorelleBluePrimaryValue,
    <int, Color>{
      50: Color(0xFFF0EDFF),
      100: Color(0xFFE1DAFE),
      200: Color(0xFFC3B5FD),
      300: Color(0xFFA491FD),
      400: Color(0xFF866CFC),
      500: Color(0xFF6847FB), // _majorelleBluePrimaryValue
      600: Color(0xFF5339C9),
      700: Color(0xFF3E2B97),
      800: Color(0xFF2A1C64),
      900: Color(0xFF150E32),
    },
  );
  static const int _majorelleBluePrimaryValue = 0xFF6847FB;

  /// The primary color and swatch of slateGray
  /// for Geo location
  ///
  /// {@tool snippet}
  ///
  /// ```dart
  /// Icon(
  ///   Icons.widgets,
  ///   color: Colors.slateGray[400],
  /// )
  /// ```
  /// {@end-tool}
  ///
  /// See also:
  ///
  ///  * [Theme.of], which allows you to select colors from the current theme
  ///    rather than hard-coding colors in your build methods.
  static const MaterialColor slateGray = MaterialColor(
    _slateGrayPrimaryValue,
    <int, Color>{
      500: Color(0xFF6c8591), // _slateGrayPrimaryValue
    },
  );
  static const int _slateGrayPrimaryValue = 0xFF6c8591;

}

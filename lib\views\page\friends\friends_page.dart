import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/enum/enum_i.dart' as oe;
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/component/profile/profile_widget_builder.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/page/page_interceptor.dart';

class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  FriendsPageState createState() => FriendsPageState();
}

class FriendsPageState extends State<FriendsPage> {

  // Declare your controller here
  late Future<FriendsPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => FriendsPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {

    return FutureBuilder<FriendsPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
              theme: controller.theme,
              // titleBar: AuthWidgetBuilder.buildAuthBackButton(controller.theme, context),
              titleBar: AppBar(
                // TRY THIS: Try changing the color here to a specific color (to
                // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
                // change color while the other colors stay the same.
                backgroundColor: Theme.of(context).colorScheme.inversePrimary,
                // Here we take the value from the MyHomePage object that was created by
                // the App.build method, and use it to set our appbar title.
                title: Text(
                  'Friends page',
                  // 'app.title'.t18,
                  style: controller.theme.themeData.textTheme.labelSmall,
                ),
                leading: AuthWidgetBuilder.buildBackButton(controller.theme, context),
              ),
              body: Stack(
                children: [
                  Center(
                    // Center is a layout widget. It takes a single child and positions it
                    // in the middle of the parent.
                    child: Column(
                      // Column is also a layout widget. It takes a list of children and
                      // arranges them vertically. By default, it sizes itself to fit its
                      // children horizontally, and tries to be as tall as its parent.
                      //
                      // Column has various properties to control how it sizes itself and
                      // how it positions its children. Here we use mainAxisAlignment to
                      // center the children vertically; the main axis here is the vertical
                      // axis because Columns are vertical (the cross axis would be
                      // horizontal).
                      //
                      // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
                      // action in the IDE, or press "p" in the console), to see the
                      // wireframe for each widget.
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          'You have pushed the button this many times:',
                          style: controller.theme.textStyleExtension.petAvatarMore,
                        ),
                        Obx(() => Text(
                          '${controller.counter.value}',
                          style: Theme.of(context).textTheme.headlineMedium,
                        )),
                        AuthWidgetBuilder.buildInputBoxEmail(controller.theme, controller.emailInputBox, controller.emailInputController),
                        SizedBox(height: 10.w,),
                        AuthWidgetBuilder.buildInputBoxPassword(controller.theme, controller.passwordInputBox, controller.passwordInputController),
                        SizedBox(height: 10.w,),

                        // Phone input box
                        SizedBox(
                          height: 60.w,
                          child: controller.buildCoordinateInputBox(
                            controller.theme, controller.coordinateInputController,
                          )
                        ),
                        SizedBox(height: 20.w,),
                        FloatingActionButton(
                          backgroundColor: controller.theme.colorExtension.geoTrail,
                          onPressed: controller.addDeviceStatusRecord,
                          tooltip: 'add pet geo record',
                          child: const Icon(Icons.add),
                        ),
                        FloatingActionButton(
                          backgroundColor: controller.theme.colorExtension.geoPark,
                          onPressed: controller.addDeviceGeoRecord,
                          tooltip: 'add pet geo record',
                          child: const Icon(Icons.add),
                        ),

                ],

                    ),
                  ),

                  Obx(()=>controller.petAvatar.value?? SizedBox.shrink()),

                  FloatingActionButton(
                    backgroundColor: controller.theme.colorExtension.geoFavor,
                    onPressed: controller.testCreateDevice,
                    tooltip: 'Increment',
                    child: const Icon(Icons.add),
                  ),
                  // This trailing comma makes auto-formatting nicer for build methods.
                ],
              ),
              // bottomBar: ,
            );
          }
        });
  }
}

class FriendsPageController extends GetxController {

  final UserDao userDao = UserDao();
  final AuthService _authService = AuthService.instance;
  final UserService userService = UserService();
  final PetService _petService = PetService();
  final CloudStorage cloudStorage = CloudStorage();
  final ConfigService configService = ConfigService();

  // DAOs
  final PetDao petDao = PetDao();
  final DeviceDao deviceDao = DeviceDao();

  // Services
  final PetService petService = PetService();
  final GeoLocationService geoLocationService = GeoLocationService();

  // Theme plugin
  late ThemePlugin theme;

  // Variables
  final RxInt counter = 0.obs;

  // TextEditingController
  final TextEditingController emailInputBox = TextEditingController();
  final CommonTextFieldController emailInputController = Get.put(CommonTextFieldController(), tag: 'email');
  final TextEditingController passwordInputBox = TextEditingController();
  final CommonTextFieldController passwordInputController = Get.put(CommonTextFieldController(), tag: 'password');

  final TextEditingController coordinateInputBox = TextEditingController();
  late CommonTextField3Controller coordinateInputController;
  final _coordinateKey = GlobalKey(debugLabel: 'coordinateKey${DateTime.now().millisecondsSinceEpoch}');
  var petAvatar = Rx<Widget?>(null);

  // Async initialization
  Future<FriendsPageController> init() async {

    // Validate auth state
    PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    // build username input controller
    coordinateInputController = buildCoordinateTextFieldController(
      theme, coordinateInputBox.obs,
      globalKey: _coordinateKey.obs,
      onCleared: onCoordinateCleared.obs,
      onChanged: onCoordinateChanged.obs,
    );
    // Listen to the focus node
    coordinateInputController.textFieldFocusNode.addListener(() {
      coordinateInputController.isFocused.value = coordinateInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_coordinateKey);
    });

    // load pet avatar
    petAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
      theme,
      userId: _authService.userAccount.value!.sid!,
      petId: '2e5cd8b3-1e33-447e-81a7-aa748ddc9ff3',
      avatar: _authService.defaultPet.value?.avatar ?? '03a7dd46-c49f-459d-ba94-9c4e36908ab2.png'
    );

    return this;
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && (key == _coordinateKey && coordinateInputController.textFieldFocusNode.hasFocus)) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  Future<void> testDataAccess() async {
    counter.value += 1;

    // test sign up
    String email = '<EMAIL>';
    String password = "*********";

    // ServiceResponse<User> response = await userService.signUpWithEmailAndPassword(email, password);

    // ServiceResponse<User> response = await userService.loginInWithEmailAndPassword(email, password);
    ServiceResponse<User> response = await _authService.getCurrentSignUpUser();
    if (response.code != 200) {
      // ServiceResponse<User> res1 = await userService.loginInWithEmailAndPassword(email, password);
      ServiceResponse<User> res1 = await _authService.signUpWithEmailAndPassword(email, password);
    } else {
      ServiceResponse<User> res2 = await _authService.loginInWithEmailAndPassword(email, password);
    }

    ServiceResponse<User> res2 = await _authService.getCurrentSignUpUser();

    UserAccount acc = UserAccount.create(
      fid: res2.data!.uid,
      email: email,
      salt: 'salt',
      hashedCredential: '*********',
    );

    UserData data = UserData.create(
      uid: 'b6e4c05d-f066-4590-924f-55ffb1df8bf0',
      avatar: 'avatar',
    );

    UserAuthRecord record = UserAuthRecord.create(
      uid: 'b6e4c05d-f066-4590-924f-55ffb1df8bf0',
      authChannel: oe.AuthChannel.emailPassword,
    );

    // Test Firestore ----------------------------------------------------------
    await userDao.addUserAccount(account: acc);
    // await userDao.addUserAccount(account: acc, data: data);
    // await userService.createUser(account: acc, data: data);
    // await userService.addUserData(data);
    // await userService.addUserAuthRecord(record);
    // await userDao.updateUserAccount('b6e4c05d-f066-4590-924f-55ffb1df8bf0', acc);
    // await userDao.softDeleteUserAccount('b6e4c05d-f066-4590-924f-55ffb1df8bf0');

    // Test Firebase Storage ---------------------------------------------------

    // download image
    String filePath = 'users/eGJtzlA7RqDNG9rRIEmPt0hjuGFA/posts/*********xyz/media/Screenshot 2025-03-11 at 10.50.35 PM.png';
    String url = await cloudStorage.getDownloadURL(filePath);
    Get.dialog(
      Image.network(
        url,
        // fit: BoxFit.cover,
      ),
    );

    // For debug
    int i = 100;
  }

  Future<void> testInputBox() async {

    // String displayText = inputBox.text;

    // String text = InputUtil.toOriginPhoneNumber(phoneNumber: displayText);
    // String text = InputUtil.toOriginPhoneNumber(phoneNumber: displayText, areaCode: SupportedAreaCode.cn.code);

    // textFieldController.toggleMistakeState();

    emailInputController.toggleEnabled();
    emailInputBox.text = 'test';

    int code = 200;
  }

  Future<void> testImagePicker() async {

    // upload image
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      await userService.uploadUserAvatar(uid: uid, image: image);
    }

    int code = 200;
  }

  Future<void> testAddPet() async {

    Pet pet = Pet.create(
      owner: _authService.userAccount.value!.sid!,
      name: 'Kitty',
      type: oe.PetType.cat,
      isLive: true,
    );

    await _petService.addOwnedPet(pet);

    int code = 200;
  }

  Future<void> testUpdatePetAvatar() async {

    // upload image
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      String pid = '2e5cd8b3-1e33-447e-81a7-aa748ddc9ff3';

      ServiceResponse<String> res = await _petService.uploadPetAvatar(uid: uid, pid: pid, image: image);
      if (res.code == 200) {
        petAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
          theme,
          userId: _authService.userAccount.value!.sid!,
          petId: pid,
          avatar: res.data
        );
      }
    }

    int code = 200;
  }

  Future<void> testDeletePet() async {

    String pid = '65b6fa84-09ee-4508-8339-4230be802bd3';
    await _petService.deleteOwnedPet(pid);
    int code = 200;
  }

  Future<void> testRecordPetGrowing() async {

    String uid = '642df874-f6bd-47a8-8d29-d89615244579';
    String pid = '42a654ad-b072-4192-b3b4-857e46c2379d';

    PetGrowingRecord r = PetGrowingRecord.create(
      uid: uid,
      pid: pid,
      weight: 8.2,
    );

    await _petService.recordPetGrowing(r);
    int code = 200;
  }

  Future<void> testDeletePetGrowingRecord() async {

    String pid = 'pid';
    String rid = 'de22b8c6-4c0e-4db3-8610-0e0ce03092a8';

    ServiceResponse<String> res = await _petService.deletePetGrowingRecord(pid, rid);
    if (res.code == 200) {
      logger.d('Pet growing record deleted successfully');
    } else {
      logger.e('Failed to delete pet growing record: ${res.msg}');
    }
    int code = 200;
  }

  Future<void> testGetLatestPetGrowingRecord() async {

    String pid = '42a654ad-b072-4192-b3b4-857e46c2379d';

    PetGrowingRecord? r = await _petService.getLatestPetGrowingRecordById(pid);
    if (r != null) {
      logger.d('Latest pet growing record: ${r.toString()}');
    } else {
      logger.d('No pet growing record found');
    }

    int code = 200;
  }

  Future<void> testVerifyBeforeUpdateEmail() async {

    ServiceResponse<String> res = await _authService.verifyBeforeUpdateEmail("<EMAIL>");

    int code = res.code;
  }

  Future<void> testVerifyBeforeUpdateEmailAddress() async {

    ServiceResponse<String> res = await _authService.verifyBeforeUpdateEmail("<EMAIL>");

    int code = res.code;
  }

  Future<void> redirect() async {
    Get.to(()=> const LinkEmailPage());
  }

  Future<void> signOut() async {
    await _authService.signOut();
    Get.offAll(()=> const LogInPage());
  }

  Future<void> disableUserAddress() async {
    // Skip user address setting
    await configService.clearGlobalConfig();

  }

  // ----- Device testing -----
  Future<void> testCreateDevice() async {

    String uid = '9768b3c2-b736-45c7-9109-056c82a367aa';

    Device locator = Device.create(
        deviceType: oe.DeviceType.locator,
        deviceModel: oe.DeviceModel.locator,
        manufacturer: 'locator producer',
        manufacturerSerialNumber: 'L01_TEST_2',
        uid: uid,
    );

    deviceDao.addDevice(locator);

    Device feedingMachine = Device.create(
        deviceType: oe.DeviceType.feedingMachine,
        deviceModel: oe.DeviceModel.piFI,
        manufacturer: 'feeding machine producer',
        manufacturerSerialNumber: 'P01_TEST_2',
      uid: uid,
    );

    deviceDao.addDevice(feedingMachine);

  }

  Widget buildCoordinateInputBox(ThemePlugin theme, CommonTextField3Controller controller) {

    logger.d('Object ID: ${identityHashCode(controller)}');

    return CommonTextField3(
      controller: controller,
    );
  }
  CommonTextField3Controller buildCoordinateTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      {Rx<GlobalKey>? globalKey, Rx<AsyncCallbackWithContext?>? onCleared, Rx<AsyncCallbackWithContextAndResult?>? onChanged}){

    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.emailAddress.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: 'coordinate'.obs,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,

    );
  }

  Future<void> onCoordinateCleared(BuildContext context) async {
  }

  Future<void> onCoordinateChanged({required BuildContext context, required bool result}) async {

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> addDeviceStatusRecord() async {

    String deviceId = '27277d39-8cdf-4386-8e8f-12b73e546989';
    String pid = '789bb4c5-56ee-4270-9bb3-1e35f0ac261b';
    // String coordinate = coordinateInputBox.text;

    // List<String> parts = coordinate.trim().split(',');
    //
    // double latitude = double.parse(parts[0]);
    // double longitude = double.parse(parts[1]);

    DeviceStatusMessage msg = DeviceStatusMessage.create(
      deviceId: deviceId,
      pid: pid,
      generateDate: 1747228825000,
      sendDate: 1747228826000,
      receiveDate: 1747228827000,
      battery: 70,
      standbyTime: 21600,
    );

    await deviceDao.addDeviceStatusMessage(msg);

  }

  Future<void> addDeviceGeoRecord() async {

    String deviceId = '27277d39-8cdf-4386-8e8f-12b73e546989';
    String pid = '789bb4c5-56ee-4270-9bb3-1e35f0ac261b';
    String coordinate = coordinateInputBox.text;
    if (coordinate.isEmpty) return;

    List<String> parts = coordinate.trim().split(',');
    double latitude = double.parse(parts[0]);
    double longitude = double.parse(parts[1]);

    DeviceGeoLocationMessage msg = DeviceGeoLocationMessage.create(
      deviceId: deviceId,
      pid: pid,
      generateDate: 1747228840000,
      sendDate: 1747228841000,
      receiveDate: 1747228842000,
      location: GeoFirePoint(GeoPoint(latitude, longitude)),
    );

    await deviceDao.addDeviceGeoLocationMessage(msg);

    int code = 200;

  }


}

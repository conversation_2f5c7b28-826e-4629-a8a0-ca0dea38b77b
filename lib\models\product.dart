import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'product.g.dart';

/// 商品状态枚举
enum ProductStatus {
  @JsonValue('Active')
  active,
  @JsonValue('deactived')
  deactived,
}

@JsonSerializable()
class Product extends BaseFullModel {
  
  /// 商品名称
  String productName;
  
  /// 价格
  double price;
  
  /// 货币单位
  String currency;
  
  /// 商品描述
  String description;
  
  /// 是否为易碎物品
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? fragileItems;
  
  /// SKU编码
  String? sku;
  
  /// 品牌
  String? brand;
  
  /// 商品属性 (颜色、尺寸、重量、材质等)
  Map<String, dynamic>? attributes;
  
  /// 商品状态
  ProductStatus status;
  
  /// 商品图片URL列表
  List<String>? photoURL;

  Product({
    required this.productName,
    required this.price,
    required this.currency,
    required this.description,
    required this.status,
    this.fragileItems,
    this.sku,
    this.brand,
    this.attributes,
    this.photoURL,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
  
  @override
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  static Product copyFrom(Product other) {
    return Product(
      productName: other.productName,
      price: other.price,
      currency: other.currency,
      description: other.description,
      fragileItems: other.fragileItems,
      status: other.status,
      sku: other.sku,
      brand: other.brand,
      attributes: other.attributes != null ? Map<String, dynamic>.from(other.attributes!) : null,
      photoURL: other.photoURL != null ? List<String>.from(other.photoURL!) : null,
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags != null ? List<String>.from(other.tags!) : null,
    );
  }

  /// 获取主图片URL
  String? get primaryImageUrl => photoURL?.isNotEmpty == true ? photoURL!.first : null;
  
  /// 获取格式化价格字符串
  String get formattedPrice => '$currency \$${price.toStringAsFixed(2)}';
  
  /// 检查商品是否可用
  bool get isActive => status == ProductStatus.active;
  
  /// 获取属性值
  String? getAttribute(String key) => attributes?[key]?.toString();
  
  /// 设置属性值
  void setAttribute(String key, dynamic value) {
    attributes ??= {};
    attributes![key] = value;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

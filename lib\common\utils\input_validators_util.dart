class InputUtil {

  static String phoneNumberArea1Digit = r'^\d{1,10}$';
  static String phoneNumberArea1Format = r'^\(\d{3}\) \d{3}-\d{4}$';
  static String phoneNumberArea86Digit = r'^\d{1,11}$';
  static String phoneNumberArea86Format = r'^\d{3} \d{4}-\d{4}$';
  static String validVerificationCodeRegEx = r'^\d{1,10}$';
  static String validEmailAddressRegEx = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$";
  static String validPasswordRegEx = r"^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)(?=.*?[#?!@$%^&*-]).{8,}$";

  static String toDisplayPhoneNumber({required String phoneNumber, String? areaCode = '+1'}) {

    if (areaCode == '+1') {
      return '(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6, 10)}';
    } else {
      return '${phoneNumber.substring(0, 3)} ${phoneNumber.substring(3, 7)} ${phoneNumber.substring(7, 11)}';
    }
  }

  static String toOriginPhoneNumber({required String phoneNumber, String? areaCode = '+1'}) {

    if (areaCode == '+1') {
      return phoneNumber.replaceAll(RegExp(r'\D'), '');
    } else {
      return phoneNumber.replaceAll(' ', '');
    }
  }


}
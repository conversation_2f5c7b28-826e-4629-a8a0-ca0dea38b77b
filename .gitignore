# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
*.lock

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/app/.cxx

# ios build folder
Podfile

# ENV files
.env
.env.*
!.env.sample

# Config files
#Info.plist - should be ignored when containing sensitive information
google-services.json
google-services.*.json
GoogleService-Info.plist
GoogleService-Info.*.plist
firebase.json
firebase.*.json
firebase_options.dart
firebase_options.*.dart

# Google maps
secrets.properties

// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class UserInformationPage extends StatelessWidget {
//   const UserInformationPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     double widthRatio = MediaQuery.of(context).size.width / 403;
//     double heightRatio = MediaQuery.of(context).size.height / 861;
//
//     return Scaffold(
//       backgroundColor: const Color(0xFFFCFCFC),
//       body: Stack(
//         children: [
//           _buildTopSection(widthRatio, heightRatio),
//           _buildFormSection(widthRatio, heightRatio),
//           _buildBottomSection(widthRatio, heightRatio),
//         ],
//       ),
//     );
//   }
//
//   /// **🟢 顶部导航栏**
//   Widget _buildTopSection(double w, double h) {
//     return Positioned(
//       width: 327 * w,
//       height: 40 * h,
//       top: 50 * h,
//       left: 24 * w,
//       child: Container(
//         //padding: EdgeInsets.symmetric(vertical: 12 * h, horizontal: 24 * w),
//         child: Stack(
//           alignment: Alignment.center,
//           children: [
//             // **返回按钮 + Account 文字**
//             Row(
//               children: [
//                 // **返回按钮**
//                 IconButton(
//                   onPressed: () {
//                     // ✅ **返回逻辑**
//                   },
//                   icon: const Icon(Icons.arrow_back, color: Colors.black),
//                 ),
//                 SizedBox(width: 8 * w),
//                 Container(
//                   width: 1, // ✅ `border-width: 1px`
//                   height: 22 * h, // ✅ `height: 22`
//                   color: const Color(0xFFECEFF2), // ✅ `color: #ECEFF2`
//                 ),
//                 SizedBox(width: 8 * w),
//                 const Text(
//                   "Account",
//                   style: TextStyle(
//                     fontFamily: 'Manrope',
//                     fontWeight: FontWeight.w500,
//                     fontSize: 16,
//                     color: Color(0xFF262626), // ✅ `color: #262626`
//                   ),
//                 ),
//               ],
//             ),
//             Positioned(
//               bottom: 0,
//               child: Container(
//                 width: 327 * w, // ✅ `width: 327`
//                 height: 1, // ✅ `border-width: 1px`
//                 decoration: const BoxDecoration(
//                   gradient: LinearGradient(
//                     colors: [
//                       Color(0x4D8A8A8A), // ✅ `8A8A8A` 30% 透明度
//                       Color(0x1A8A8A8A), // ✅ `8A8A8A` 10% 透明度
//                     ],
//                     begin: Alignment.centerLeft,
//                     end: Alignment.centerRight,
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//
//   /// **🟢 中间表单部分（可滑动）**
//   Widget _buildFormSection(double w, double h) {
//     return Positioned(
//       top: 124 * h,
//       left: 0,
//       right: 0,
//       bottom: 169 * h, // 留出底部按钮区域
//       child: SingleChildScrollView(
//         child: Padding(
//           padding: EdgeInsets.symmetric(horizontal: 38 * w),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // 头像区域
//               Center(
//                 child: Container(
//                   width: 125 * w,
//                   height: 125 * h,
//                   decoration: BoxDecoration(
//                     shape: BoxShape.circle,
//                     border: Border.all(color: const Color(0xFFF2D3A4), width: 2),
//                   ),
//                   child: ClipOval(
//                     child: Image.asset(
//                       "assets/images/user_avatar.png",
//                       fit: BoxFit.cover,
//                     ),
//                   ),
//                 ),
//               ),
//               SizedBox(height: 24 * h),
//
//               // Name Section
//               _buildSectionTitle("Name"),
//               _buildInputField("First name"),
//               _buildInputField("Last name"),
//               SizedBox(height: 20 * h),
//
//               // Contact Info
//               _buildSectionTitle("Contact info"),
//               _buildInputField("Email"),
//               _buildInputField("Phone number"),
//               SizedBox(height: 20 * h),
//
//               // Place of Living
//               _buildSectionTitle("Place of living"),
//               _buildInputField("City"),
//               _buildInputField("Country"),
//               SizedBox(height: 30 * h),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   /// **🟢 表单标题**
//   Widget _buildSectionTitle(String title) {
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 8),
//       child: Text(
//         title,
//         style: const TextStyle(
//           fontFamily: 'Manrope',
//           fontWeight: FontWeight.w700,
//           fontSize: 16,
//           color: Color(0xFF262626),
//         ),
//       ),
//     );
//   }
//
//   /// **🟢 输入框**
//   Widget _buildInputField(String hint) {
//     return Container(
//       width: double.infinity,
//       height: 60,
//       margin: const EdgeInsets.only(bottom: 12),
//       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 15),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8),
//         border: Border.all(color: const Color(0xFFC6C6C6)),
//       ),
//       child: TextField(
//         decoration: InputDecoration(
//           hintText: hint,
//           border: InputBorder.none,
//           hintStyle: const TextStyle(
//             fontFamily: 'Manrope',
//             fontWeight: FontWeight.w400,
//             fontSize: 16,
//             color: Color(0xFFC6C6C6),
//           ),
//         ),
//       ),
//     );
//   }
//
//   /// **🟢 底部按钮区域**
//   Widget _buildBottomSection(double w, double h) {
//     return Positioned(
//       width: 403 * w,
//       height: 211 * h,
//       top: 697 * h,
//       child: Container(
//         width: 403 * w,
//         height: 211 * h,
//         decoration: BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.circular(26),
//           boxShadow: [
//             BoxShadow(
//               color: const Color.fromRGBO(73, 77, 90, 0.12), // ✅ 12% 透明度阴影
//               offset: const Offset(0, -3),
//               blurRadius: 4, // ✅ 调整阴影柔和度
//             ),
//           ],
//         ),
//         child: Stack(
//           children: [
//             // **"Save Change" 按钮**
//             Positioned(
//               top: 24 * h, // ✅ 确保按钮不会超出
//               left: 38 * w,
//               child: Container(
//                 width: 327 * w,
//                 height: 60 * h,
//                 decoration: BoxDecoration(
//                   color: const Color(0xFFA126FF), // 按钮背景紫色
//                   borderRadius: BorderRadius.circular(30.5),
//                 ),
//                 child: Center(
//                   child: TextButton(
//                     onPressed: () {
//                       // ✅ 按钮点击事件
//                     },
//                     child: const Text(
//                       "Save change",
//                       style: TextStyle(
//                         fontFamily: 'Manrope',
//                         fontWeight: FontWeight.w700,
//                         fontSize: 16,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//
//             // **"Cancel" 文字按钮**
//             Positioned(
//               top: 94 * h, // ✅ 确保 "Cancel" 文字在正确位置
//               left: 174 * w, // ✅ 居中
//               child: TextButton(
//                 onPressed: () {
//                   // ✅ 取消操作
//                 },
//                 child: const Text(
//                   "Cancel",
//                   style: TextStyle(
//                     fontFamily: 'Manrope',
//                     fontWeight: FontWeight.w700,
//                     fontSize: 16,
//                     height: 1.5,
//                     color: Color(0xFFA126FF),
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }

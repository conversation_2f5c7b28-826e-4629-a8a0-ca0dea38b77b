import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class StoreDetailPage extends StatelessWidget {
  const StoreDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Get.back(),
        ),
        title: Text(
          'Port Moody Pet Store',
          style: TextStyle(
            fontFamily: 'Manrope',
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 16.w),
            child: CircleAvatar(
              radius: 20.w,
              backgroundImage: AssetImage('assets/images/store_avatar.png'), // 占位图片
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cat food section
            _buildCategorySection(
              title: 'Cat food',
              products: [
                _ProductData(
                  name: 'RC Persian',
                  price: '\$22.99',
                  image: 'assets/images/cat_food_1.png',
                ),
                _ProductData(
                  name: 'RC Kitten',
                  price: '\$20.99',
                  image: 'assets/images/cat_food_2.png',
                ),
                _ProductData(
                  name: 'RC Kitten',
                  price: '\$20.99',
                  image: 'assets/images/cat_food_3.png',
                ),
              ],
            ),
            
            SizedBox(height: 32.w),
            
            // Dog food section
            _buildCategorySection(
              title: 'Dog food',
              products: [
                _ProductData(
                  name: 'RC Pomeranian',
                  price: '\$32.99',
                  image: 'assets/images/dog_food_1.png',
                ),
                _ProductData(
                  name: 'RC Small Dog',
                  price: '\$30.99',
                  image: 'assets/images/dog_food_2.png',
                ),
                _ProductData(
                  name: 'RC Puppy',
                  price: '\$30.99',
                  image: 'assets/images/dog_food_3.png',
                ),
              ],
            ),
            
            SizedBox(height: 32.w),
            
            // Dog Toy section
            _buildCategorySection(
              title: 'Dog Toy',
              products: [
                _ProductData(
                  name: 'Kong',
                  price: '\$12.99',
                  image: 'assets/images/dog_toy_1.png',
                ),
                _ProductData(
                  name: 'Puppy toy',
                  price: '\$15.99',
                  image: 'assets/images/dog_toy_2.png',
                ),
                _ProductData(
                  name: 'Rope toy',
                  price: '\$20.99',
                  image: 'assets/images/dog_toy_3.png',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection({
    required String title,
    required List<_ProductData> products,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
            Text(
              'All',
              style: TextStyle(
                fontFamily: 'Manrope',
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: Color(0xFFF2D3A4),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.w),
        
        // Products grid
        SizedBox(
          height: 200.w,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: products.length,
            separatorBuilder: (context, index) => SizedBox(width: 16.w),
            itemBuilder: (context, index) {
              final product = products[index];
              return _buildProductCard(product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductCard(_ProductData product) {
    return Container(
      width: 140.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8.w,
            offset: Offset(0, 2.w),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image
          Container(
            height: 120.w,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Color(0xFFF8F8F8),
              borderRadius: BorderRadius.vertical(top: Radius.circular(12.w)),
            ),
            child: Center(
              child: Container(
                width: 80.w,
                height: 80.w,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8.w),
                ),
                child: Icon(
                  Icons.pets,
                  color: Colors.grey[600],
                  size: 40.w,
                ),
              ),
            ),
          ),
          
          // Product info
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(12.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    product.name,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF262626),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        product.price,
                        style: TextStyle(
                          fontFamily: 'Manrope',
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF262626),
                        ),
                      ),
                      Container(
                        width: 32.w,
                        height: 32.w,
                        decoration: BoxDecoration(
                          color: Color(0xFFF2D3A4),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 18.w,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 商品数据模型
class _ProductData {
  final String name;
  final String price;
  final String image;

  _ProductData({
    required this.name,
    required this.price,
    required this.image,
  });
}

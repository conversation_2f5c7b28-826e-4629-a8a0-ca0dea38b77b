// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_feeding_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeviceFeedingMessage _$DeviceFeedingMessageFromJson(
        Map<String, dynamic> json) =>
    DeviceFeedingMessage(
      sid: json['sid'] as String?,
      pid: json['pid'] as String?,
      deviceId: json['deviceId'] as String,
      generateDate: (json['generateDate'] as num?)?.toInt(),
      sendDate: (json['sendDate'] as num?)?.toInt(),
      receiveDate: (json['receiveDate'] as num?)?.toInt(),
      foodTypes: (json['foodTypes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      amount: json['amount'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$DeviceFeedingMessageToJson(
        DeviceFeedingMessage instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'pid': instance.pid,
      'deviceId': instance.deviceId,
      'generateDate': instance.generateDate,
      'sendDate': instance.sendDate,
      'receiveDate': instance.receiveDate,
      'foodTypes': instance.foodTypes,
      'amount': instance.amount,
      'images': instance.images,
      'notes': instance.notes,
    };

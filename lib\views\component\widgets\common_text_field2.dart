import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/input_validators_util.dart';
import 'package:path/path.dart';
import 'common_bar.dart';

class CommonTextField2 extends StatelessWidget {

  final CommonTextFieldController controller;
  final TextEditingController textEditingController;
  final FocusNode textFieldFocusNode = FocusNode();
  final String? areaCode;
  final bool? hasMistake;

  // Common properties
  final double width;
  final double height;
  final bool? hasRadius;
  final double? circular;

  // Bg properties
  final bool? hasShadow;
  final Color? shadowColor;
  final Offset? shadowOffset;
  final double? shadowBlurRadius;
  final double? shadowSpreadRadius;

  // TextField properties
  final String? hintText;
  final List<TextInputFormatter>? inputFormatters;
  final double? borderWidth;
  final bool? showCursor;
  final bool? isFilled;
  final Color? fillColor;
  final double? fillColorOpacity;
  final double textPaddingHorizontal;
  final double textPaddingVertical;

  // Icon properties
  final double? clearIconSize;
  final double? passwordVisibilityIconSize;

  // Rx properties
  final bool? isEnabled;
  final bool? obscureText;
  final TextInputType? keyboardType;
  final bool? enableClearIcon;

  final Color? iconColor;
  final Color? borderColor;
  final Color? focusBorderColor;
  final Color? mistakeBorderColor;
  final Color? disabledBorderColor;
  final TextStyle? hintStyle;
  final TextStyle? textStyle;
  final TextStyle? mistakeTextStyle;
  final TextStyle? disabledTextStyle;

  // Callbacks
  final AsyncCallbackWithContext? onCleared;
  final AsyncCallbackWithContextAndResult? onChanged;
  final VoidCallback? onTogglePasswordVisibility;

  CommonTextField2({

    super.key,
    required this.textEditingController,
    required this.controller,
    this.areaCode = '+1',
    this.hasMistake = false,

    required this.width,
    required this.height,
    this.hasRadius = false,
    this.circular = 10,

    this.hasShadow = false,
    this.shadowColor,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.shadowSpreadRadius,

    this.hintText,
    this.inputFormatters,
    this.borderWidth = 1,
    this.showCursor = true,
    this.isFilled = true,
    this.fillColor = Colors.white,
    this.fillColorOpacity = 0,
    this.textPaddingHorizontal = 10,
    this.textPaddingVertical = 10,

    this.enableClearIcon = false,
    this.clearIconSize = 12,
    this.passwordVisibilityIconSize = 24,

    this.isEnabled = true,
    this.obscureText = true,
    this.iconColor = Colors.black38,
    this.borderColor = Colors.black38,
    this.focusBorderColor = Colors.purple,
    this.mistakeBorderColor = Colors.red,
    this.disabledBorderColor = Colors.black38,
    this.hintStyle,
    this.textStyle,
    this.mistakeTextStyle,
    this.disabledTextStyle,
    this.keyboardType = TextInputType.text,

    this.onCleared,
    this.onChanged,
    this.onTogglePasswordVisibility,
  }) ;

  @override
  Widget build(BuildContext context) {

    // Pass rx properties to controller
    controller.textEditingController = textEditingController;
    controller.keyboardType.value = keyboardType!;
    controller.areaCode = areaCode;
    controller.hintText.value = hintText!;

    controller.isEnabled.value = isEnabled!;
    controller.obscureText.value = obscureText!;
    controller.hasMistake.value = hasMistake!;

    controller.iconColor.value = iconColor!;
    controller.borderColor.value = borderColor!;
    controller.focusBorderColor.value = focusBorderColor!;
    controller.mistakeBorderColor.value = mistakeBorderColor!;
    controller.disabledBorderColor.value = disabledBorderColor!;
    controller.enablePasswordVisibilityIcon.value = keyboardType == TextInputType.visiblePassword;

    controller.hintStyle.value = hintStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.black12,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );
    controller.textStyle.value = textStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.purple,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );
    controller.mistakeTextStyle.value = mistakeTextStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.red,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );
    controller.disabledTextStyle.value = mistakeTextStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.black12,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );

    controller.onCleared = onCleared;
    controller.onChanged = onChanged;
    controller.onTogglePasswordVisibility = onTogglePasswordVisibility;

    Widget? bg = hasShadow == false ? null : CommonBar(
      width: width,
      height: height,
      circular: circular,
      hasShadow: true,
      shadowColor: shadowColor,
      shadowOffset: shadowOffset,
      shadowBlurRadius: shadowBlurRadius,
      shadowSpreadRadius: shadowSpreadRadius,
    );

    Widget textField = SizedBox(
      width: width,
      height: height,
      child: Obx(()=>
        TextField(
          controller: textEditingController,
          focusNode: controller.textFieldFocusNode,
          keyboardType: controller.keyboardType.value,
          obscureText: controller.keyboardType.value == TextInputType.visiblePassword ? controller.obscureText.value : false,
          enabled: true,
          readOnly: !controller.isEnabled.value,
          inputFormatters: controller.inputFormatters.value,
          onChanged: (value)=> controller.onTextChanged(value, context),
          style: controller.isEnabled.isTrue ? (controller.hasMistake.isTrue ? controller.mistakeTextStyle.value : controller.textStyle.value) : controller.disabledTextStyle.value,
          showCursor: showCursor,
          decoration: InputDecoration(
            hintText: controller.hintText.value,
            hintStyle: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value : controller.hintStyle.value,
            fillColor: fillColor,
            filled: isFilled,
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Clear icon
                if (controller.isFocused.value && enableClearIcon!)
                  GestureDetector(
                    onTap: () {
                      textEditingController.clear();
                      if (onCleared != null) onCleared!(context);
                    },
                    child: Icon(Icons.close, size: passwordVisibilityIconSize, color: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value.color! : controller.isFocused.value ? controller.textStyle.value.color : controller.iconColor.value),
                  ),
                if (controller.isFocused.value && controller.enablePasswordVisibilityIcon.value) SizedBox(width: passwordVisibilityIconSize! / 4), // ✅ 只有密码时才加间距
                // Password visibility icon
                if (controller.isFocused.value && controller.enablePasswordVisibilityIcon.value) // ✅ 只有在有输入时，才显示眼睛 Icon
                  GestureDetector(
                    onTap: controller.togglePasswordVisibility,
                    child: Icon(
                      controller.obscureText.value
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      size: passwordVisibilityIconSize,
                      color: controller.hasMistake.isTrue ? controller.mistakeTextStyle.value.color! : controller.isFocused.value ? controller.textStyle.value.color : controller.iconColor.value,
                    ),
                  ),
                if (controller.isFocused.value && enableClearIcon!) SizedBox(width: passwordVisibilityIconSize! * 2 / 3,)
              ],
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: hasRadius! ? BorderRadius.circular(circular!) : BorderRadius.zero,
              borderSide: BorderSide(
                color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.borderColor.value,
                width: borderWidth!,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(circular!),
              borderSide: BorderSide(
                color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.focusBorderColor.value,
                width: borderWidth!,
              ),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(circular!),
              borderSide: BorderSide(
                color: controller.hasMistake.isTrue ? controller.mistakeBorderColor.value : controller.disabledBorderColor.value,
                width: borderWidth!,
              ),
            ),
            contentPadding: EdgeInsets.symmetric(
                horizontal: textPaddingHorizontal,
                vertical: textPaddingVertical
            ),
          ),
        ),
      ),
    );

    return Stack(
      alignment: Alignment.center,
      children: [
        bg?? SizedBox.shrink(),
        textField,
      ],
    );
  }
}

class CommonTextFieldController extends GetxController {

  final FocusNode textFieldFocusNode = FocusNode();
  late TextEditingController textEditingController;
  Rx<TextInputType> keyboardType = TextInputType.text.obs;
  late String? areaCode;

  var isEnabled = true.obs;
  var isFocused = false.obs;
  var enablePasswordVisibilityIcon = false.obs;
  var obscureText = false.obs;
  var hasMistake = false.obs;

  Rx<Color> iconColor = Colors.black38.obs;
  Rx<Color> borderColor = Colors.black38.obs;
  Rx<Color> focusBorderColor = Colors.black38.obs;
  Rx<Color> mistakeBorderColor = Colors.red.obs;
  Rx<Color> disabledBorderColor = Colors.black38.obs;
  var hintText = ''.obs;

  Rx<TextStyle> hintStyle = TextStyle().obs;
  Rx<TextStyle> textStyle = TextStyle().obs;
  Rx<TextStyle> mistakeTextStyle = TextStyle().obs;
  Rx<TextStyle> disabledTextStyle = TextStyle().obs;
  var inputFormatters = Rx<List<TextInputFormatter>?>(null);

  // Callbacks
  late VoidCallback? onTogglePasswordVisibility;
  late AsyncCallbackWithContext? onCleared;
  late AsyncCallbackWithContextAndResult? onChanged;

  @override
  void onInit() {
    super.onInit();

    // ✅ Listen for focus changes and update reactive variable
    textFieldFocusNode.addListener(() {
      isFocused.value = textFieldFocusNode.hasFocus;
    });
  }

  @override
  void onClose() {
    textFieldFocusNode.dispose(); // ✅ Dispose FocusNode
    super.onClose();
  }

  void onTextChanged(String value, BuildContext context) {

    if (hasMistake.value) {
      hasMistake.value = false;
    }

    // Validate phone number
    if (keyboardType.value == TextInputType.phone) {
      if (areaCode == '+1') {

        // Not a valid phone number
        if (!RegExp(InputUtil.phoneNumberArea1Digit).hasMatch(value)) {

          textEditingController.text = value.substring(0, value.length - 1); // ✅ Trims excess input
          textEditingController.text = InputUtil.toOriginPhoneNumber(phoneNumber: textEditingController.text);
          // textEditingController.selection = TextSelection.fromPosition(
          //   TextPosition(offset: textEditingController.text.length),
          // );

          if(onChanged != null) onChanged!(context: context, result: false);
        }
        // Valid phone number, display in format
        else {

          textEditingController.text = InputUtil.toDisplayPhoneNumber(phoneNumber: value); // ✅ Trims excess input
          textEditingController.selection = TextSelection.fromPosition(
            TextPosition(offset: 14),
          );

          if(onChanged != null) onChanged!(context: context, result: true);
        }

      } else if (areaCode == '+86') {

        // Not a valid phone number
        if (!RegExp(InputUtil.phoneNumberArea86Digit).hasMatch(value)) {

          textEditingController.text = value.substring(0, value.length - 1); // ✅ Trims excess input
          textEditingController.text = InputUtil.toOriginPhoneNumber(phoneNumber: textEditingController.text);
          // textEditingController.selection = TextSelection.fromPosition(
          //   TextPosition(offset: textEditingController.text.length),
          // );

          if(onChanged != null) onChanged!(context: context, result: false);
        }
        // Valid phone number, display in format
        else {

          textEditingController.text = InputUtil.toDisplayPhoneNumber(phoneNumber: value, areaCode: areaCode); // ✅ Trims excess input
          textEditingController.selection = TextSelection.fromPosition(
            TextPosition(offset: 14),
          );

          if(onChanged != null) onChanged!(context: context, result: true);
        }
      }
    }
    // Validate OTP code
    else if (keyboardType.value == TextInputType.number) {

      if (!RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(value)) {

        // textEditingController.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.text.length),
        // );
        if(onChanged != null) onChanged!(context: context, result: false);
      }
      else {

        if(onChanged != null) onChanged!(context: context, result: true);
      }
      //
      // if (RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(textEditingController.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
    // Verify email address
    else if (keyboardType.value == TextInputType.emailAddress) {

      if (!RegExp(InputUtil.validEmailAddressRegEx).hasMatch(value)) {

        if(onChanged != null) onChanged!(context: context, result: false);
        // textEditingController.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.text.length),
        // );
      } else {
        if(onChanged != null) onChanged!(context: context, result: true);
      }

      //
      // if (RegExp(InputUtil.validEmailAddressRegEx).hasMatch(textEditingController.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
    // Verify password
    else if (keyboardType.value == TextInputType.visiblePassword) {

      if (!RegExp(InputUtil.validPasswordRegEx).hasMatch(value)) {

        if(onChanged != null) onChanged!(context: context, result: false);
        // textEditingController.text = value.substring(0, value.length - 1); // ✅ Trims excess input
        // textEditingController.selection = TextSelection.fromPosition(
        //   TextPosition(offset: textEditingController.text.length),
        // );
      } else {
        if(onChanged != null) onChanged!(context: context, result: true);
      }
      //
      // if (RegExp(InputUtil.validVerificationCodeRegEx).hasMatch(textEditingController.text)) {
      //
      //   if(onChanged != null) onChanged!(context: context, result: true);
      // } else {
      //
      //   if(onChanged != null) onChanged!(context: context, result: false);
      // }

    }
  }

  void toggleMistakeState() {
    hasMistake.value = !hasMistake.value;
  }

  void togglePasswordVisibility() {
    obscureText.value = !obscureText.value;
  }

  void resetTextField() {
    hasMistake.value = false;
    textEditingController.clear();
  }

  void toggleEnabled() {
    isEnabled.value = !isEnabled.value;
  }
}

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';
import 'geo_i.dart';

part 'post.g.dart';

@JsonSerializable()
class Post extends BaseFullModel {

  String uid;
  PostType type;
  PostContentType contentType;
  List<String>? pets;
  PublishVisibility visibility;
  String? title;
  String content;
  @JsonKey(fromJson: JsonUtil.geoFirePointFromJson, toJson: JsonUtil.geoFirePointToJson)
  GeoFirePoint? location; // stored in Firestore as GeoPoint
  double? altitude;
  List<String>? keywords;
  // Event
  String? placeId;
  String? displayName;
  String? formattedAddress;

  Post({
    required this.uid,
    required this.type,
    required this.contentType,
    this.pets,
    required this.visibility,
    this.title,
    required this.content,
    this.location,
    this.altitude,
    this.keywords,
    this.placeId,
    this.displayName,
    this.formattedAddress,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Post.fromJson(Map<String, dynamic> json) => _$PostFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PostToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Post.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Post.fromJson(jsonData);
  }

  factory Post.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Post.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'post';

  static create ({
    required String uid,
    required PostType type,
    required PostContentType contentType,
    List<String>? pets,
    required PublishVisibility visibility,
    String? title,
    required String content,
    GeoFirePoint? location,
    double? altitude,
    List<String>? keywords,
    String? placeId,
    String? displayName,
    String? formattedAddress,
  }) {

    return Post(
      sid: uuid.v4(),
      uid: uid,
      type: type,
      contentType: contentType,
      pets: pets,
      visibility: visibility,
      title: title,
      content: content,
      location: location,
      altitude: altitude,
      keywords: keywords,
      placeId: placeId,
      displayName: displayName,
      formattedAddress: formattedAddress,
      isValid: true,
    );
  }

  static copyFrom(Post other) {
    return Post(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      pets: other.pets,
      type: other.type,
      contentType: other.contentType,
      visibility: other.visibility,
      title: other.title,
      content: other.content,
      location: other.location,
      altitude: other.altitude,
      keywords: other.keywords,
      placeId: other.placeId,
      displayName: other.displayName,
      formattedAddress: other.formattedAddress,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

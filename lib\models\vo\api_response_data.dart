import 'package:json_annotation/json_annotation.dart';

part 'api_response_data.g.dart';

@JsonSerializable()
class ApiResponseData {
  int? code;
  String? msg;
  dynamic data;
  String? timezone;

  ApiResponseData({
    this.code,
    this.msg,
    this.data,
    this.timezone,
  });

  factory ApiResponseData.fromJson(Map<String, dynamic> json) => _$ApiResponseDataFromJson(json);
  Map<String, dynamic> toJson() => _$ApiResponseDataToJson(this);

  static create ({
    int? code,
    String? msg,
    dynamic data,
    String? timezone,
  }) {

    return ApiResponseData(
      code: code,
      msg: msg,
      data: data,
      timezone: timezone,
    );
  }

  static ApiResponseData exUnknown() {
    ApiResponseData e = ApiResponseData.create();
    e.code = -100;
    return e;
  }

  static ApiResponseData exTimeout() {
    ApiResponseData e = ApiResponseData.create();
    e.code = -200;
    return e;
  }

  static ApiResponseData exConnectionError() {
    ApiResponseData e = ApiResponseData.create();
    e.code = -300;
    return e;
  }

  static ApiResponseData exBadCert() {
    ApiResponseData e = ApiResponseData.create();
    e.code = -400;
    return e;
  }

}

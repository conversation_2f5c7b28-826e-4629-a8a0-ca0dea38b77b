// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:get/get.dart';
//
// import 'package:onenata_app/models/models_i.dart';
//
// class UserController extends GetxController {
//   final FirebaseFirestore _firestore = FirebaseFirestore.instance;
//   final Rx<UserInfo?> currentUser = Rx<UserInfo?>(null);
//
//   // 获取用户信息
//   Future<void> getUserInfo(String uid) async {
//     try {
//       final doc = await _firestore.collection('users').doc(uid).get();
//       if (doc.exists) {
//         currentUser.value = UserInfo.fromJson(doc.data()!);
//       }
//     } catch (e) {
//       print('获取用户信息失败: $e');
//     }
//   }
//
//   // 更新用户信息
//   Future<void> updateUserInfo(String uid, Map<String, dynamic> data) async {
//     try {
//       await _firestore.collection('users').doc(uid).update({
//         ...data,
//         'updatedAt': Timestamp.now(),
//       });
//       await getUserInfo(uid);
//     } catch (e) {
//       print('更新用户信息失败: $e');
//     }
//   }
//
//   // 创建新用户
//   Future<void> createUser(UserInfo user) async {
//     try {
//       await _firestore.collection('users').doc(user.sid).set(user.toJson());
//     } catch (e) {
//       print('创建用户失败: $e');
//     }
//   }
//
//   // 更新用户最后登录时间
//   Future<void> updateLastLoginTime(String uid) async {
//     try {
//       await _firestore.collection('users').doc(uid).update({
//         'lastLoginTime': Timestamp.now(),
//       });
//     } catch (e) {
//       print('更新登录时间失败: $e');
//     }
//   }
//
//   // 更新宠物数量
//   Future<void> updatePetsCount(String uid, int count) async {
//     try {
//       await _firestore.collection('users').doc(uid).update({
//         'petsCount': count,
//       });
//       await getUserInfo(uid);
//     } catch (e) {
//       print('更新宠物数量失败: $e');
//     }
//   }
// }

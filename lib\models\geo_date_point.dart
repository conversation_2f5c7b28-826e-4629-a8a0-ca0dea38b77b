import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'geo_i.dart';

part 'geo_date_point.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoDatePoint {

  GeoDate? date;
  bool? truncated;
  int? day;
  int? hour;
  int? minute;

  GeoDatePoint({
    this.date,
    this.truncated,
    this.day,
    this.hour,
    this.minute,
  });

  factory GeoDatePoint.fromJson(Map<String, dynamic> json) => _$GeoDatePointFromJson(json);
  Map<String, dynamic> toJson() => _$GeoDatePointToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'date') {
        jsonData[key] = GeoDate.fromApi(value);
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoDatePoint.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoDatePoint.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'date') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = GeoDate.fromFirestore(value);
      } else if (key == 'truncated') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = JsonUtil.boolFromJson(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoDatePoint.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoDatePoint.fromJson(jsonData);
  }

  factory GeoDatePoint.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoDatePoint.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'date') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = GeoDate.toFirestoreJson(value);
      } else if (key == 'truncated') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = JsonUtil.boolToJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoDatePoint? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({

    GeoDate? date,
    bool? truncated,
    int? day,
    int? hour,
    int? minute,
  }) {

    return GeoDatePoint(
      date: date,
      truncated: truncated,
      day: day,
      hour: hour,
      minute: minute,
    );
  }

  static GeoDatePoint copyFrom(GeoDatePoint? other) {

    return GeoDatePoint(
      date: GeoDate.copyFrom(other?.date),
      truncated: other?.truncated,
      day: other?.day,
      hour: other?.hour,
      minute: other?.minute,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

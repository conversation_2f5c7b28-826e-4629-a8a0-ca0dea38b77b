import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';
import 'geo_i.dart';

part 'post_comment.g.dart';

@JsonSerializable()
class Comment extends BaseFullModel {

  String uid;
  String author;
  String postId;
  String? commentId;
  PublishVisibility visibility;
  String content;
  @JsonKey(fromJson: JsonUtil.geoFirePointFromJson, toJson: JsonUtil.geoFirePointToJson)
  GeoFirePoint? location; // stored in Firestore as GeoPoint
  double? altitude;
  String? country;

  Comment({
    required this.uid,
    required this.author,
    required this.postId,
    this.commentId,
    required this.visibility,
    required this.content,
    this.location,
    this.altitude,
    this.country,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CommentToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory Comment.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return Comment.fromJson(jsonData);
  }

  factory Comment.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return Comment.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'post';

  static create ({
    required String uid,
    required String author,
    required String postId,
    String? commentId,
    required PublishVisibility visibility,
    required String content,
    GeoFirePoint? location,
    double? altitude,
    String? country,
  }) {

    return Comment(
      sid: uuid.v4(),
      uid: uid,
      author: author,
      postId: postId,
      commentId: commentId,
      visibility: visibility,
      content: content,
      location: location,
      altitude: altitude,
      country: country,
      isValid: true,
    );
  }

  static copyFrom(Comment other) {
    return Comment(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      author: other.author,
      postId: other.postId,
      commentId: other.commentId,
      visibility: other.visibility,
      content: other.content,
      location: other.location,
      altitude: other.altitude,
      country: other.country,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/models/pet.dart';
import 'package:onenata_app/views/theme/colors/onenata_classic_colors.dart';
import 'package:onenata_app/views/component/appointment/datetime_selection_bottom_sheet.dart';

// 服务类型数据模型
class ServiceType {
  final String id;
  final String name;
  final String description;
  final int durationMinutes;
  final double price;
  final bool isAvailable;

  ServiceType({
    required this.id,
    required this.name,
    required this.description,
    required this.durationMinutes,
    required this.price,
    this.isAvailable = true,
  });
}

class ServiceSelectionBottomSheet extends StatefulWidget {
  final Pet selectedPet;
  final VoidCallback? onServiceSelected;

  const ServiceSelectionBottomSheet({
    Key? key,
    required this.selectedPet,
    this.onServiceSelected,
  }) : super(key: key);

  @override
  State<ServiceSelectionBottomSheet> createState() => _ServiceSelectionBottomSheetState();
}

class _ServiceSelectionBottomSheetState extends State<ServiceSelectionBottomSheet> {
  ServiceType? selectedService;
  
  // 写死的服务数据，后续改为动态加载
  final List<ServiceType> services = [
    ServiceType(
      id: '1',
      name: 'Bath & Blow Dry',
      description: 'Gentle wash and blow dry',
      durationMinutes: 30,
      price: 45.00,
      isAvailable: true,
    ),
    ServiceType(
      id: '2',
      name: 'Hair Trimming',
      description: 'Professional hair trimming',
      durationMinutes: 60,
      price: 65.00,
      isAvailable: true,
    ),
    ServiceType(
      id: '3',
      name: 'Nail Clipping',
      description: 'Gentle paw care',
      durationMinutes: 20,
      price: 25.00,
      isAvailable: false,
    ),
    ServiceType(
      id: '4',
      name: 'Teeth Brushing',
      description: 'Promote good oral and dental health',
      durationMinutes: 30,
      price: 35.00,
      isAvailable: true,
    ),
    ServiceType(
      id: '5',
      name: 'Bath & Blow Dry',
      description: 'Gentle wash and blow dry',
      durationMinutes: 30,
      price: 45.00,
      isAvailable: true,
    ),
  ];

  void _selectService(ServiceType service) {
    if (service.isAvailable) {
      setState(() {
        selectedService = service;
      });
    }
  }

  void _continueToNext() {
    if (selectedService != null) {
      // 关闭当前弹窗并打开日期时间选择弹窗
      Get.back();
      DateTimeSelectionBottomSheetHelper.show(
        selectedPet: widget.selectedPet,
        selectedService: selectedService!,
        onAppointmentConfirmed: () {
          widget.onServiceSelected?.call();
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 403.w,
      height: 823.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.w)),
      ),
      child: Column(
        children: [
          // 拖拽指示条
          Container(
            margin: EdgeInsets.only(top: 12.w),
            width: 36.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
          
          // 标题
          Padding(
            padding: EdgeInsets.only(top: 20.w, left: 24.w, right: 24.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Please select a service type',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF262626),
                ),
              ),
            ),
          ),
          
          // 服务列表
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
              child: ListView.builder(
                itemCount: services.length,
                itemBuilder: (context, index) {
                  final service = services[index];
                  final isSelected = selectedService?.id == service.id;
                  
                  return GestureDetector(
                    onTap: () => _selectService(service),
                    child: Container(
                      width: 355.w,
                      height: 98.w,
                      margin: EdgeInsets.only(bottom: 12.w),
                      padding: EdgeInsets.all(10.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.w),
                        border: Border.all(
                          color: isSelected 
                              ? OneNataClassicColors.veronica 
                              : Color(0xFFE5E5E5),
                          width: isSelected ? 2.w : 1.w,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8.w,
                            offset: Offset(0, 2.w),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 服务名称和价格
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                service.name,
                                style: TextStyle(
                                  fontFamily: 'Manrope',
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: service.isAvailable 
                                      ? Color(0xFF262626) 
                                      : Color(0xFF999999),
                                ),
                              ),
                              Text(
                                'CA \$${service.price.toStringAsFixed(0)}',
                                style: TextStyle(
                                  fontFamily: 'Manrope',
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w600,
                                  color: service.isAvailable 
                                      ? Color(0xFF262626) 
                                      : Color(0xFF999999),
                                ),
                              ),
                            ],
                          ),
                          
                          SizedBox(height: 4.w),
                          
                          // 服务描述
                          Text(
                            service.description,
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w400,
                              color: service.isAvailable 
                                  ? Color(0xFF666666) 
                                  : Color(0xFF999999),
                            ),
                          ),
                          
                          SizedBox(height: 8.w),
                          
                          // 时长
                          Text(
                            '${service.durationMinutes} min',
                            style: TextStyle(
                              fontFamily: 'Manrope',
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w500,
                              color: OneNataClassicColors.veronica,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Continue 按钮
          Container(
            margin: EdgeInsets.only(bottom: 20.w, left: 24.w, right: 24.w),
            width: double.infinity,
            height: 48.w,
            child: ElevatedButton(
              onPressed: selectedService != null ? _continueToNext : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedService != null 
                    ? Color(0xFFF2D3A4) 
                    : Color(0xFFC6C6C6),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.w),
                ),
                elevation: 0,
              ),
              child: Text(
                'Continue',
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示服务选择底部弹窗的静态方法
class ServiceSelectionBottomSheetHelper {
  static void show({
    required Pet selectedPet,
    VoidCallback? onServiceSelected,
  }) {
    Get.bottomSheet(
      ServiceSelectionBottomSheet(
        selectedPet: selectedPet,
        onServiceSelected: onServiceSelected,
      ),
      isDismissible: true,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }
}

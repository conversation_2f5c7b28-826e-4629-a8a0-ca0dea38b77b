name: onenata_app
description: "onenata app project"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  #firebase
  firebase_core: ^3.12.1
  firebase_app_check: ^0.3.2+4
  firebase_messaging: ^15.2.4
  firebase_auth: ^5.5.1
  cloud_firestore: ^5.6.5
  firebase_storage: ^12.4.4
#  firebase_in_app_messaging: ^0.8.1+4
#  cloud_functions: ^5.3.4
  crypto: ^3.0.6

  #get
  get: ^4.7.2

  # Permission
  permission_handler: ^12.0.0+1

  # Media
  image_picker: ^1.1.2
  crop_your_image: ^2.0.0
#  video_player: ^2.9.3
#  camera: ^0.11.1

  # Network
  dio: ^5.8.0+1

  # Storage & Database
  path_provider: ^2.1.5
  path: ^1.9.1
  get_storage: ^2.1.1
  flutter_secure_storage: ^9.2.4
  image_gallery_saver_plus: ^4.0.0

  # date time and lang
  date_format: ^2.0.7
  jiffy: ^6.3.2
  intl: ^0.19.0
  timezone: ^0.10.0
  flutter_timezone: ^4.1.0
  flutter_easyloading: ^3.0.5
  easy_localization: ^3.0.7+1
  cached_network_image: ^3.3.1

  # UI UX
  google_fonts: ^6.2.1
  flutter_screenutil: ^5.9.3

  # Utils
  logger: ^2.5.0
  flutter_dotenv: ^5.1.0
  vibration: ^3.1.3
  json_annotation: ^4.9.0

  # 国旗
  flag: 7.0.0
  country_list_pick: ^1.0.1
  country_picker: ^2.0.27

  # 通用弹框
  bot_toast: ^4.1.3

  #uuid:
  uuid: ^4.5.1

  #google_maps
  google_maps_flutter: ^2.12.1
  geolocator: ^13.0.4
  geoflutterfire_plus: ^0.0.32


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # 动画：
  confetti: ^0.8.0
  flutter_svg: ^2.0.17

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env.dev
    - .env.prod
    - assets/images/
    - assets/translations/
    - assets/images/map/
    - assets/scripts/mapStyle.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

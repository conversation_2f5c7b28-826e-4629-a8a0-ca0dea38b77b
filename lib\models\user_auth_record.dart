import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_model.dart';

part 'user_auth_record.g.dart';

@JsonSerializable()
class UserAuthRecord extends BaseModel {

  String uid;
  AuthChannel authChannel;
  String? deviceModel;
  String? deviceOS;
  String? deviceId;
  String? fcmToken;

  UserAuthRecord({
    required this.uid,
    required this.authChannel,
    this.deviceModel,
    this.deviceOS,
    this.deviceId,
    this.fcmToken,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
  });

  factory UserAuthRecord.fromJson(Map<String, dynamic> json) => _$UserAuthRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$UserAuthRecordToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory UserAuthRecord.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return UserAuthRecord.fromJson(jsonData);
  }

  factory UserAuthRecord.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return UserAuthRecord.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'user_auth_record';

  static create ({
    required String uid,
    required AuthChannel authChannel,
    String? deviceModel,
    String? deviceOS,
    String? deviceId,
    String? fcmToken,
  }) {

    return UserAuthRecord(
      sid: uuid.v4(),
      uid: uid,
      authChannel: authChannel,
      deviceModel: deviceModel,
      deviceOS: deviceOS,
      deviceId: deviceId,
      fcmToken: fcmToken,
      isValid: true,
    );
  }

  static copyFrom(UserAuthRecord other) {
    return UserAuthRecord(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      uid: other.uid,
      authChannel: other.authChannel,
      deviceModel: other.deviceModel,
      deviceOS: other.deviceOS,
      deviceId: other.deviceId,
      fcmToken: other.fcmToken,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

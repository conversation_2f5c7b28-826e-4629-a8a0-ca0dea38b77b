import 'dart:async';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

class PetDao {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  final CollectionReference<Map<String, dynamic>> _petCollection = FirebaseFirestore.instance.collection(Pet.collection);
  final CollectionReference _petFeedingRecordCollection = FirebaseFirestore.instance.collection('pet_feeding_record');
  // final CollectionReference _petGeoRecordCollection = FirebaseFirestore.instance.collection(PetGeoRecord.collection());
  final CollectionReference _petGrowingRecordCollection = FirebaseFirestore.instance.collection('pet_growing_record');
  final CollectionReference _petOwnerRecordCollection = FirebaseFirestore.instance.collection('pet_owner_record');
  final CollectionReference _petVaccineRecordCollection = FirebaseFirestore.instance.collection('pet_vaccine_record');
  final CollectionReference _petDaycareRecordCollection = FirebaseFirestore.instance.collection('pet_daycare_record');
  final CollectionReference _petGroomingRecordCollection = FirebaseFirestore.instance.collection('pet_grooming_record');
  final CollectionReference _petMedicineRecordCollection = FirebaseFirestore.instance.collection('pet_medicine_record');
  final CollectionReference _petOtherRecordCollection = FirebaseFirestore.instance.collection('pet_other_record');
  final CollectionReference _petPoopRecordCollection = FirebaseFirestore.instance.collection('pet_poop_record');
  final CollectionReference _petSocialRecordCollection = FirebaseFirestore.instance.collection('pet_social_record');
  final CollectionReference _petVetRecordCollection = FirebaseFirestore.instance.collection('pet_vet_record');
  final CollectionReference _petWalkingRecordCollection = FirebaseFirestore.instance.collection('pet_walking_record');

  // -------------------------------- Pet --------------------------------
  Future<void> addPet(Pet pet) async {
    DocumentReference petRef = _petCollection.doc(pet.sid!);

    Map<String, dynamic> petDoc = pet.toFirestoreData();
    petDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await petRef.set(petDoc);
  }

  Future<void> updatePet(Pet pet, {bool mergeOption = false}) async {

    DocumentReference petRef = _petCollection.doc(pet.sid!);
    Map<String, dynamic> petDoc = pet.toFirestoreData();
    petDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await petRef.set(petDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deletePet(String petId) async {
    DocumentReference petRef = _petCollection.doc(petId);
    await petRef.delete();
  }

  Future<void> softDeletePet(String petId) async {
    DocumentReference petRef = _petCollection.doc(petId);
    await petRef.update({CommonField.isValid: 0});
  }

  Future<List<Pet>?> getPetsByUser ({required String uid, bool? isValid}) async {

    Query query = _petCollection
        .where('owner', isEqualTo: uid);

    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query.get();

    if (querySnapshot.docs.isNotEmpty) {

      List<Pet> pets = querySnapshot.docs.map((doc) {
        return Pet.fromFirestoreData(doc);
      }).toList();

      return pets;
    }
    else {
      return null;
    }
  }

  Future<Pet?> getPetById (String petId) async {

    DocumentSnapshot doc = await _petCollection.doc(petId).get();

    if (doc.exists) {
      return Pet.fromFirestoreData(doc);
    }
    else {
      return null;
    }
  }

  Future<bool> exists ({required String uid, required String petName}) async {

    QuerySnapshot querySnapshot = await _petCollection
        .where('owner', isEqualTo: uid)
        .where('name', isEqualTo: petName)
        .get();

    return querySnapshot.docs.isNotEmpty;
  }

  // Function to get GeoPoint instance from Cloud Firestore document data.
  GeoPoint geopointFrom(Map<String, dynamic> data) =>
      (data['latest_location'] as Map<String, dynamic>)['geopoint'] as GeoPoint;

  Future<List<Pet>> getPetListOnce(Stream<List<Pet>> petStream) async {
    return await petStream.first;
  }

  /// Query pets by location and radius
  Future<List<Pet>?> getNearbyPets ({
    required String uid,
    required GeoPoint center,
    double? radius = 2, // in km
    String? field = 'latest_location', // the location property name
    int? pageSize = 20,
    int? lastCreateDate,
    bool? isValid
  }) async {

    // Streamed document snapshots of geo query under given conditions.
    final Stream<List<DocumentSnapshot<Map<String, dynamic>>>> stream =
    GeoCollectionReference<Map<String, dynamic>>(_petCollection).subscribeWithin(
      center: GeoFirePoint(center),
      radiusInKm: radius!,
      field: field!,
      geopointFrom: geopointFrom,
      queryBuilder: (query) {
        // Conditions
        query = query.where('owner', isNotEqualTo: uid);
        if (isValid != null) {
          query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
        }
        // Filter recent 1 hour
        query = query.where('latest_location_at', isGreaterThanOrEqualTo: DateTimeUtil.currentMilliseconds()- 3600 * 1000);

        // TODO change to query by latlng with sorting by create_date (performance purpose)
        // Sorting
        // query = query.orderBy('create_date', descending: true);

        // // Pagination start
        // if (lastCreateDate != null) {
        //   query = query.startAfter([lastCreateDate]);
        // }
        //
        // // Page size
        // query = query.limit(pageSize!);

        return query;
      },
    );

    return await stream.map(
          (docList) {
            List<Pet> pets = docList
                .map((doc) =>
            doc.data() == null ? null : Pet.fromFirestoreData(doc))
                .whereType<Pet>() // filters out nulls
                .toList();
            return pets;
          }
    ).first;
  }

  // -------------------------------- Pet Feeding Record --------------------------------
  // Future<void> addPetFeedingRecord1({
  //   required String petId,
  //   required PetFeedingRecord record,
  // }) async {
  //   final petRef = _petCollection.doc(petId);
  //
  //   await petRef.update({
  //     'feeding_records': FieldValue.arrayUnion([record.toJson()])
  //   });
  // }
  //
  // Future<void> updatePetFeedingRecord1({
  //   required String petId,
  //   required PetFeedingRecord updatedRecord,
  // }) async {
  //   await feedingRecordCollection(petId)
  //       .doc(updatedRecord.sid)
  //       .set(updatedRecord.toJson());
  // }
  //
  // Future<void> deletePetFeedingRecord1({
  //   required String petId,
  //   required String recordSid,
  // }) async {
  //   final petRef = _petCollection.doc(petId);
  //
  //   final petSnapshot = await petRef.get();
  //   if (!petSnapshot.exists) {
  //     throw Exception('Pet not found');
  //   }
  //
  //   final data = petSnapshot.data() as Map<String, dynamic>;
  //   List<dynamic> feedingRecords = data['feeding_records'] ?? [];
  //
  //   List updatedRecords = feedingRecords.where((record) {
  //     final recordMap = Map<String, dynamic>.from(record);
  //     return recordMap['sid'] != recordSid;
  //   }).toList();
  //
  //   await petRef.update({'feeding_records': updatedRecords});
  // }
  //
  // Future<void> softDeletePetFeedingRecord1({
  //   required String petId,
  //   required String recordSid,
  // }) async {
  //   final petRef = _petCollection.doc(petId);
  //
  //   final petSnapshot = await petRef.get();
  //   if (!petSnapshot.exists) {
  //     throw Exception('Pet not found');
  //   }
  //
  //   final data = petSnapshot.data() as Map<String, dynamic>;
  //   List<dynamic> feedingRecords = data['feeding_records'] ?? [];
  //
  //   List<Map<String, dynamic>> updatedRecords = feedingRecords.map((record) {
  //     final recordMap = Map<String, dynamic>.from(record);
  //     if (recordMap['sid'] == recordSid) {
  //       recordMap['is_valid'] = 0;
  //     }
  //     return recordMap;
  //   }).toList();
  //
  //   await petRef.update({'feeding_records': updatedRecords});
  // }

  CollectionReference feedingRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('feeding_records');
  }

  Future<void> addPetFeedingRecordToCollection({
    required String petId,
    required PetFeedingRecord record,
  }) async {
    await feedingRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetFeedingRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await feedingRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetFeedingRecordInCollection({
    required String petId,
    required PetFeedingRecord updatedRecord,
  }) async {
    await feedingRecordCollection(petId)
        .doc(updatedRecord.sid)
        .set(updatedRecord.toJson());
  }

  Future<List<PetFeedingRecord>> getPetFeedingRecordsFromCollection(String petId) async {
    final querySnapshot = await feedingRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetFeedingRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference poopRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('poop_records');
  }

  Future<void> addPetPoopRecordToCollection({
    required String petId,
    required PetPoopRecord record,
  }) async {
    await poopRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetPoopRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await poopRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetPoopRecordInCollection({
    required String petId,
    required PetPoopRecord record,
  }) async {
    await poopRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<List<PetPoopRecord>> getPetPoopRecordsFromCollection(String petId) async {
    final querySnapshot = await poopRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetPoopRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference walkingRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('walking_records');
  }

  Future<void> addPetWalkingRecordToCollection({
    required String petId,
    required PetWalkingRecord record,
  }) async {
    await walkingRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetWalkingRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await walkingRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetWalkingRecordInCollection({
    required String petId,
    required PetWalkingRecord record,
  }) async {
    await walkingRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetWalkingRecord>> getPetWalkingRecordsFromCollection(String petId) async {
    final querySnapshot = await walkingRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetWalkingRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference socialRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('social_records');
  }

  Future<void> addPetSocialRecordToCollection({
    required String petId,
    required PetSocialRecord record,
  }) async {
    await socialRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetSocialRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await socialRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetSocialRecordInCollection({
    required String petId,
    required PetSocialRecord record,
  }) async {
    await socialRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetSocialRecord>> getPetSocialRecordsFromCollection(String petId) async {
    final querySnapshot = await socialRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetSocialRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference medicineRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('medicine_records');
  }

  Future<void> addPetMedicineRecordToCollection({
    required String petId,
    required PetMedicineRecord record,
  }) async {
    await medicineRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetMedicineRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await medicineRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetMedicineRecordInCollection({
    required String petId,
    required PetMedicineRecord record,
  }) async {
    await medicineRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetMedicineRecord>> getPetMedicineRecordsFromCollection(String petId) async {
    final querySnapshot = await medicineRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetMedicineRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference vaccineRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('vaccine_records');
  }

  Future<void> addPetVaccineRecordToCollection({
    required String petId,
    required PetVaccineRecord record,
  }) async {
    await vaccineRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetVaccineRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await vaccineRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetVaccineRecordInCollection({
    required String petId,
    required PetVaccineRecord record,
  }) async {
    await vaccineRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetVaccineRecord>> getPetVaccineRecordsFromCollection(String petId) async {
    final querySnapshot = await vaccineRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetVaccineRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference groomingRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('grooming_records');
  }

  Future<void> addPetGroomingRecordToCollection({
    required String petId,
    required PetGroomingRecord record,
  }) async {
    await groomingRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetGroomingRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await groomingRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetGroomingRecordInCollection({
    required String petId,
    required PetGroomingRecord record,
  }) async {
    await groomingRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetGroomingRecord>> getPetGroomingRecordsFromCollection(String petId) async {
    final querySnapshot = await groomingRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetGroomingRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference daycareRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('daycare_records');
  }

  Future<void> addPetDaycareRecordToCollection({
    required String petId,
    required PetDaycareRecord record,
  }) async {
    await daycareRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetDaycareRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await daycareRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetDaycareRecordInCollection({
    required String petId,
    required PetDaycareRecord record,
  }) async {
    await daycareRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetDaycareRecord>> getPetDaycareRecordsFromCollection(String petId) async {
    final querySnapshot = await daycareRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetDaycareRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference vetRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('vet_records');
  }

  Future<void> addPetVetRecordToCollection({
    required String petId,
    required PetVetRecord record,
  }) async {
    await vetRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetVetRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await vetRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetVetRecordInCollection({
    required String petId,
    required PetVetRecord record,
  }) async {
    await vetRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetVetRecord>> getPetVetRecordsFromCollection(String petId) async {
    final querySnapshot = await vetRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetVetRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  CollectionReference otherRecordCollection(String petId) {
    return _petCollection.doc(petId).collection('other_records');
  }

  Future<void> addPetOtherRecordToCollection({
    required String petId,
    required PetOtherRecord record,
  }) async {
    await otherRecordCollection(petId).doc(record.sid).set(record.toJson());
  }

  Future<void> deletePetOtherRecordFromCollection({
    required String petId,
    required String recordSid,
  }) async {
    await otherRecordCollection(petId).doc(recordSid).delete();
  }

  Future<void> updatePetOtherRecordInCollection({
    required String petId,
    required PetOtherRecord record,
  }) async {
    await otherRecordCollection(petId)
        .doc(record.sid)
        .set(record.toJson());
  }

  Future<List<PetOtherRecord>> getPetOtherRecordsFromCollection(String petId) async {
    final querySnapshot = await otherRecordCollection(petId).get();
    return querySnapshot.docs.map((doc) => PetOtherRecord.fromJson(doc.data() as Map<String, dynamic>)).toList();
  }


  // -------------------------------- Pet Geo Record --------------------------------
  CollectionReference geoLocationRecordColRef(String petId) {
    return _petCollection.doc(petId).collection(PetGeoRecord.collection);
  }
  Future<void> addPetGeoRecord(PetGeoRecord record) async {
    DocumentReference recordRef = geoLocationRecordColRef(record.pid).doc(record.sid!);

    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc);
  }
  Future<void> updatePetGeoRecord(PetGeoRecord record, {bool mergeOption = false}) async {
    DocumentReference recordRef = geoLocationRecordColRef(record.pid).doc(record.sid!);
    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc, SetOptions(merge: mergeOption));
  }
  Future<void> deletePetGeoRecord(String pid, String recordId) async {
    DocumentReference recordRef = geoLocationRecordColRef(pid).doc(recordId);
    await recordRef.delete();
  }
  Future<void> softDeletePetGeoRecord(String pid, String recordId) async {
    DocumentReference recordRef = geoLocationRecordColRef(pid).doc(recordId);
    await recordRef.update({CommonField.isValid: 0});
  }

  Future<PetGeoRecord?> getPetGeoRecordById ({required String pid, required String recordId}) async {

    DocumentSnapshot doc = await geoLocationRecordColRef(pid).doc(recordId).get();

    if (doc.exists) {
      return PetGeoRecord.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastCreateDate] is the create_date of the last message in the previous page
  Future<List<PetGeoRecord>?> getPetGeoRecordList ({required String pid, int? pageSize = 20, int? lastCreateDate, bool? isValid}) async {

    Query query = geoLocationRecordColRef(pid);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('create_date', descending: true);

    // Pagination start
    if (lastCreateDate != null) {
      query = query.startAfter([lastCreateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> PetGeoRecord.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<PetGeoRecord?> getLatestPetGeoRecordByPet ({required String pid, bool? isValid}) async {

    Query query = geoLocationRecordColRef(pid);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('create_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return PetGeoRecord.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

  // -------------------------------- Pet Growing Record --------------------------------
  CollectionReference growingRecordColRef(String petId) {
    return _petCollection.doc(petId).collection(PetGrowingRecord.collection);
  }
  Future<void> addPetGrowingRecord(PetGrowingRecord record) async {
    DocumentReference recordRef = growingRecordColRef(record.pid).doc(record.sid!);

    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc);
  }
  Future<void> updatePetGrowingRecord(PetGrowingRecord record, {bool mergeOption = false}) async {
    DocumentReference recordRef = growingRecordColRef(record.pid).doc(record.sid!);
    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc, SetOptions(merge: mergeOption));
  }
  Future<void> deletePetGrowingRecord(String pid, String recordId) async {
    DocumentReference recordRef = geoLocationRecordColRef(pid).doc(recordId);
    await recordRef.delete();
  }
  Future<void> softDeletePetGrowingRecord(String pid, String recordId) async {
    DocumentReference recordRef = geoLocationRecordColRef(pid).doc(recordId);
    await recordRef.update({CommonField.isValid: 0});
  }

  Future<PetGrowingRecord?> getPetGrowingRecordById ({required String pid, required String recordId}) async {

    DocumentSnapshot doc = await growingRecordColRef(pid).doc(recordId).get();

    if (doc.exists) {
      return PetGrowingRecord.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  /// Query with pagination
  /// Order by [create_date] descending
  /// [lastCreateDate] is the create_date of the last message in the previous page
  Future<List<PetGrowingRecord>?> getPetGrowingRecordList ({required String pid, int? pageSize = 20, int? lastCreateDate, bool? isValid}) async {

    Query query = geoLocationRecordColRef(pid);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    // Sorting
    query = query.orderBy('create_date', descending: true);

    // Pagination start
    if (lastCreateDate != null) {
      query = query.startAfter([lastCreateDate]);
    }

    // Query page
    QuerySnapshot querySnapshot = await query
        .limit(pageSize!)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((e)=> PetGrowingRecord.fromFirestoreData(e)).toList();
    } else {
      return null;
    }
  }

  Future<PetGrowingRecord?> getLatestPetGrowingRecordByPet ({required String pid, bool? isValid}) async {

    Query query = geoLocationRecordColRef(pid);

    // Conditions
    if (isValid != null) {
      query = query.where('is_valid', isEqualTo: JsonUtil.boolToJson(isValid));
    }

    QuerySnapshot querySnapshot = await query
        .orderBy('create_date', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return PetGrowingRecord.fromFirestoreData(querySnapshot.docs.first);
    } else {
      return null;
    }
  }

  // -------------------------------- Pet Owner Record --------------------------------
  Future<void> addPetOwnerRecord(PetOwnerRecord record) async {
    DocumentReference recordRef = _petOwnerRecordCollection.doc(record.sid!);

    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc);
  }

  Future<void> updatePetOwnerRecord(PetOwnerRecord record, {bool mergeOption = false}) async {
    DocumentReference recordRef = _petOwnerRecordCollection.doc(record.sid!);
    Map<String, dynamic> recordDoc = record.toFirestoreData();
    recordDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await recordRef.set(recordDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deletePetOwnerRecord(String recordId) async {
    DocumentReference recordRef = _petOwnerRecordCollection.doc(recordId);
    await recordRef.delete();
  }

  Future<void> softDeletePetOwnerRecord(String recordId) async {
    DocumentReference recordRef = _petOwnerRecordCollection.doc(recordId);
    await recordRef.update({CommonField.isValid: 0});
  }

  // -------------------------------- Pet Vaccine Record --------------------------------
  Future<void> addPetVaccineRecord({
    required String petId,
    required PetVaccineRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'vaccine_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetVaccineRecord({
    required String petId,
    required PetVaccineRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vaccineRecords = data['vaccine_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = vaccineRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'vaccine_records': updatedRecords});
  }

  Future<void> deletePetVaccineRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vaccineRecords = data['vaccine_records'] ?? [];

    List updatedRecords = vaccineRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'vaccine_records': updatedRecords});
  }

  Future<void> softDeletePetVaccineRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vaccineRecords = data['vaccine_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = vaccineRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'vaccine_records': updatedRecords});
  }


  Future<void> addPetDaycareRecord({
    required String petId,
    required PetDaycareRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'daycare_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetDaycareRecord({
    required String petId,
    required PetDaycareRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> daycareRecords = data['daycare_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = daycareRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'daycare_records': updatedRecords});
  }

  Future<void> deletePetDaycareRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> daycareRecords = data['daycare_records'] ?? [];

    List updatedRecords = daycareRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'daycare_records': updatedRecords});
  }

  Future<void> softDeletePetDaycareRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> daycareRecords = data['daycare_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = daycareRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'daycare_records': updatedRecords});
  }


  Future<void> addPetGroomingRecord({
    required String petId,
    required PetGroomingRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'grooming_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetGroomingRecord({
    required String petId,
    required PetGroomingRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> groomingRecords = data['grooming_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = groomingRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'grooming_records': updatedRecords});
  }

  Future<void> deletePetGroomingRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> groomingRecords = data['grooming_records'] ?? [];

    List updatedRecords = groomingRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'grooming_records': updatedRecords});
  }

  Future<void> softDeletePetGroomingRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> groomingRecords = data['grooming_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = groomingRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'grooming_records': updatedRecords});
  }


  Future<void> addPetOtherRecord({
    required String petId,
    required PetOtherRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'other_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetOtherRecord({
    required String petId,
    required PetOtherRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> otherRecords = data['other_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = otherRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'other_records': updatedRecords});
  }

  Future<void> deletePetOtherRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> otherRecords = data['other_records'] ?? [];

    List updatedRecords = otherRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'other_records': updatedRecords});
  }

  Future<void> softDeletePetOtherRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> otherRecords = data['other_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = otherRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'other_records': updatedRecords});
  }


  Future<void> addPetPoopRecord({
    required String petId,
    required PetPoopRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'poop_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetPoopRecord({
    required String petId,
    required PetPoopRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> poopRecords = data['poop_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = poopRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'poop_records': updatedRecords});
  }

  Future<void> deletePetPoopRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> poopRecords = data['poop_records'] ?? [];

    List updatedRecords = poopRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'poop_records': updatedRecords});
  }

  Future<void> softDeletePetPoopRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> poopRecords = data['poop_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = poopRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'poop_records': updatedRecords});
  }



  Future<void> addPetSocialRecord({
    required String petId,
    required PetSocialRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'social_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetSocialRecord({
    required String petId,
    required PetSocialRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> socialRecords = data['social_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = socialRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'social_records': updatedRecords});
  }

  Future<void> deletePetSocialRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> socialRecords = data['social_records'] ?? [];

    List updatedRecords = socialRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'social_records': updatedRecords});
  }

  Future<void> softDeletePetSocialRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> socialRecords = data['social_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = socialRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'social_records': updatedRecords});
  }


  Future<void> addPetVetRecord({
    required String petId,
    required PetVetRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'vet_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetVetRecord({
    required String petId,
    required PetVetRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vetRecords = data['vet_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = vetRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'vet_records': updatedRecords});
  }

  Future<void> deletePetVetRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vetRecords = data['vet_records'] ?? [];

    List updatedRecords = vetRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'vet_records': updatedRecords});
  }

  Future<void> softDeletePetVetRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> vetRecords = data['vet_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = vetRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'vet_records': updatedRecords});
  }


  Future<void> addPetMedicineRecord({
    required String petId,
    required PetMedicineRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'medicine_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetMedicineRecord({
    required String petId,
    required PetMedicineRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> medicineRecords = data['medicine_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = medicineRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'medicine_records': updatedRecords});
  }

  Future<void> deletePetMedicineRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> medicineRecords = data['medicine_records'] ?? [];

    List updatedRecords = medicineRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'medicine_records': updatedRecords});
  }

  Future<void> softDeletePetMedicineRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> medicineRecords = data['medicine_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = medicineRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'medicine_records': updatedRecords});
  }


  Future<void> addPetWalkingRecord({
    required String petId,
    required PetWalkingRecord record,
  }) async {
    final petRef = _petCollection.doc(petId);

    await petRef.update({
      'walking_records': FieldValue.arrayUnion([record.toJson()])
    });
  }

  Future<void> updatePetWalkingRecord({
    required String petId,
    required PetWalkingRecord updatedRecord,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> walkingRecords = data['walking_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = walkingRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == updatedRecord.sid) {
        return updatedRecord.toJson();
      }
      return recordMap;
    }).toList();

    await petRef.update({'walking_records': updatedRecords});
  }

  Future<void> deletePetWalkingRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> walkingRecords = data['walking_records'] ?? [];

    List updatedRecords = walkingRecords.where((record) {
      final recordMap = Map<String, dynamic>.from(record);
      return recordMap['sid'] != recordSid;
    }).toList();

    await petRef.update({'walking_records': updatedRecords});
  }

  Future<void> softDeletePetWalkingRecord({
    required String petId,
    required String recordSid,
  }) async {
    final petRef = _petCollection.doc(petId);

    final petSnapshot = await petRef.get();
    if (!petSnapshot.exists) {
      throw Exception('Pet not found');
    }

    final data = petSnapshot.data() as Map<String, dynamic>;
    List<dynamic> walkingRecords = data['walking_records'] ?? [];

    List<Map<String, dynamic>> updatedRecords = walkingRecords.map((record) {
      final recordMap = Map<String, dynamic>.from(record);
      if (recordMap['sid'] == recordSid) {
        recordMap['is_valid'] = 0;
      }
      return recordMap;
    }).toList();

    await petRef.update({'walking_records': updatedRecords});
  }
}

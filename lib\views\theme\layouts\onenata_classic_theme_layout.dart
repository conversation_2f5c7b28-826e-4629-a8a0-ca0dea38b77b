import 'dart:ui';

import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'theme_layout.dart';

class OneNataClassicThemeLayout extends ThemeLayout {
  OneNataClassicThemeLayout() {
    // Common
    inputFieldWidth = 327.w;
    inputFieldHeight = 60.w;
    inputFieldCircular = 16.w;
    inputFieldBorderWidth = 1.w;

    longButtonWidth = 327.w;
    longButtonHeight = 60.w;
    longButtonCircular = 30.5.w;

    // Header
    leftArrowBlackSize = 20.w;
    leftArrowYellowSize = 24.w;
    leftArrowYellowBgSize = 46.w;
    leftArrowYellowBgCircular = 16.w;

    // Bottom
    bottomNaviBarHeight = 59.w;
    bottomNaviItemBoxWidth = 65.w;
    bottomNaviItemBoxHeight = 47.w;
    bottomNaviItemIconSize = 20.w;
    bottomNaviItemIconSpaceAfter = 10.w;

    // Auth page
    authTextFieldWidth = 327.w;
    authTextFieldHeight = 60.w;
    authTextFieldCircular = 16.w;
    authTextFieldBorderWidth = 1.w;
    authTextFieldPaddingHorizontal = 24.w;
    authTextFieldPaddingVertical = 15.w;
    authTextFieldClearIconSize = 12.w;
    authTextFieldPasswordVisibilityIconSize = 24.w;
    authButtonWidth = 327.w;
    authButtonHeight = 60.w;
    authButtonCircular = 30.5.w;
    authAlterButtonWidth = 194.w;
    authAlterButtonHeight = 50.w;
    authLabelTitleWidth = 327.w;
    authLabelDescHeight = 50.w;
    authLabelTitlePaddingLeft = 38.w;
    authLabelLineSpace = 10.w;

    authWelcomeAvatarSize = 120.w;

    authAvatarHeaderLineHeight = 222.w;
    authAvatarHeaderLineHeight2 = 197.w;
    authAvatarPaddingTop = 50.w;
    authAvatarPaddingLeft = 159.w;
    authTitleBarHeight = 50.w;
    authWhiteSectionCircular = 85.w;
    authWhiteSectionShadowOffset = Offset(0, 0);
    authWhiteSectionShadowBlur = 12.w;
    authWhiteSectionShadowSpread = 0;

    authBackButtonIconWidth = 7.w;
    authBackButtonIconHeight = 14.w;
    authBackButtonIconPaddingTop = 60.w;
    authBackButtonIconPaddingLeft = 24.w;
    authBackButtonBgSize = 46.w;
    authBackButtonBgCircular = 16.w;
    authBackButtonBgShadowOffset = Offset(0, 1.w);
    authBackButtonBgShadowBlur = 4.w;
    authBackButtonBgShadowSpread = 0;

    authBackgroundImageWidth = 447.11.w;
    authBackgroundImageHeight = 443.96.w;
    authBackgroundImagePaddingLeft = -22.w;
    authBackgroundImagePaddingTop = 0.w;

    authAvatarSize = 86.w;
    authAvatarShadowOffset = Offset(0, 1.w);
    authAvatarShadowBlur = 4.w;
    authAvatarShadowSpread = 0;

    authAvatarTitleMarginTop = 14.w;

    authPetBreedBgSize = 160.w;
    authPetBreedBgCircular = 50.w;
    authPetBreedVectorWidth = 145.92.w;
    authPetBreedVectorHeight = 111.04.w;
    authPetBreedBallLargeBgSize = 50.79.w;
    authPetBreedBallLargeSize = 35.89.w;
    authPetBreedBallSmallSize = 23.89.w;
    authPetBreedBallSmallSize = 16.89.w;
    authPetBreedDogSize = 120.w;
    authPetBreedCatSize = 120.w;
    authPetBreedOtherSize = 110.w;

    authPetAddAvatarBgLarge1Size = 280.w;
    authPetAddAvatarBgLarge1BorderWidth = 1.w;
    authPetAddAvatarBgLarge2Size = 226.w;
    authPetAddAvatarBgLarge2BorderWidth = 1.w;
    authPetAddAvatarBgSize = 186.w;
    authPetAddAvatarIconSize = 35.07.w;
    authPetAddAvatarImageSize = 186.w;
    authPetAddAvatarCameraBgSize = 46.w;
    authPetAddAvatarCameraBgCircular = 14.w;
    authPetAddAvatarCameraBgShadowOffset = Offset(0, 1.w);
    authPetAddAvatarCameraBgShadowBlur = 4.w;
    authPetAddAvatarCameraBgShadowSpread = 0;

    authPetAddInfoBgLarge1Size = 198.w;
    authPetAddInfoBgLarge1BorderWidth = 0.7.w;
    authPetAddInfoBgLarge2Size = 160.w;
    authPetAddInfoBgLarge2BorderWidth = 0.7.w;
    authPetAddInfoBgSize = 125.w;
    authPetAddInfoIconSize = 24.5.w;
    authPetAddInfoImageSize = 125.w;
    authPetAddInfoCameraBgSize = 32.2.w;
    authPetAddInfoCameraBgCircular = 9.8.w;
    authPetAddInfoCameraBgShadowOffset = Offset(0, 0.7.w);
    authPetAddInfoCameraBgShadowBlur = 2.8.w;
    authPetAddInfoCameraBgShadowSpread = 0;

    // Profile page
    profileAvatarLargeSize = 86.w;
    profileAvatarMediumSize = 74.27.w;
    profileAvatarMediumBorderWidth = 2.w;
    profileAddPetIconSize = 17.5.w;
    profileAvatarSmallSize = 42.w;
    profileAvatarCameraBarWidth = 24.w;
    profileAvatarCameraBarHeight = 24.w;
    profileAvatarCameraBarCircular = 7.3.w;
    profileAvatarCameraBarShadowOffset = Offset(0, 0.52.w);
    profileAvatarCameraBarShadowBlur = 2.1.w;
    profileAvatarCameraBarShadowSpread = 0;
    profileAvatarCameraIconWidth = 11.33.w;
    profileAvatarCameraIconHeight = 11.33.w;
    profileSettingListIconSize = 18.w;
    profileSettingListIconBgSize = 46.w;
    profileSettingListIconBgBorderWidth = 1.2.w;
    profileSettingRightArrowSize = 24.w;
    profileContactInfoIconSize = 20.w;
    profileAddPetAvatarLargeSize = 186.w;
    profileAddPetAvatarSize = 125.w;
    profilePetAvatarSize = 74.5.w;

    // Even page
    eventAvatarSize = 74.27.w;
    eventAvatarBgSize = 94.w;
    eventAvatarLargeSize = 128.w;
    eventAvatarLargeBgSize = 162.w;
    eventPetProfileEditIconSize = 20.w;
    eventPetProfileEditIconBgSize = 38.w;
    eventPetProfileEditIconBgBorderWidth = 1.w;
    eventPetProfileListIconSize = 20.w;
    eventPetProfileListIconBgSize = 46.w;
    eventPetProfileListIconBgBorderWidth = 1.w;
    eventPostGridWidth = 364.w;
    eventPostTileWidth = 178.w;
    eventPostTileHeight = 169.w;
    eventPostTilePadding = 8.w;
    eventPostTileGap = 8.w;
    eventPostImageWidth = 162.w;
    eventPostImageHeight = 115.w;
    eventPostImageCircular = 2.w;
    eventEventTileWidth = 355.w;
    eventEventTileHeight = 84.w;
    eventEventTileCircular = 14.w;
    eventEventTilePadding = 16.w;
    eventEventTileBorderWidth = 1.5.w;
    eventEventImageSize = 52.w;
    eventEventSubTileWidth = 255.w;
    eventEventSubTileHeightDate = 20.w;
    eventEventSubTileHeightDetail = 24.w;
    eventEventDateIconWidth = 12.w;
    eventEventDateIconHeight = 13.5.w;
    eventEventPlaceIconSize = 24.w;
    eventPlaceTileWidth = 355.w;
    eventPlaceTileHeight = 96.w;
    eventPlaceTileCircular = 14.w;
    eventPlaceTilePadding = 16.w;
    eventPlaceTileGap = 16.w;
    eventPlaceTileShadow1Offset = Offset(0, 4.w);
    eventPlaceTileShadow1Blur = 20.w;
    eventPlaceTileShadow1Spread = -2.w;
    eventPlaceTileShadow2Offset = Offset(0, 0);
    eventPlaceTileShadow2Blur = 5.w;
    eventPlaceTileShadow2Spread = 0.w;

    // Geo location page
    geoMyLocationIconSize = 40.w;
    geoMyLocationIconMarginRight = 10.w;
    geoMyLocationIconMarginBottom = 100.w;
    geoCommonLocationDotSize = 28.w;
    geoPetLocationAvatarSize = 42.w;
    geoPetLocationIconWidth = 56.w;
    geoPetLocationIconHeight = 80.w;
    geoPlaceIconColorSize = 40.w;
    geoPlaceIconWhiteSize = 50.w;
    geoPlaceIconParkWidth = 14.25.w;
    geoPlaceIconParkHeight = 12.3.w;
    geoPlaceIconTrailWidth = 13.95.w;
    geoPlaceIconTrailHeight = 13.95.w;
    geoFilterButtonWidthOutdoor = 97.w;
    geoFilterButtonWidthPark = 148.w;
    geoFilterButtonWidthTrail = 148.w;
    geoFilterButtonWidthVet = 72.85.w;
    geoFilterButtonWidthPetStore = 104.w;
    geoFilterButtonWidthDogFriendlyRestaurant = 187.w;
    geoFilterButtonWidthFavor = 107.w;
    geoFilterButtonHeight = 35.w;
    geoFilterButtonBorderRadius = 10.w;
    geoFilterButtonSelectedBorderWidth = 1.5.w;

    geoPlaceInfoListHeightLong = 495.w;
    geoPlaceInfoListHeightShort = 280.w;
    geoPlaceInfoCardWidth = 392.w;
    geoPlaceInfoCardHeight = 220.w;
    geoPlaceInfoCardImageWidth = 101.w;
    geoPlaceInfoCardImageHeight = 80.w;
    geoPlaceInfoCardImageBorderRadius = 6.15.w;
    geoPlaceInfoCardTextPanelWidth = 186.w;
    geoPlaceInfoCardTextPanelHeight = 105.w;
    geoPlaceInfoCardTitleHeight = 24.w;
    geoPlaceInfoCardGeoInfoHeight = 18.w;
    geoPlaceInfoCardStatusHeight = 25.w;
    geoPlaceInfoCardTagHeight = 16.w;
    geoPlaceInfoCardCheckInButtonWidth = 100.w;
    geoPlaceInfoCardCheckInButtonHeight = 35.w;
    geoPlaceInfoCardCheckInButtonBorderRadius = 10.w;
    geoPlaceInfoCardFavorIconWidth = 16.w;
    geoPlaceInfoCardFavorIconHeight = 14.1.w;
    geoPlaceInfoCardFavorButtonSize = 35.w;
    geoPlaceInfoCardFavorButtonBorderRadius = 10.w;

    geoSearchTextButtonBgSize = 38.w;
    geoSearchTextButtonIconSize = 15.w;
    geoSearchTextFieldWidth = 384.w;
    geoSearchTextFieldHeight = 65.w;
    geoSearchTextFieldCircular = 16.w;
    geoSearchTextFieldPaddingHorizontal = 90.w;
    geoSearchTextFieldPaddingVertical = 15.w;
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../views/component/widgets/common_record_widget.dart';
import '../../component/widgets/common_text.dart';
import 'record_page.dart';

class RecordTypeDetailPage extends StatelessWidget {
  final RecordPageController controller;
  final String type;
  final String label;

  const RecordTypeDetailPage({
    super.key,
    required this.controller,
    required this.type,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    final allRecords = controller.allActivities.entries
        .expand((entry) => entry.value.map((record) {
      return {
        'date': entry.key,
        ...record,
      };
    }))
        .where((r) => r['type'] == type)
        .toList();

    allRecords.sort((a, b) {
      final aDate = DateFormat('yyyy-MM-dd').parse(a['date']!);
      final bDate = DateFormat('yyyy-MM-dd').parse(b['date']!);
      return bDate.compareTo(aDate);
    });

    return Scaffold(
      backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
      body: Stack(
        children: [
          _buildWhiteTopSection(), // ✅ 底部背景
          Column(
            children: [
              SizedBox(height: 16.w),
              _buildTopSection(controller), // ✅ 顶部内容
              SizedBox(height: 12.w),
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.only(bottom: 100.w),
                  itemCount: allRecords.length,
                  itemBuilder: (context, index) {
                    final r = allRecords[index];
                    final date = DateFormat('EEE dd MMM yyyy')
                        .format(DateFormat('yyyy-MM-dd').parse(r['date']!));
                    return CommonRecordWidget(
                      type: r['type'] ?? 'note',
                      time: '$date | ${r['time'] ?? ''}',
                      description: r['description'] ?? '',
                      theme: controller.theme,
                      onDelete: () {},
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWhiteTopSection() {
    return Container(
      height: 102.w,
      width: double.infinity,
      decoration: BoxDecoration(
        color: controller.theme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.only(bottomRight: Radius.circular(25.r)),
        boxShadow: [
          BoxShadow(
            color: controller.theme.themeData.colorScheme.onSurface.withOpacity(0.15),
            offset: const Offset(0, 0),
            blurRadius: 18,
          ),
        ],
      ),
    );
  }


  Widget _buildTopSection(RecordPageController controller) {
    return Container(
      height: 64.w,
      padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 12.w),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: Icon(Icons.arrow_back,
                color: controller.theme.themeData.colorScheme.onSurface),
          ),
          SizedBox(width: 8.w),
          Container(
            width: 1.w,
            height: 22.w,
            color: controller.theme.themeData.colorScheme.tertiary,
          ),
          SizedBox(width: 8.w),
          CommonText(
            "$label Record",
            controller.theme.textStyleExtension.userProfileSettingBody,
          ),
          const Spacer(),
          Obx(() => Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            decoration: BoxDecoration(
              color: controller.theme.themeData.colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: controller.selectedPet.value,
                icon: Icon(
                  Icons.keyboard_arrow_down,
                  color: controller.theme.themeData.colorScheme.tertiary,
                ),
                dropdownColor: controller.theme.themeData.colorScheme.surface,
                items: controller.pets.map((pet) {
                  return DropdownMenuItem<String>(
                    value: pet.name,
                    child: Row(
                      children: [
                        FutureBuilder<Widget?>(
                          future: controller.petAvatarFutures[
                          controller.petList!
                              .firstWhere((p) => p.name == pet.name)
                              .sid!],
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return SizedBox(width: 20.w, height: 20.w);
                            } else if (snapshot.hasError ||
                                snapshot.data == null) {
                              return CircleAvatar(
                                radius: 10.w,
                                backgroundImage: const AssetImage(
                                    "assets/images/default_pet.png"),
                              );
                            } else {
                              return SizedBox(
                                width: 20.w,
                                height: 20.w,
                                child: snapshot.data!,
                              );
                            }
                          },
                        ),
                        SizedBox(width: 8.w),
                        SizedBox(
                          width: 40.w,
                          child: CommonText(
                            pet.name,
                            controller.theme.textStyleExtension
                                .placeListItemMileage,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    controller.selectedPet.value = newValue;
                    controller.currentPetData.value = controller.pets
                        .firstWhere((pet) => pet.name == newValue);
                  }
                },
              ),
            ),
          )),
        ],
      ),
    );
  }
}

plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    // apply false to set not involve it immediately to avoid compile error
//    id "com.google.android.libraries.mapsplatform.secrets-gradle-plugin" version "2.0.1" apply false
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def googleMapApiKey = localProperties.getProperty('GOOGLE_MAP_API_KEY')

def kotlin_version = "2.1.10"
def firebase_bom_version = "33.10.0"
def desugaring_version = '2.1.5'
def play_services_base_version = "18.5.0"
def play_services_location_version = "21.3.0"
def mapsplatform_secret_version = "2.0.1"

android {
    namespace = "com.onenata.onenata_app"
    compileSdkVersion 35

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.onenata.onenata_app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 30
        targetSdkVersion 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders = [GOOGLE_MAP_API_KEY: googleMapApiKey]
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlin_version}"

    // Import the Firebase BoM
    implementation platform("com.google.firebase:firebase-bom:${firebase_bom_version}")

    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation("com.google.firebase:firebase-appcheck-playintegrity")
    implementation("com.google.firebase:firebase-appcheck-debug")
    implementation("com.google.firebase:firebase-messaging")
    implementation("com.google.firebase:firebase-auth")
    implementation("com.google.firebase:firebase-firestore")
    implementation("com.google.firebase:firebase-storage")

    // Add dependency for Play Services
//    implementation "com.google.android.libraries.mapsplatform.secrets-gradle-plugin:secrets-gradle-plugin:${mapsplatform_secret_version}"
    implementation "com.google.android.gms:play-services-base:${play_services_base_version}"
    implementation "com.google.android.gms:play-services-location:${play_services_location_version}"

    // coreLibraryDesugaring
    coreLibraryDesugaring "com.android.tools:desugar_jdk_libs:${desugaring_version}"
    implementation 'androidx.multidex:multidex:2.0.1'

    // Fix Android 12L crash
    implementation 'androidx.window:window:1.3.0'
    implementation 'androidx.window:window-java:1.3.0'
}

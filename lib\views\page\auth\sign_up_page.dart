import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  SignUpPageState createState() => SignUpPageState();
}

class SignUpPageState extends State<SignUpPage> {
  late Future<SignUpController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => SignUpController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SignUpController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;


            logger.i('Object ID: ${identityHashCode(controller.passwordInputController)}');

            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [

                      // Avatar Header
                      SizedBox(
                        height: controller.theme.layout.authAvatarHeaderLineHeight
                      ),

                      // Label
                      SizedBox(
                        height: 149.w,
                        child: Column(
                          children: [
                            Expanded(child: SizedBox.shrink()),
                            Obx(()=> AuthWidgetBuilder.buildLabel(
                                controller.theme,
                                title: controller.pageLabelTitle.value,
                                desc: controller.pageLabelDesc.value)
                            ),
                            SizedBox(height: 20.w,),
                          ],
                        )
                      ),

                      Expanded(
                        child: SingleChildScrollView(
                          padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom,
                          ),
                          controller: controller.scrollController,
                          child: Column(
                            children: [
                              // Phone input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPhone2(
                                  controller.theme, controller.usernameInputController,
                              )),
                              SizedBox(height: 20.w,),

                              // Verification code input box
                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxVerificationCode2(
                                  controller.theme, controller.passwordInputController,
                              )),

                              // Resend section
                              Obx(()=> (controller.isVerificationCodeSent.isTrue && controller.isPhoneNumberValid.isTrue) ? controller.isReSendEnabled.value ?
                                // Resend enabled
                                GestureDetector(
                                  onTap: () async {
                                    await controller.sendVerificationCodeOfPhoneNumber();
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      CommonText(
                                          'auth.button.resend'.t18,
                                          controller.theme.textStyleExtension.authPageResend
                                      ),
                                      SizedBox(width: 38.w,)
                                    ],
                                  ),

                                ):
                                // Resend disabled
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Obx(()=> CommonText(
                                        'auth.button.resend.in.second'.t19({
                                          'second': '${controller.remainedTime.value.toInt()}'
                                        }),
                                        controller.theme.textStyleExtension.authPageResendDisabled
                                    )),
                                    SizedBox(width: 38.w,)
                                  ],
                                ):
                                SizedBox(
                                  height: 50.w,
                                  child: SizedBox.shrink(),
                                )
                              ),

                              SizedBox(
                                height: (screenSize.height
                                    - controller.theme.layout.authAvatarHeaderLineHeight
                                    - 149.w
                                    - 140.w
                                    - 50.w
                                    - 160.w) < 0 ? 0 : (screenSize.height
                                    - controller.theme.layout.authAvatarHeaderLineHeight
                                    - 149.w
                                    - 140.w
                                    - 50.w
                                    - 160.w),
                              ),
                              // Expanded(child: SizedBox.shrink()),

                              SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.buttonController,
                                )),
                              SizedBox(height: 20.w,),

                              // Change to email
                              SizedBox(
                                height: 50.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.alterButtonController,
                                )),
                              SizedBox(height: 30.w,),
                            ],
                          )
                        ),
                      ),
                    ],
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
                ],
              ),
            );
          }
        });
  }
  //
  // Widget _buildWhiteSection() {
  //   return const CommonWhiteSection(
  //     width: 403,
  //     height: 639,
  //     top: 222,
  //     borderRadius: 85,
  //   );
  // }
  //
  // Widget _buildSignupText(SignUpPageController controller) {
  //   return Positioned(
  //     top: 274.w,
  //     left: 38.w,
  //     child: Text(
  //       'signup'.t18,
  //       style: controller.theme.textStyleExtension.authPageTitle,
  //     ),
  //   );
  // }
  //
  // Widget _buildPhoneLabel(SignUpPageController controller) {
  //   return Positioned(
  //     top: 307.w,
  //     left: 38.w,
  //     child: Text(
  //       'sign.up.phone'.t18,
  //       style: controller.theme.textStyleExtension.authPageDesc,
  //     ),
  //   );
  // }
  //
  // Widget _buildPhoneField(SignUpPageController controller) {
  //   return Positioned(
  //     top: 371.w,
  //     left: 38.w,
  //     child: Obx(
  //       () => CommonTextField(
  //         controller: controller.phoneController,
  //         hintText: 'phone'.t18,
  //         keyboardType: TextInputType.phone,
  //         inputFormatters: [
  //           FilteringTextInputFormatter.digitsOnly,
  //           LengthLimitingTextInputFormatter(10),
  //         ],
  //         textColor: controller.isPhoneNumberValid.value
  //             ? controller.theme.themeData.colorScheme.secondary
  //             : controller.theme.themeData.colorScheme.tertiary,
  //         borderColor: controller.isPhoneNumberValid.value
  //             ? controller.theme.themeData.colorScheme.secondary
  //             : controller.theme.themeData.colorScheme.tertiary,
  //         iconColor: controller.theme.themeData.colorScheme.secondary,
  //         onChanged: (value) {
  //           controller.isPhoneEntered.value = value.isNotEmpty;
  //           controller.updateButtonState();
  //         },
  //         onClear: () {
  //           controller.isPhoneEntered.value = false;
  //           controller.updateButtonState();
  //         },
  //       ),
  //     ),
  //   );
  // }
  //
  // Widget _buildCodeField(SignUpPageController controller) {
  //   return Positioned(
  //     top: 451.w,
  //     left: 38.w,
  //     child: Obx(
  //       () => CommonTextField(
  //         controller: controller.codeController,
  //         hintText: 'verification.code'.t18,
  //         keyboardType: TextInputType.number,
  //         isEnabled: controller.codeFieldEnabled.value,
  //         textColor: controller.isCodeValid.value
  //             ? controller.theme.themeData.colorScheme.secondary
  //             : controller.theme.themeData.colorScheme.error,
  //         borderColor: controller.isCodeValid.value
  //             ? (controller.isCodeEntered.value
  //                 ? controller.theme.themeData.colorScheme.secondary
  //                 : controller.theme.themeData.colorScheme.tertiary)
  //             : controller.theme.themeData.colorScheme.error,
  //         iconColor: controller.isCodeValid.value
  //             ? controller.theme.themeData.colorScheme.secondary
  //             : controller.theme.themeData.colorScheme.error,
  //         onChanged: (value) {
  //           controller.isCodeEntered.value = value.isNotEmpty;
  //           controller.updateButtonState();
  //         },
  //         onClear: () {
  //           controller.isCodeEntered.value = false;
  //           controller.updateButtonState();
  //         },
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildResendSection(SignUpPageController controller) {
  //   return Obx(() => controller.isCodeSent.value
  //       ? Positioned(
  //           top: 511.w,
  //           right: 38.w,
  //           child: controller.isReSendEnabled.value
  //               ? GestureDetector(
  //                   onTap: controller.isPhoneNumberValid.value
  //                       ? () {
  //                           controller.startCountdown();
  //                           controller.isCodeValid.value = true;
  //                           controller.updateButtonState();
  //                         }
  //                       : null,
  //                   child: Text(
  //                     'resend'.t18,
  //                     style: controller.isPhoneNumberValid.value
  //                         ? controller.theme.textStyleExtension.authPageResend
  //                         : controller.theme.textStyleExtension.authPageResendDisabled,
  //                     textAlign: TextAlign.center,
  //                   ),
  //                 )
  //               : Text(
  //                   'Resend code in ${controller.remainedTime.value}s',
  //                   style: controller.theme.textStyleExtension.authPageResendDisabled,
  //                   textAlign: TextAlign.center,
  //                 ),
  //         )
  //       : Container());
  // }
  //
  // Widget _buildInvalidCodeMessage(SignUpPageController controller) {
  //   return Obx(() => !controller.isCodeValid.value
  //       ? Positioned(
  //           top: 511.w,
  //           left: 38.w,
  //           child: Text(
  //             'verification.code.error'.t18,
  //             style: controller.theme.textStyleExtension.authPageInvalid,
  //             textAlign: TextAlign.center,
  //           ),
  //         )
  //       : Container());
  // }
  //
  // Widget _buildSubmitButton(SignUpPageController controller) {
  //   return Positioned(
  //     top: 721.w,
  //     left: 38.w,
  //     child: Obx(
  //       () => CommonButton(
  //         text: controller.buttonText.value,
  //         onPressed: controller.enableButton.value
  //             ? () {
  //                 if (!controller.isCodeSent.value) {
  //                   controller.isCodeSent.value = true;
  //                   controller.startCountdown();
  //                   controller.updateButtonState();
  //                 } else {
  //                   if (controller.codeController.text ==
  //                       controller.validCode) {
  //                     controller.resetState(); // Reset before navigation
  //                     Get.toNamed('/selectPet');
  //                   } else {
  //                     controller.isCodeValid.value = false;
  //                     controller.enableButton.value = false;
  //                     controller.updateButtonState();
  //                   }
  //                 }
  //               }
  //             : null,
  //         backgroundColor: controller.enableButton.value
  //             ? controller.theme.themeData.colorScheme.secondary
  //             : controller.theme.themeData.colorScheme.tertiary,
  //         textColor: controller.theme.themeData.colorScheme.onPrimary,
  //         borderColor: Colors.transparent,
  //         width: 327.w,
  //         height: 60.w,
  //       ),
  //     ),
  //   );
  // }
  //
  // Widget _buildBottomText(SignUpPageController controller) {
  //   return Positioned(
  //     top: 800.w,
  //     left: 105.w,
  //     child: GestureDetector(
  //       onTap: () {
  //         Get.toNamed('/signUpEmail');
  //       },
  //       child: Text(
  //         'change.to.email'.t18,
  //         textAlign: TextAlign.center,
  //         style: controller.theme.textStyleExtension.authPageChangeLogin,
  //       ),
  //     ),
  //   );
  // }
}

class SignUpController extends GetxController {

  // Services
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;

  final ScrollController scrollController = ScrollController();
  final _usernameKey = GlobalKey(debugLabel: 'usernameKey${DateTime.now().millisecondsSinceEpoch}');
  final _passwordKey = GlobalKey(debugLabel: 'passwordKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  //
  // final CommonButtonController buttonController = Get.put(CommonButtonController(), tag: 'sign-up-submit');
  // final CommonButtonController alterButtonController = Get.put(CommonButtonController(), tag: 'sign-up-alter');

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  // var buttonText = ''.obs;
  // var onPressed = Rx<AsyncCallback?>(null);

  var isVerificationCodeSent = false.obs;
  var isVerificationCodeValid = false.obs;
  var verificationId = ''.obs;
  var verificationCode = ''.obs;
  var isPhoneNumberValid = false.obs;
  var isReSendEnabled = false.obs;
  var remainedTime = 30.obs;
  var areaCode = '+1'.obs;
  late int phoneNumberValidLength = 10;
  Timer? timer;

  final String validCode = '123';

  // Theme plugin
  late ThemePlugin theme;

  Future<SignUpController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
        'auth.label.signup'.t18,
        theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
        'auth.label.signup.phone'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildPhoneNumberTextFieldController(
      theme, usernameInputBox.obs,
      globalKey: _usernameKey.obs,
      onCleared: onPhoneNumberCleared.obs,
      onChanged: isInputPhoneNumberValid.obs,
    );
    // Listen to the focus node
    usernameInputController.textFieldFocusNode.addListener(() {
      usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_usernameKey);
    });

    // build password input controller
    passwordInputController = AuthWidgetBuilder.buildVerificationCodeTextFieldController(
      theme, passwordInputBox.obs,
      globalKey: _passwordKey.obs,
      onCleared: onVerificationCodeCleared.obs,
      onChanged: isInputVerificationCodeValid.obs
    );
    // Listen to the focus node
    passwordInputController.textFieldFocusNode.addListener(() {
      passwordInputController.isFocused.value = passwordInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_passwordKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.send.code'.t18.obs,
      onPressed: sendVerificationCodeOfPhoneNumber.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.change.to.email'.t18.obs,
      onPressed: alter.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    if (areaCode.value == '+1') {
      phoneNumberValidLength = 10;
    }
    else if (areaCode.value == '+86') {
      phoneNumberValidLength = 11;
    }
    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
  }

  @override
  void dispose() {
    timer?.cancel();
    // scrollController.dispose();
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    buttonController.isInProgress.value = false;
    super.dispose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && (key == _usernameKey && usernameInputController.textFieldFocusNode.hasFocus ||
        key == _passwordKey && passwordInputController.textFieldFocusNode.hasFocus)) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  void startCountdown() {

    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainedTime.value == 0) {
        isReSendEnabled.value = true;
        timer.cancel();
      } else {
        remainedTime.value--;
      }
    });
  }

  bool isButtonEnabled() {

    // Click to verify code
    if (isVerificationCodeSent.value) {
      return isVerificationCodeValid.value;
    }
    // Click to send verification code
    else {
      return isPhoneNumberValid.value;
    }
  }

  Future<void> onPhoneNumberCleared(BuildContext context) async {
    await resetState();
  }

  Future<void> onVerificationCodeCleared(BuildContext context) async {

    // passwordInputBox.text = '';
    isVerificationCodeValid.value = false;
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    // buttonController.isEnabled.value = false;
    // buttonController.onPressed.value = verifyOtp;

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputPhoneNumberValid({required BuildContext context, required bool result}) async {
    isPhoneNumberValid.value = result;
    usernameInputController.hasMistake.value = isPhoneNumberValid.isFalse && usernameInputBox.text.length > phoneNumberValidLength;

    if (isPhoneNumberValid.isTrue) {
      buttonController.text = 'auth.button.send.code'.t18.obs;
      buttonController.onPressed = sendVerificationCodeOfPhoneNumber.obs;
      buttonController.isEnabled.value = true;
    } else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputVerificationCodeValid({required BuildContext context, required bool result}) async {
    isVerificationCodeValid.value = result;
    passwordInputController.hasMistake.value = isVerificationCodeValid.isFalse && passwordInputBox.text != '';

    if (isVerificationCodeValid.isTrue && isPhoneNumberValid.isTrue) {
      logger.d('show continue');
      buttonController.text = 'auth.button.verify.code'.t18.obs;
      buttonController.onPressed = verifyOtp.obs;
      buttonController.isEnabled.value = true;
    }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationCodeOfPhoneNumber() async {

    if (!isPhoneNumberValid.value) return;

    String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';
    // String phoneNumber = InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text);
    // String phoneNumber = kReleaseMode ? InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text) : '+***********';

    // Check existing
    UserAccount? userAccount = await userDao.getUserAccountById(phoneNumber: phoneNumber);
    if (userAccount != null) {
      Get.snackbar('auth.error.title'.t18, 'auth.error.number.exists'.t18);
      return;
    }

    // Check existing
    ServiceResponse<String> response = await authService.sendVerificationCodePhoneNumber(phoneNumber);

    if (response.code == 201) {

      // save user account
      await userService.createUserAccount(authService.currentUser.value!, AuthChannel.sms);

      // add auth record in Firestore and security storage
      await userService.recordAuthActivity(authService.currentUser.value!, AuthChannel.sms);

      // Create user account
      UserData data = UserData(
        uid: authService.userAccount.value!.sid!,
      );
      await userService.createUserData(data);

      // Login success
      await resetState();
      Get.offAll(RootPage());
      return;
    }
    else if (response.code == 200) {

      verificationId.value = response.data!;
      isReSendEnabled.value = true;
      isVerificationCodeSent.value = true;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.sent'.t18,
        theme.textStyleExtension.authPageNotification,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.isEnabled.value = true;
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;

      buttonController.text.value = 'auth.button.verify.code'.t18;
      buttonController.isEnabled.value = false;
      buttonController.onPressed.value = verifyOtp;

      startCountdown();
    }
    else if (response.code == 408) {

      verificationId.value = response.data!;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.timeout'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
    else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.desc'.t18);

      passwordInputBox.text = '';
      passwordInputController.obscureText.value = true;
      passwordInputController.hasMistake.value = false;
      passwordInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
  }

  Future<void> verifyOtp() async {

    String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';

    try {
      // Sign in the user with the sms code
      ServiceResponse<User> response = await authService.verifyCodeLogIn(phoneNumber, verificationId.value, passwordInputBox.text);

      if (response.code == 200) {

        // Update current user in auth service
        // authService.currentUser.value = response.data!;

        // save user account in Firestore and security storage
        await userService.createUserAccount(authService.currentUser.value!, AuthChannel.sms);

        // add auth record in Firestore and security storage
        await userService.recordAuthActivity(authService.currentUser.value!, AuthChannel.sms);

        // Create user data
        UserData data = UserData.create(
          uid: authService.userAccount.value!.sid!,
        );
        await userService.createUserData(data);

        await resetState();
        Get.offAll(RootPage());
        return;
      }
      else {

        pageLabelDesc.value = CommonText(
          'auth.error.code.invalid'.t18,
          theme.textStyleExtension.authPageWarning,
          width: 350.w,
          height: 50.w,
          maxLines: 2,
          softWrap: true,
        );
        //
        // passwordInputBox.text = '';
        // passwordInputController.isEnabled.value = true;
        // passwordInputController.obscureText.value = true;
        // passwordInputController.hasMistake.value = false;

        // isReSendEnabled.value = false;
        // buttonController.isEnabled.value = true;
        // isVerificationCodeSent.value = false;
        // onPressed.value = sendVerificationCodeOfPhoneNumber;
      }
    }
    catch (e) {
      isVerificationCodeValid.value = false;
    }
  }

  Future<void> alter() async {
    await resetState();
    Get.to(()=> SignUpEmailPage());
  }

  Future<void> resetState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.phone'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    passwordInputController.textFieldFocusNode.unfocus();

    // Reset states
    isPhoneNumberValid.value = false;
    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    isVerificationCodeSent.value = false;
    isVerificationCodeValid.value = false;

    // Button
    buttonController.text = 'auth.button.send.code'.t18.obs;
    buttonController.onPressed.value = sendVerificationCodeOfPhoneNumber;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }
}

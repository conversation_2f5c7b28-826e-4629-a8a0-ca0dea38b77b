import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_vaccine_record.g.dart';

@JsonSerializable()
class PetVaccineRecord{
  String sid;
  int time;
  String? vaccineName;
  String? location;
  int? expiredDate;
  String? batchNumber;
  List<String>? photos;
  String? notes;

  PetVaccineRecord({
    required this.sid,
    required this.time,
    this.vaccineName,
    this.location,
    this.expiredDate,
    this.batchNumber,
    this.photos,
    this.notes,
  });

  factory PetVaccineRecord.fromJson(Map<String, dynamic> json) => _$PetVaccineRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetVaccineRecordToJson(this);

  static create({
    required int time,
    String? vaccineName,
    String? location,
    int? expiredDate,
    String? batchNumber,
    List<String>? photos,
    String? notes,
  }) {

    return PetVaccineRecord(
      sid: uuid.v4(),
      time: time,
      vaccineName: vaccineName,
      location: location,
      expiredDate: expiredDate,
      batchNumber: batchNumber,
      photos: photos,
      notes: notes,
    );
  }

  static copyFrom(PetVaccineRecord other) {
    return PetVaccineRecord(
      sid: other.sid,
      notes: other.notes,
      time: other.time,
      vaccineName: other.vaccineName,
      expiredDate: other.expiredDate,
      location: other.location,
      batchNumber: other.batchNumber,
      photos: other.photos,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

enum SupportedLocales {

  enCa(0, "en-CA"),
  fr<PERSON>a(1, "fr-CA"),
  zhHansCn(2, "zh-Hans<PERSON><PERSON><PERSON>"),
  zh<PERSON><PERSON>TCn(3, "zh-Hant-CN"),
  ;

  final int key; // Order of the tab controller
  final String code; // Stored code in database
  const SupportedLocales(this.key, this.code);

  // Factory constructor to create a TestType object based on the code
  factory SupportedLocales.fromKey(int key) {
    return SupportedLocales.values.firstWhere((element) => element.key == key, orElse: () => throw ArgumentError('Invalid key: $key'));
  }
  factory SupportedLocales.fromCode(String code) {
    return SupportedLocales.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

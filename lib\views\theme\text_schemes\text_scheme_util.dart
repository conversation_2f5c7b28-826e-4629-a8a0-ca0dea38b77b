import 'package:flutter/material.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';

class TextThemeUtil {

  static TextStyle buildTextStyle({double? size, FontWeight? weight, Color? color, String? family}) {

    double fontSize = size ?? 20.sp;
    FontWeight fontWeight = weight ?? FontWeight.w400;
    String fontFamily = family ?? 'Manrope';
    Color fontColor = color ?? Colors.white;

    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: fontColor,
    );
  }
}

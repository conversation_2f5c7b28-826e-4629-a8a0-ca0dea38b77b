import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';

import 'auth_service.dart';
import 'service_response.dart';

class PetService extends GetxService {

  // Services
  final AuthService _authService = AuthService.instance;

  // Local storage
  final CloudStorage _cloudStorage = CloudStorage();
  final LocalStorage _localStorage = LocalStorage.instance;
  final SecureStorage _secureStorage = SecureStorage.instance;
  final Storage _storage = Storage.instance;

  final PetDao _petDao = PetDao();

  Future<PetService> init() async {

    return this;
  }

  // Firestore operations ------------------------------------------------------------
  Future<ServiceResponse<String>> addOwnedPet(Pet pet) async {

    if (pet.name == null || pet.name!.isEmpty) {

      logger.w('Pet name cannot be empty');
      return ServiceResponse(
        code: 400,
        msg: 'Pet name cannot be empty',
      );
    }

    if (pet.owner != _authService.userAccount.value!.sid!) {

      logger.e('Do not have permission to delete this pet');
      return ServiceResponse(
        code: 400,
        msg: 'You don\'t have permission to add pet to other user',
      );
    }

    if (_authService.ownedPets.value != null) {
      if (_authService.ownedPets.value!.any((p) => p.name == pet.name)) {
        logger.w('Pet with name ${pet.name} already exists');
        return ServiceResponse(
          code: 400,
          msg: 'Pet with name ${pet.name} already exists',
        );
      }
    }

    // Load origin pets for rollback
    List<Pet>? origin = [];
    if (_authService.ownedPets.value != null) {
      for(Pet p in _authService.ownedPets.value!) {
        origin.add(Pet.copyFrom(p));
      }
    }
    Pet? oDefaultPet = Pet.copyFrom(_authService.defaultPet.value);

    try {

      // Persist pet in the local storage
      List<Pet>? pets = _authService.ownedPets.value ?? [];
      pets.add(pet);
      await saveLocalOwnedPets(pets);
      await saveLocalDefaultPet(pet);
      _authService.ownedPets.value = pets.map<Pet>((p) => Pet.copyFrom(p)).toList();
      _authService.defaultPet.value = Pet.copyFrom(pet);

      // Persist pet in the firestore
      await _petDao.addPet(pet);

      logger.d('Success adding pet');
      return ServiceResponse(
        code: 200,
        msg: 'Success adding pet',
      );
    }
    catch(e) {

      if (origin.isEmpty) {
        await clearLocalOwnedPets();
        await clearLocalDefaultPet();
        _authService.ownedPets.value = null;
        _authService.defaultPet.value = null;
      } else {
        await saveLocalOwnedPets(origin);
        await saveLocalDefaultPet(oDefaultPet!);
        _authService.ownedPets.value = origin;
        _authService.defaultPet.value = oDefaultPet;
      }

      logger.e('Error adding pet: $e');

      return ServiceResponse(
        code: 500,
        msg: 'Error adding pet',
      );
    }

  }

  Future<ServiceResponse<String>> updateOwnedPet(Pet pet) async {

    if (_authService.ownedPets.value == null ||
        _authService.ownedPets.value!.isEmpty ||
        _authService.ownedPets.value!.any((p) { return p.sid == pet.sid; }) == false) {

      logger.w('Pet not found');
      return ServiceResponse(
        code: 400,
        msg: 'No pets with name ${pet.name} found',
      );
    }

    if (pet.owner != _authService.userAccount.value!.sid!) {

      logger.e('Do not have permission to delete this pet');
      return ServiceResponse(
        code: 400,
        msg: 'You don\'t have permission to update  pet',
      );
    }

    // Load origin pets for rollback
    List<Pet>? origin = [];
    if (_authService.ownedPets.value != null) {
      for(Pet p in _authService.ownedPets.value!) {
        origin.add(Pet.copyFrom(p));
      }
    }

    List<Pet>? latest = [];
    for(Pet p in _authService.ownedPets.value!) {
      if (p.name == pet.name) {
        latest.add(pet);
      } else {
        latest.add(p);
      }
    }

    try {

      // Persist pet in the local storage
      await saveLocalOwnedPets(latest);

      // Persist pet in the firestore
      await _petDao.updatePet(pet);

      logger.d('Success updating pet');
      return ServiceResponse(
        code: 200,
        msg: 'Success updating pet',
      );
    }
    catch(e) {
      if (origin.isEmpty) {
        _authService.ownedPets.value = null;
        await clearLocalOwnedPets();
      } else {
        _authService.ownedPets.value = origin;
        await saveLocalOwnedPets(_authService.ownedPets.value!);
      }

      logger.e('Error updating pet: $e');

      return ServiceResponse(
        code: 500,
        msg: 'Error updating pet',
      );
    }
  }

  Future<List<Pet>?> getOwnedPets(String uid) async {

    return await _petDao.getPetsByUser(
      uid: uid,
      isValid: true,
    );
    // if (_authService.ownedPets.value != null) {
    //   return _authService.ownedPets.value;
    // }
    // else {
    //   return await _petDao.getPetsByUser(uid);
    // }
  }

  /// Upload pet avatar to firebase storage and save into Document directory
  /// [uid] sid of user account
  /// [pid] sid of pet
  /// [image] file picked by image picker
  Future<ServiceResponse<String>> uploadPetAvatar({required String uid, required String pid, required XFile image}) async {

    try {
      String? fileType = FileUtil.getFileType(image.path);
      String path = FileUtil.buildUserResourcePath(
          MediaType.petAvatar, uid, petId: pid);
      String name = fileType != null ? '${uuid.v4()}.$fileType' : uuid.v4();
      String filePath = '$path/$name';

      // Upload file to cloud storage
      Uint8List bytes = await image.readAsBytes();
      await _cloudStorage.uploadFile(filePath, bytes: bytes);

      // Save file at local document directory
      await _localStorage.saveImageToPath(
          path: path, filename: name, bytes: bytes);

      // Update pet if exists
      Pet? pet = await _petDao.getPetById(pid);
      if (pet != null) {
        pet.avatar = name;
        await updateOwnedPet(pet);
      }

      logger.d('Success uploading pet avatar');
      return ServiceResponse(
        code: 200,
        data: name,
        msg: 'Success uploading pet avatar',
      );
    }
    catch(e) {
      logger.e('Error uploading pet avatar: $e');

      return ServiceResponse(
        code: 500,
        msg: 'Error updating pet avatar',
      );
    }
  }

  /// Delete pet by owner
  Future<ServiceResponse<String>> deleteOwnedPet(String pid) async {

    // Check if pet exists

    if (_authService.ownedPets.value == null || _authService.ownedPets.value!.isEmpty) {

      logger.w('Pet not found');
      return ServiceResponse(
        code: 400,
        msg: 'Pet not found',
      );
    }

    if (_authService.ownedPets.value!.any((p) { return p.sid == pid && p.owner != _authService.userAccount.value!.sid!; })) {

      logger.e('Do not have permission to delete this pet');
      return ServiceResponse(
        code: 400,
        msg: 'You don\'t have permission to delete this pet',
      );
    }

    // Load origin pets for rollback
    List<Pet>? origin = [];
    if (_authService.ownedPets.value != null) {
      for(Pet p in _authService.ownedPets.value!) {
        origin.add(Pet.copyFrom(p));
      }
    }
    Pet? oDefaultPet = Pet.copyFrom(_authService.defaultPet.value);

    List<Pet>? latest = [];
    for(Pet p in _authService.ownedPets.value!) {
      if (p.sid != pid) {
        latest.add(p);
      }
    }

    try {
      // update local storage
      if (latest.isEmpty) {
        await clearLocalOwnedPets();
        await clearLocalDefaultPet();
      } else {
        await saveLocalOwnedPets(latest);
        await saveLocalDefaultPet(latest.first);
        _authService.ownedPets.value = latest;
        _authService.defaultPet.value = latest.first;
      }

      // delete from firestore
      await _petDao.deletePet(pid);

      logger.d('Success deleting pet');
      return ServiceResponse(
        code: 200,
        msg: 'Success deleting pet',
      );
    }
    catch(e) {

      await saveLocalOwnedPets(origin);
      await saveLocalDefaultPet(oDefaultPet!);
      _authService.ownedPets.value = origin;
      _authService.defaultPet.value = oDefaultPet;

      logger.e('Error deleting pet: $e');

      return ServiceResponse(
        code: 500,
        msg: 'Error deleting pet',
      );
    }
  }

  Future<List<Pet>?> getNearbyPets({
    required String uid,
    required GeoPoint center,
    double? radius = 2, // in km
    String? field = 'latest_location', // the location property name
    int? pageSize = 20,
    int? lastCreateDate,
    bool? isValid = true,
  }) async{
    return await _petDao.getNearbyPets(
      uid: uid,
      center: center,
      radius: radius,
      field: field,
      pageSize: pageSize,
      lastCreateDate: lastCreateDate,
      isValid: isValid
    );
  }

  Future<List<Pet>?> getNearbyPetsInRectangle({
    required String uid,
    required GeoPoint southwest,
    required GeoPoint northeast,
    String? field = 'latest_location', // the location property name
    int? pageSize = 20,
    int? lastCreateDate,
    bool? isValid = true,
  }) async{

    // Calculate the center point and radius of the outer circle of the rectangle
    final diagonalDistance = GeoFirePoint(southwest).distanceBetweenInKm(geopoint: northeast);
    final radius = diagonalDistance / 2;
    double lat = (southwest.latitude + northeast.latitude) / 2;
    double lng = (southwest.longitude + northeast.longitude) / 2;
    GeoPoint center = GeoPoint(lat, lng);

    return await _petDao.getNearbyPets(
        uid: uid,
        center: center,
        radius: radius,
        field: field,
        pageSize: pageSize,
        lastCreateDate: lastCreateDate,
        isValid: isValid
    );
  }

  // Pet geo record operations ---------------------------------------------------
  Future<PetGeoRecord?> getLatestPetGeoRecordById (String pid) async {
    return await _petDao.getLatestPetGeoRecordByPet(
      pid: pid,
      isValid: true,
    );
  }

  Future<ServiceResponse<String>> recordPetGeoLocation(PetGeoRecord record) async {

    try {
      await _petDao.addPetGeoRecord(record);
      logger.d('Success recording pet geo location');
      return ServiceResponse(
        code: 200,
        msg: 'Success recording pet growing',
      );
    }
    catch(e) {
      logger.e('Error recording pet geo location: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error recording pet growing',
      );
    }
  }

  Future<ServiceResponse<String>> deletePetGeoRecord(String pid, String recordId) async {

    try {
      await _petDao.deletePetGeoRecord(pid, recordId);
      logger.d('Success deleting pet geo location record');
      return ServiceResponse(
        code: 200,
        msg: 'Success deleting pet growing record',
      );
    }
    catch(e) {
      logger.e('Error deleting pet geo location record: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error deleting pet growing record',
      );
    }
  }


  Future<PetGrowingRecord?> getLatestPetGrowingRecordById (String pid) async {
    return await _petDao.getLatestPetGrowingRecordByPet(pid: pid);
  }

  Future<ServiceResponse<String>> recordPetGrowing(PetGrowingRecord record) async {

    try {
      await _petDao.addPetGrowingRecord(record);
      logger.d('Success recording pet growing');
      return ServiceResponse(
        code: 200,
        msg: 'Success recording pet growing',
      );
    }
    catch(e) {
      logger.e('Error recording pet growing: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error recording pet growing',
      );
    }
  }

  Future<ServiceResponse<String>> deletePetGrowingRecord(String pid, String recordId) async {

    try {
      await _petDao.deletePetGrowingRecord(pid, recordId);
      logger.d('Success deleting pet growing record');
      return ServiceResponse(
        code: 200,
        msg: 'Success deleting pet growing record',
      );
    }
    catch(e) {
      logger.e('Error deleting pet growing record: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error deleting pet growing record',
      );
    }
  }


  Future<ServiceResponse<String>> recordPetFeeding(String petSid, PetFeedingRecord record) async {
    try {
      await _petDao.addPetFeedingRecordToCollection(petId: petSid, record: record); // 改成子collection写入
      logger.d('Success recording pet feeding');
      return ServiceResponse(
        code: 200,
        msg: 'Success recording pet feeding',
      );
    } catch (e) {
      logger.e('Error recording pet feeding: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error recording pet feeding',
      );
    }
  }

  Future<ServiceResponse<String>> deletePetFeedingRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetFeedingRecordFromCollection(petId: petSid, recordSid: recordSid);
      logger.d('Success deleting pet feeding record');
      return ServiceResponse(
        code: 200,
        msg: 'Success deleting pet feeding record',
      );
    } catch (e) {
      logger.e('Error deleting pet feeding record: $e');
      return ServiceResponse(
        code: 500,
        msg: 'Error deleting pet feeding record',
      );
    }
  }

  Future<List<PetFeedingRecord>> getPetFeedingRecords(String petSid) async {
    return await _petDao.getPetFeedingRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetFeedingRecord(String petSid, PetFeedingRecord record) async {
    try {
      await _petDao.updatePetFeedingRecordInCollection(petId: petSid, updatedRecord: record);
      logger.d('Success updating pet feeding record');
      return ServiceResponse(code: 200, msg: 'Success updating pet feeding record');
    } catch (e) {
      logger.e('Error updating pet feeding record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet feeding record');
    }
  }



  Future<ServiceResponse<String>> recordPetPoop(String petSid, PetPoopRecord record) async {
    try {
      await _petDao.addPetPoopRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet poop');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet poop');
    }
  }

  Future<ServiceResponse<String>> deletePetPoopRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetPoopRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet poop record');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet poop record');
    }
  }

  Future<List<PetPoopRecord>> getPetPoopRecords(String petSid) async {
    return await _petDao.getPetPoopRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetPoopRecord(String petSid, PetPoopRecord record) async {
    try {
      await _petDao.updatePetPoopRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet poop record');
      return ServiceResponse(code: 200, msg: 'Success updating pet poop record');
    } catch (e) {
      logger.e('Error updating pet poop record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet poop record');
    }
  }



  Future<ServiceResponse<String>> recordPetWalking(String petSid, PetWalkingRecord record) async {
    try {
      await _petDao.addPetWalkingRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet walking');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet walking');
    }
  }

  Future<ServiceResponse<String>> deletePetWalkingRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetWalkingRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet walking record');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet walking record');
    }
  }

  Future<List<PetWalkingRecord>> getPetWalkingRecords(String petSid) async {
    return await _petDao.getPetWalkingRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetWalkingRecord(String petSid, PetWalkingRecord record) async {
    try {
      await _petDao.updatePetWalkingRecordInCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success updating pet walking record');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error updating pet walking record');
    }
  }


  Future<ServiceResponse<String>> recordPetSocial(String petSid, PetSocialRecord record) async {
    try {
      await _petDao.addPetSocialRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet social');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet social');
    }
  }

  Future<ServiceResponse<String>> deletePetSocialRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetSocialRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet social record');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet social record');
    }
  }

  Future<List<PetSocialRecord>> getPetSocialRecords(String petSid) async {
    return await _petDao.getPetSocialRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetSocialRecord(String petSid, PetSocialRecord record) async {
    try {
      await _petDao.updatePetSocialRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet social record');
      return ServiceResponse(code: 200, msg: 'Success updating pet social record');
    } catch (e) {
      logger.e('Error updating pet social record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet social record');
    }
  }


  Future<ServiceResponse<String>> recordPetMedicine(String petSid, PetMedicineRecord record) async {
    try {
      await _petDao.addPetMedicineRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet medicine');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet medicine');
    }
  }

  Future<ServiceResponse<String>> deletePetMedicineRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetMedicineRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet medicine');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet medicine');
    }
  }

  Future<List<PetMedicineRecord>> getPetMedicineRecords(String petSid) async {
    return await _petDao.getPetMedicineRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetMedicineRecord(String petSid, PetMedicineRecord record) async {
    try {
      await _petDao.updatePetMedicineRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet medicine record');
      return ServiceResponse(code: 200, msg: 'Success updating pet medicine record');
    } catch (e) {
      logger.e('Error updating pet medicine record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet medicine record');
    }
  }


  Future<ServiceResponse<String>> recordPetVaccine(String petSid, PetVaccineRecord record) async {
    try {
      await _petDao.addPetVaccineRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet vaccine');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet vaccine');
    }
  }

  Future<ServiceResponse<String>> deletePetVaccineRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetVaccineRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet vaccine');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet vaccine');
    }
  }

  Future<List<PetVaccineRecord>> getPetVaccineRecords(String petSid) async {
    return await _petDao.getPetVaccineRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetVaccineRecord(String petSid, PetVaccineRecord record) async {
    try {
      await _petDao.updatePetVaccineRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet vaccine record');
      return ServiceResponse(code: 200, msg: 'Success updating pet vaccine record');
    } catch (e) {
      logger.e('Error updating pet vaccine record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet vaccine record');
    }
  }


  Future<ServiceResponse<String>> recordPetGrooming(String petSid, PetGroomingRecord record) async {
    try {
      await _petDao.addPetGroomingRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet grooming');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet grooming');
    }
  }

  Future<ServiceResponse<String>> deletePetGroomingRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetGroomingRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet grooming');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet grooming');
    }
  }

  Future<List<PetGroomingRecord>> getPetGroomingRecords(String petSid) async {
    return await _petDao.getPetGroomingRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetGroomingRecord(String petSid, PetGroomingRecord record) async {
    try {
      await _petDao.updatePetGroomingRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet grooming record');
      return ServiceResponse(code: 200, msg: 'Success updating pet grooming record');
    } catch (e) {
      logger.e('Error updating pet grooming record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet grooming record');
    }
  }


  Future<ServiceResponse<String>> recordPetDaycare(String petSid, PetDaycareRecord record) async {
    try {
      await _petDao.addPetDaycareRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet daycare');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet daycare');
    }
  }

  Future<ServiceResponse<String>> deletePetDaycareRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetDaycareRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet daycare');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet daycare');
    }
  }

  Future<List<PetDaycareRecord>> getPetDaycareRecords(String petSid) async {
    return await _petDao.getPetDaycareRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetDaycareRecord(String petSid, PetDaycareRecord record) async {
    try {
      await _petDao.updatePetDaycareRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet daycare record');
      return ServiceResponse(code: 200, msg: 'Success updating pet daycare record');
    } catch (e) {
      logger.e('Error updating pet daycare record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet daycare record');
    }
  }


  Future<ServiceResponse<String>> recordPetVet(String petSid, PetVetRecord record) async {
    try {
      await _petDao.addPetVetRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet vet');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet vet');
    }
  }

  Future<ServiceResponse<String>> deletePetVetRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetVetRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet vet');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet vet');
    }
  }

  Future<List<PetVetRecord>> getPetVetRecords(String petSid) async {
    return await _petDao.getPetVetRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetVetRecord(String petSid, PetVetRecord record) async {
    try {
      await _petDao.updatePetVetRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet vet record');
      return ServiceResponse(code: 200, msg: 'Success updating pet vet record');
    } catch (e) {
      logger.e('Error updating pet vet record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet vet record');
    }
  }


  Future<ServiceResponse<String>> recordPetOther(String petSid, PetOtherRecord record) async {
    try {
      await _petDao.addPetOtherRecordToCollection(petId: petSid, record: record);
      return ServiceResponse(code: 200, msg: 'Success recording pet other');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error recording pet other');
    }
  }

  Future<ServiceResponse<String>> deletePetOtherRecord(String petSid, String recordSid) async {
    try {
      await _petDao.deletePetOtherRecordFromCollection(petId: petSid, recordSid: recordSid);
      return ServiceResponse(code: 200, msg: 'Success deleting pet other');
    } catch (e) {
      return ServiceResponse(code: 500, msg: 'Error deleting pet other');
    }
  }

  Future<List<PetOtherRecord>> getPetOtherRecords(String petSid) async {
    return await _petDao.getPetOtherRecordsFromCollection(petSid);
  }

  Future<ServiceResponse<String>> updatePetOtherRecord(String petSid, PetOtherRecord record) async {
    try {
      await _petDao.updatePetOtherRecordInCollection(petId: petSid, record: record);
      logger.d('Success updating pet other record');
      return ServiceResponse(code: 200, msg: 'Success updating pet other record');
    } catch (e) {
      logger.e('Error updating pet other record: $e');
      return ServiceResponse(code: 500, msg: 'Error updating pet other record');
    }
  }

  Future<String?> uploadRecordImage({
    required String uid,
    required String pid,
    required String recordType,
    required XFile image,
  }) async {
    try {
      // 获取文件扩展名（如 jpg、png）
      String? fileType = FileUtil.getFileType(image.path);
      if (fileType == null) return null;

      // 构建路径：users/{uid}/pets/{pid}/{recordType}/{uuid}.{ext}
      String path = 'users/$uid/pets/$pid/$recordType';
      String name = '${uuid.v4()}.$fileType';
      String filePath = '$path/$name';

      // 读取文件内容
      Uint8List bytes = await image.readAsBytes();

      // 上传到 Firebase Storage
      await _cloudStorage.uploadFile(filePath, bytes: bytes);

      // 可选：保存到本地缓存
      await _localStorage.saveImageToPath(
        path: path,
        filename: name,
        bytes: bytes,
      );

      // 返回文件名供记录使用（只保存相对路径或文件名）
      return name;
    } catch (e) {
      logger.e('Error uploading record image: $e');
      return null;
    }
  }


  Future<List<PetWalkingRecord>> getTodayPetWalkingRecords(String petId) async {
    final allRecords = await _petDao.getPetWalkingRecordsFromCollection(petId);

    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return allRecords.where((record) {
      final startTime = DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0);
      return startTime.isAfter(todayStart) && startTime.isBefore(todayEnd);
    }).toList();
  }

  // Local storage operations -----------------------------------------------------------
  Future<void> saveLocalOwnedPets(List<Pet> pets) async {
    await _storage.write(StorageKeys.ownedPets, jsonEncode(pets.map((pet) => pet.toJson()).toList()));
  }
  Future<void> clearLocalOwnedPets() async {
    await _storage.remove(StorageKeys.ownedPets);
  }
  Future<void> saveLocalDefaultPet(Pet pet) async {
    await _storage.write(StorageKeys.defaultPet, jsonEncode(pet.toJson()));
  }
  Future<void> clearLocalDefaultPet() async {
    await _storage.remove(StorageKeys.defaultPet);
  }


}

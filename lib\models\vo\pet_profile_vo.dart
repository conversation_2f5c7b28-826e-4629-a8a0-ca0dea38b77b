class PetProfileVO {
  final String name;
  final String image;
  final String type;
  final String breed;
  final String gender;
  final String weight;
  final String birthday;
  final String home;
  final String nextVaccine;

  PetProfileVO({
    required this.name,
    required this.image,
    required this.type,
    required this.breed,
    required this.gender,
    required this.weight,
    required this.birthday,
    required this.home,
    required this.nextVaccine,
  });

  factory PetProfileVO.empty() {
    return PetProfileVO(
      name: "Unknown",
      image: "assets/images/default_pet.png",
      type: "unknown",
      breed: "unknown",
      gender: "unknown",
      weight: "unknown",
      birthday: "unknown",
      home: "unknown",
      nextVaccine: "N/A",
    );
  }
}

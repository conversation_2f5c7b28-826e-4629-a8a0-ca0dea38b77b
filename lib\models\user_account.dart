import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'user_account.g.dart';

@JsonSerializable()
class UserAccount extends BaseFullModel {

  String fid; // Firebase user id
  String? phoneNumber;
  String? email;
  String? salt;
  String? hashedCredential;
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isEmailVerified;

  UserAccount({
    required this.fid,
    this.phoneNumber,
    this.email,
    this.salt,
    this.hashedCredential,
    this.isEmailVerified,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory UserAccount.fromJson(Map<String, dynamic> json) => _$UserAccountFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$UserAccountToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory UserAccount.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return UserAccount.fromJson(jsonData);
  }

  factory UserAccount.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return UserAccount.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'user_account';

  static create ({
    required String fid,
    String? phoneNumber,
    String? email,
    String? salt,
    String? hashedCredential,
    bool? isEmailVerified,
  }) {

    return UserAccount(
      sid: uuid.v4(),
      fid: fid,
      phoneNumber: phoneNumber,
      email: email,
      salt: salt,
      hashedCredential: hashedCredential,
      isEmailVerified: isEmailVerified,
      isValid: true,
    );
  }

  static copyFrom(UserAccount other) {
    return UserAccount(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      fid: other.fid,
      phoneNumber: other.phoneNumber,
      email: other.email,
      salt: other.salt,
      hashedCredential: other.hashedCredential,
      isEmailVerified: other.isEmailVerified,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'staff_holiday.g.dart';

/// Staff Holiday - 员工休假
/// 存储在 Firestore collection "staff-holiday"
@JsonSerializable()
class StaffHoliday extends BaseFullModel {
  String storeId;                    // 店铺ID
  String staffId;                    // 员工ID
  String holidayId;                  // 休假唯一ID
  String title;                      // 休假标题
  String startDate;                  // 开始日期 YYYY-MM-DD
  String endDate;                    // 结束日期 YYYY-MM-DD
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isFullDay;                   // 是否全天
  String? startTime;                 // 开始时间 HH:MM
  String? endTime;                   // 结束时间 HH:MM
  String? reason;                    // 休假原因
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isRecurring;                 // 是否循环
  String? recurringPattern;          // 循环模式 (weekly, monthly, yearly)
  @JsonKey(fromJson: JsonUtil.boolFromJson, toJson: JsonUtil.boolToJson)
  bool? isApproved;                  // 是否已批准
  String? approvedBy;                // 批准人ID
  int? approvedAt;                   // 批准时间（毫秒时间戳）

  StaffHoliday({
    required this.storeId,
    required this.staffId,
    required this.holidayId,
    required this.title,
    required this.startDate,
    required this.endDate,
    required this.isFullDay,
    this.startTime,
    this.endTime,
    this.reason,
    required this.isRecurring,
    this.recurringPattern,
    required this.isApproved,
    this.approvedBy,
    this.approvedAt,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory StaffHoliday.fromJson(Map<String, dynamic> json) =>
      _$StaffHolidayFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$StaffHolidayToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  static String get collection => 'staff-holiday';

  bool isActive() {
    final today = DateTime.now().toIso8601String().split('T')[0];
    return (isApproved ?? false) &&
           startDate.compareTo(today) <= 0 &&
           endDate.compareTo(today) >= 0;
  }

  bool conflictsWith(String date, {String? startTime, String? endTime}) {
    if (date.compareTo(this.startDate) < 0 || date.compareTo(this.endDate) > 0) {
      return false;
    }

    if (isFullDay ?? false) {
      return true;
    }

    if (startTime == null || endTime == null || this.startTime == null || this.endTime == null) {
      return false;
    }

    return !(endTime.compareTo(this.startTime!) <= 0 || startTime.compareTo(this.endTime!) >= 0);
  }
}

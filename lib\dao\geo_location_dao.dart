import 'dart:async';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';

class GeoLocationDao {

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collection reference
  final CollectionReference _gfpCollection = FirebaseFirestore.instance.collection(GeoFavorPlace.collection);

  // user data ----------------------------------------------
  Future<void> addGeoFavorPlace (GeoFavorPlace gfp) async {

    DocumentReference gfpRef = _gfpCollection.doc(gfp.sid);
    Map<String, dynamic> dataDoc = gfp.toFirestoreData();
    dataDoc[CommonField.createDate] = DateTimeUtil.currentMilliseconds();

    await gfpRef.set(dataDoc);
  }

  Future<void> updateGeoFavorPlace (GeoFavorPlace gfp, {bool mergeOption = false}) async {

    DocumentReference gfpRef = _gfpCollection.doc(gfp.sid!);
    Map<String, dynamic> dataDoc = gfp.toFirestoreData();
    dataDoc[CommonField.updateDate] = DateTimeUtil.currentMilliseconds();

    await gfpRef.set(dataDoc, SetOptions(merge: mergeOption));
  }

  Future<void> deleteGeoFavorPlace (String gfpId) async {

    DocumentReference gfpRef = _gfpCollection.doc(gfpId);
    await gfpRef.delete();
  }

  Future<void> softDeleteUserData (String gfpId) async {

    DocumentReference gfpRef = _gfpCollection.doc(gfpId);
    await gfpRef.set({CommonField.isValid: 0});
  }

  Future<GeoFavorPlace?> getGeoFavorPlaceById (String gfpId) async {

    DocumentSnapshot doc = await _gfpCollection.doc(gfpId).get();

    if (doc.exists) {
      return GeoFavorPlace.fromFirestoreData(doc);
    } else {
      return null;
    }
  }

  Future<List<GeoFavorPlace>?> getGeoFavorPlaceList ({String? uid, GeoPlaceFilterType? type}) async {

    Query query = _gfpCollection;
    if (uid != null) {
      query = query.where('uid', isEqualTo: uid);
    }

    if (type != null) {
      query = query.where('filter_type', isEqualTo: type.code);
    }
    QuerySnapshot querySnapshot = await query.get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.map((p)=> GeoFavorPlace.fromFirestoreData(p)).toList();
    } else {
      return null;
    }
  }

}

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Load environment variables from .env files
/// The data access point should be in each const class
class Env {

  // Environment switch
  // static String get envFile => kReleaseMode ? ".env.prod" : ".env.local";
  static String get envFile => kReleaseMode ? ".env.prod" : ".env.dev";

  static String get firebaseEmulatorToken => dotenv.env['FIREBASE_EMULATOR_TOKEN'] ?? '';

  // Load environment variables
  static bool get firebaseEmulatorAuthEnabled => dotenv.env['FIREBASE_EMULATOR_AUTH_ENABLED']?.toLowerCase() == 'true' ? true : false;
  static String get firebaseEmulatorAuthHost => dotenv.env['FIREBASE_EMULATOR_AUTH_HOST'] ?? 'localhost';
  static int get firebaseEmulatorAuthPort => int.parse(dotenv.env['FIREBASE_EMULATOR_AUTH_PORT'] ?? '10099');

  static bool get firebaseEmulatorFirestoreEnabled => dotenv.env['FIREBASE_EMULATOR_FIRESTORE_ENABLED']?.toLowerCase() == 'true' ? true : false;
  static String get firebaseEmulatorFirestoreHost => dotenv.env['FIREBASE_EMULATOR_FIRESTORE_HOST'] ?? 'localhost';
  static int get firebaseEmulatorFirestorePort => int.parse(dotenv.env['FIREBASE_EMULATOR_FIRESTORE_PORT'] ?? '10088');

  static bool get firebaseEmulatorStorageEnabled => dotenv.env['FIREBASE_EMULATOR_STORAGE_ENABLED']?.toLowerCase() == 'true' ? true : false;
  static String get firebaseEmulatorStorageHost => dotenv.env['FIREBASE_EMULATOR_STORAGE_HOST'] ?? 'localhost';
  static int get firebaseEmulatorStoragePort => int.parse(dotenv.env['FIREBASE_EMULATOR_STORAGE_PORT'] ?? '10089');
  static String get firebaseStorageHost => dotenv.env['FIREBASE_STORAGE_HOST'] ?? 'http://localhost:10089';

  static String get gmApiBaseUrl => dotenv.env['GOOGLE_MAP_API_BASE_URL'] ?? '';
  static String get gmApiKey => dotenv.env['GOOGLE_MAP_API_KEY'] ?? '';
  static String get gmStyleId => dotenv.env['GOOGLE_MAP_STYLE_ID'] ?? '4f3e2209bfb9ea52';
}

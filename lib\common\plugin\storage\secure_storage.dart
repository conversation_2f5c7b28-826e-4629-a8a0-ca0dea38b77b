import 'dart:convert';
import 'dart:core';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class SecureStorage extends GetxService{

  static SecureStorage get instance => Get.find();

  // Sensitive storage
  final AndroidOptions _getAndroidOptions = const AndroidOptions(
    encryptedSharedPreferences: true,
  );

  // Unlock option for iOS
  final options = const IOSOptions(accessibility: KeychainAccessibility.first_unlock);
  // secureStorage.write(key: 'key', value: 'value', iOptions: options);

  late FlutterSecureStorage secureStorage;

  @override
  void onInit() {

    super.onInit();

    secureStorage = FlutterSecureStorage(aOptions: _getAndroidOptions);
  }

  Future<bool> hasData(String key) async {
    return await secureStorage.containsKey(key: key);
  }

  Future<void> write(String key, String value) async {
    await secureStorage.write(key: key, value: value);
  }

  Future<dynamic> read(String key) async {
    return await secureStorage.read(key: key);
  }

  Future<Map<String, String>?> readAll(String key) async {
    return await secureStorage.readAll();
  }

  Future<void> delete(String key) async {
    await secureStorage.delete(key: key);
  }

  Future<void> deleteAll() async {
    await secureStorage.deleteAll();
  }
}

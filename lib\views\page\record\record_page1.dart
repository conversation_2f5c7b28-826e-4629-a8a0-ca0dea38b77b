import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/page/page_interceptor.dart';

class RecordPage extends StatefulWidget {
  const RecordPage({super.key});

  @override
  RecordPageState createState() => RecordPageState();
}

class RecordPageState extends State<RecordPage> {

  // Declare your controller here
  late Future<RecordPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => RecordPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {

    return FutureBuilder<RecordPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
              theme: controller.theme,
              // titleBar: AuthWidgetBuilder.buildAuthBackButton(controller.theme, context),
              titleBar: AppBar(
                // TRY THIS: Try changing the color here to a specific color (to
                // Colors.amber, perhaps?) and trigger a hot reload to see the AppBar
                // change color while the other colors stay the same.
                backgroundColor: Theme.of(context).colorScheme.inversePrimary,
                // Here we take the value from the MyHomePage object that was created by
                // the App.build method, and use it to set our appbar title.
                title: Text(
                  'Record page',
                  // 'app.title'.t18,
                  style: controller.theme.themeData.textTheme.labelSmall,
                ),
                leading: AuthWidgetBuilder.buildBackButton(controller.theme, context),
              ),
              body: Stack(
                children: [
                  Center(
                    // Center is a layout widget. It takes a single child and positions it
                    // in the middle of the parent.
                    child: Column(
                      // Column is also a layout widget. It takes a list of children and
                      // arranges them vertically. By default, it sizes itself to fit its
                      // children horizontally, and tries to be as tall as its parent.
                      //
                      // Column has various properties to control how it sizes itself and
                      // how it positions its children. Here we use mainAxisAlignment to
                      // center the children vertically; the main axis here is the vertical
                      // axis because Columns are vertical (the cross axis would be
                      // horizontal).
                      //
                      // TRY THIS: Invoke "debug painting" (choose the "Toggle Debug Paint"
                      // action in the IDE, or press "p" in the console), to see the
                      // wireframe for each widget.
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          'You have pushed the button this many times:',
                          style: controller.theme.textStyleExtension.petAvatarMore,
                        ),
                        Obx(() => Text(
                          '${controller.counter.value}',
                          style: Theme.of(context).textTheme.headlineMedium,
                        )),
                        AuthWidgetBuilder.buildInputBoxEmail(controller.theme, controller.emailInputBox, controller.emailInputController),
                        SizedBox(height: 10.w,),
                        AuthWidgetBuilder.buildInputBoxPassword(controller.theme, controller.passwordInputBox, controller.passwordInputController),
                      ],

                    ),
                  ),

                  FloatingActionButton(
                    backgroundColor: controller.theme.colorExtension.geoFavor,
                    onPressed: controller.testInputBox,
                    tooltip: 'Increment',
                    child: const Icon(Icons.add),
                  ),


                  // This trailing comma makes auto-formatting nicer for build methods.
                ],
              ),
              // bottomBar: ,
            );
          }
        });
  }
}

class RecordPageController extends GetxController {

  final UserDao userDao = UserDao();
  final AuthService authService = AuthService.instance;
  final UserService userService = UserService();
  final CloudStorage cloudStorage = CloudStorage();

  // Theme plugin
  late ThemePlugin theme;

  // Variables
  final RxInt counter = 0.obs;


  // TextEditingController
  final TextEditingController emailInputBox = TextEditingController();
  final CommonTextFieldController emailInputController = Get.put(CommonTextFieldController(), tag: 'email');
  final TextEditingController passwordInputBox = TextEditingController();
  final CommonTextFieldController passwordInputController = Get.put(CommonTextFieldController(), tag: 'password');

  // Async initialization
  Future<RecordPageController> init() async {

    // Validate auth state
    PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    String? fcmToken = await FirebaseMessaging.instance.getToken();

    return this;
  }

  Future<void> testDataAccess() async {
    counter.value += 1;

    // test sign up
    String email = '<EMAIL>';
    String password = "*********";

    // ServiceResponse<User> response = await userService.signUpWithEmailAndPassword(email, password);

    // ServiceResponse<User> response = await userService.loginInWithEmailAndPassword(email, password);
    ServiceResponse<User> response = await authService.getCurrentSignUpUser();
    if (response.code != 200) {
      // ServiceResponse<User> res1 = await userService.loginInWithEmailAndPassword(email, password);
      ServiceResponse<User> res1 = await authService.signUpWithEmailAndPassword(email, password);
    } else {
      ServiceResponse<User> res2 = await authService.loginInWithEmailAndPassword(email, password);
    }

    ServiceResponse<User> res2 = await authService.getCurrentSignUpUser();

    UserAccount acc = UserAccount.create(
      fid: res2.data!.uid,
      email: email,
      salt: 'salt',
      hashedCredential: '*********',
    );

    UserData data = UserData.create(
      uid: 'b6e4c05d-f066-4590-924f-55ffb1df8bf0',
      avatar: 'avatar',
    );

    UserAuthRecord record = UserAuthRecord.create(
      uid: 'b6e4c05d-f066-4590-924f-55ffb1df8bf0',
      authChannel: AuthChannel.emailPassword,
    );

    // Test Firestore ----------------------------------------------------------
    await userDao.addUserAccount(account: acc);
    // await userDao.addUserAccount(account: acc, data: data);
    // await userService.createUser(account: acc, data: data);
    // await userService.addUserData(data);
    // await userService.addUserAuthRecord(record);
    // await userDao.updateUserAccount('b6e4c05d-f066-4590-924f-55ffb1df8bf0', acc);
    // await userDao.softDeleteUserAccount('b6e4c05d-f066-4590-924f-55ffb1df8bf0');

    // Test Firebase Storage ---------------------------------------------------

    // upload image
    // XFile? image = await ImagePicker()
    //     .pickImage(source: ImageSource.gallery);
    // if (image != null) {
    //
    //   String fid = 'eGJtzlA7RqDNG9rRIEmPt0hjuGFA';
    //   String fileName = image.path.split('/').last;
    //   String filePath = '${FileUtil.buildUserResourcePath(MediaType.image, fid, pType: PublishType.post, publishId: '*********xyz')}/$fileName';
    //
    //   // Upload bytes
    //   // Uint8List bytes = await image.readAsBytes();
    //   // cloudStorage.uploadFile(filePath, bytes: bytes);
    //
    //   // Upload file
    //   // cloudStorage.uploadFile(filePath, file: File(image.path));
    // }

    // download image
    String filePath = 'users/eGJtzlA7RqDNG9rRIEmPt0hjuGFA/posts/*********xyz/media/Screenshot 2025-03-11 at 10.50.35 PM.png';
    String url = await cloudStorage.getDownloadURL(filePath);
    Get.dialog(
      Image.network(
        url,
        // fit: BoxFit.cover,
      ),
    );

    // For debug
    int i = 100;
  }

  Future<void> testInputBox() async {

    // String displayText = inputBox.text;

    // String text = InputUtil.toOriginPhoneNumber(phoneNumber: displayText);
    // String text = InputUtil.toOriginPhoneNumber(phoneNumber: displayText, areaCode: SupportedAreaCode.cn.code);

    // textFieldController.toggleMistakeState();

    emailInputController.toggleEnabled();
    emailInputBox.text = 'test';

    int code = 200;
  }
}

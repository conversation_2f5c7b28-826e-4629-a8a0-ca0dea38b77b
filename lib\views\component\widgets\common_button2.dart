import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import '../../../common/plugin/logger/logger.dart';
import 'common_bar.dart';

class CommonButton2 extends StatefulWidget {

  final CommonButtonController controller;
  final Widget? loadingWidget;
  final bool? isEnabled;

  // Button properties
  final double? width;
  final double? height;
  final Color? color;
  final Color? disabledColor;
  final String? text;
  final TextStyle? textStyle;
  final TextStyle? disabledTextStyle;

  // Radius
  final bool? hasRadius;
  final double? circular;

  // Border properties
  final bool? hasBorder;
  final Color? borderColor;
  final double? borderWidth;

  // Bg properties
  final bool? hasShadow;
  final Color? shadowColor;
  final Offset? shadowOffset;
  final double? shadowBlurRadius;
  final double? shadowSpreadRadius;

  // Callbacks
  final AsyncVoidCallback? onPressed;

  const CommonButton2({
    super.key,
    this.isEnabled = true,
    required this.controller,
    this.loadingWidget,
    this.width = 327,
    this.height = 60,
    this.color,
    this.disabledColor,
    this.text = '',
    this.textStyle,
    this.disabledTextStyle,

    this.hasRadius = true,
    this.circular = 30,

    this.hasBorder = false,
    this.borderColor = Colors.black12,
    this.borderWidth = 1,

    this.hasShadow = false,
    this.shadowColor = Colors.black12,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.shadowSpreadRadius,

    this.onPressed,
  });

  @override
  CommonButton2State createState() => CommonButton2State();
}

class CommonButton2State extends State<CommonButton2> {

  // Declare your controller here
  late Future<CommonButtonController> _controller;

  @override
  void initState() {

    super.initState();
    // Initialize the controller in initState
    _controller = CommonButtonController().init(widget);
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CommonButtonController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: SpinKitThreeBounce(size: 20, color: Colors.white),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;


            logger.i('Object ID: ${identityHashCode(controller)}');

            // Bg bar
            Widget bgBar = Obx (()=> CommonBar(

              width: controller.width.value,
              height: controller.height.value,
              circular: controller.circular.value,
              color: controller.isEnabled.value ? controller.color.value : controller.disabledColor.value,

              hasBorder: controller.hasBorder.value,
              borderColor: controller.borderColor.value,
              borderWidth: controller.borderWidth.value,

              hasShadow: controller.hasShadow.value,
              shadowColor: controller.shadowColor.value,
              shadowOffset: controller.shadowOffset.value,
              shadowBlurRadius: controller.shadowBlurRadius.value,
              shadowSpreadRadius: controller.shadowSpreadRadius.value,
            ));

            // Text widget
            Widget? textWidget = Obx (()=> CommonText(
              controller.text.value,
              controller.isEnabled.value ? controller.textStyle.value : controller.disabledTextStyle.value,
            ));

            return Obx(()=> GestureDetector(

              onTap: () async {
                // close keyboard
                FocusScope.of(context).unfocus();

                if (controller.isInProgress.value) {
                  return;
                }

                if (controller.isEnabled.value) {
                  await controller.setLoadingStatus(true);
                  if (controller.onPressed.value != null) await controller.onPressed.value!();
                  await Future.delayed(const Duration(milliseconds: 200));
                  await controller.setLoadingStatus(false);
                }
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                  bgBar,
                  !controller.isInProgress.value ? textWidget : controller.loadingWidget.value != null ? controller.loadingWidget.value! : SpinKitThreeBounce(size: 20.w, color: controller.textStyle.value.color,),
                ],
              )),
            );
          }
        });
  }
}

class CommonButtonController extends GetxController {

  var isEnabled = true.obs;
  var width = 327.w.obs;
  var height = 60.w.obs;
  var color = Colors.white.obs;
  var disabledColor = Colors.black12.obs;
  var text = ''.obs;
  var textStyle = TextStyle().obs;
  var disabledTextStyle = TextStyle().obs;

  var hasRadius = true.obs;
  var circular = 30.w.obs;

  var hasBorder = false.obs;
  var borderColor = Colors.black12.obs;
  var borderWidth = 1.w.obs;

  var hasShadow = false.obs;
  var shadowColor = Colors.black12.obs;
  var shadowOffset = Offset(0, 1.w).obs;
  var shadowBlurRadius = 1.w.obs;
  var shadowSpreadRadius = 0.0.obs;

  var isInProgress = false.obs;
  var loadingWidget = Rx<Widget?>(null);
  var onPressed = Rx<AsyncVoidCallback?>(null);

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<CommonButtonController> init(Widget widget) async {

    widget = widget as CommonButton2;

    // Text style
    isEnabled.value = widget.isEnabled!;
    width.value = widget.width!;
    height.value = widget.height!;
    color.value = widget.color!;
    disabledColor.value = widget.disabledColor!;
    text.value = widget.text!;
    textStyle.value = widget.textStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.white,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );
    disabledTextStyle.value = widget.disabledTextStyle?? TextStyle(
      fontSize: 18.sp,
      color: Colors.black26,
      fontWeight: FontWeight.w500,
      height: 1.5,
    );

    // Radius
    hasRadius.value = widget.hasRadius!;
    circular.value = widget.circular!;

    // Border properties
    hasBorder.value = widget.hasBorder!;
    if (widget.hasBorder!) {
      borderColor.value = widget.borderColor!;
      borderWidth.value = widget.borderWidth!;
    }

    // Bg properties
    hasShadow.value = widget.hasShadow!;
    if (widget.hasShadow!) {
      shadowColor.value = widget.shadowColor!;
      shadowOffset.value = widget.shadowOffset!;
      shadowBlurRadius.value = widget.shadowBlurRadius!;
      shadowSpreadRadius.value = widget.shadowSpreadRadius!;
    }

    onPressed.value = widget.onPressed;
    loadingWidget.value = widget.loadingWidget;

    return this;
  }

  Future<void> setLoadingStatus(bool status) async {
    isInProgress.value = status;
  }

  void toggleEnabled() {
    isEnabled.value = !isEnabled.value;
  }

  void onClick() {



  }
}
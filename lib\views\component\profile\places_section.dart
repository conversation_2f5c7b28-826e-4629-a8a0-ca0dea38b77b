import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class PlacesSection extends StatelessWidget {
  final List<Map<String, dynamic>> places = [
    {
      "placeId": "1",
      "placeName": "Marine Dr. park",
      "distance": "1.3 km",
      "rating": 4.7,
      "imageUrl": "https://images.pexels.com/photos/4587993/pexels-photo-4587993.jpeg",
      "updateTime": DateTime(2025, 2, 22, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "2",
      "placeName": "Clayton off leash park",
      "distance": "0.8 km",
      "rating": 4.9,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 19, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "3",
      "placeName": "Point grey dog park",
      "distance": "0.5 km",
      "rating": 4.2,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 15, 14, 30),
      "openingHours": "13:30",
    },
    {
      "placeId": "4",
      "placeName": "2038 Powell trail",
      "distance": "1.3 km",
      "rating": 4.7,
      "imageUrl": "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "updateTime": DateTime(2025, 2, 22, 14, 30),
      "openingHours": "13:30",
    },
  ];

  @override
  Widget build(BuildContext context) {
    double w = MediaQuery.of(context).size.width / 403;
    double h = MediaQuery.of(context).size.height / 822;

    return Padding(
      padding: EdgeInsets.only(top: 8 * h),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 24 * w),
        itemCount: places.length,
        itemBuilder: (context, index) {
          var place = places[index];
          return _buildPlaceItem(place, w, h);
        },
      ),
    );
  }

  Widget _buildPlaceItem(Map<String, dynamic> place, double w, double h) {
    DateTime updateTime = place["updateTime"];
    String formattedDate = DateFormat("dd MMM yyyy").format(updateTime);
    String formattedTime = place["openingHours"];

    return Container(
      //width: 355 * w,
      //height: 96 * h,
      margin: EdgeInsets.only(bottom: 16 * h),
      padding: EdgeInsets.all(16 * w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF323247).withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: -2,
          ),
          BoxShadow(
            color: const Color(0xFF0C1A4B).withOpacity(0.08),
            blurRadius: 5,
            offset: const Offset(0, 0),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 69 * w,
            height: 69 * h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.13),
              image: DecorationImage(
                image: NetworkImage(place["imageUrl"]),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 16 * w),

          // **右侧信息**
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                place["placeName"],
                style: const TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                  color: Color(0xFF262626),
                ),
              ),
              SizedBox(height: 4 * h),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 10, color: Color(0xFFC6C6C6)),
                  SizedBox(width: 6 * w),
                  Text(
                    place["distance"],
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                  SizedBox(width: 6 * w),
                  const Text("•", style: TextStyle(fontWeight: FontWeight.w600,fontSize: 14, color: Color(0xFF000000))),
                  SizedBox(width: 6 * w),
                  const Icon(Icons.star, size: 14, color: Colors.amber),
                  SizedBox(width: 6 * w),
                  Text(
                    place["rating"].toString(),
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4 * h),
              Row(
                children: [
                  Text(
                    formattedDate,
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                  SizedBox(width: 6 * w),
                  Container(
                    width: 1,
                    height: 16 * h,
                    color: const Color(0xFFC6C6C6), // ✅ 颜色 #C6C6C6
                  ),
                  SizedBox(width: 6 * w),
                  Text(
                    formattedTime,
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

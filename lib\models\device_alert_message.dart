import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'device_message_header.dart';

part 'device_alert_message.g.dart';

@JsonSerializable()
class DeviceAlertMessage extends DeviceMessageHeader {

  DeviceAlertMessageType? alertType;
  String? description;

  DeviceAlertMessage({
    super.sid,
    super.pid,
    required super.deviceId,
    super.generateDate,
    super.sendDate,
    super.receiveDate,
    this.alertType,
    this.description,
  });

  factory DeviceAlertMessage.fromJson(Map<String, dynamic> json) => _$DeviceAlertMessageFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$DeviceAlertMessageToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      jsonData[key] = value;
    });

    return jsonData;
  }

  factory DeviceAlertMessage.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return DeviceAlertMessage.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory DeviceAlertMessage.fromFirestoreMap(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = fromFirestore(map);
    return DeviceAlertMessage.fromJson(jsonData);
  }

  factory DeviceAlertMessage.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return DeviceAlertMessage.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(DeviceAlertMessage? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? sid,
    String? pid,
    required String deviceId,
    int? generateDate,
    int? sendDate,
    int? receiveDate,
    DeviceAlertMessageType? alertType,
    String? description,
  }) {

    return DeviceAlertMessage(
      sid: sid?? uuid.v4(),
      pid: pid,
      deviceId: deviceId,
      generateDate: generateDate,
      sendDate: sendDate,
      receiveDate: receiveDate,
      alertType: alertType,
      description: description,
    );
  }

  static DeviceAlertMessage? copyFrom(DeviceAlertMessage? other) {
    return other == null ? null : DeviceAlertMessage(
      sid: other.sid,
      pid: other.pid,
      deviceId: other.deviceId,
      generateDate: other.generateDate,
      sendDate: other.sendDate,
      receiveDate: other.receiveDate,
      alertType: other.alertType,
      description: other.description,
    );
  }

  static String get collection => 'device_alert_message';

  @override
  String toString() {
    return jsonEncode(this);
  }

}

import 'dart:io';

import 'package:bot_toast/bot_toast.dart';

import 'loading_widget.dart';

class ProcessUtil {

  // Show loading dialog
  static CancelFunc? _cancelFunc;

  static void showProgress({String message = "", bool cancelable = true}) {
    hideProgress();
    _cancelFunc = message.isNotEmpty
        ? BotToast.showCustomLoading(
            clickClose: cancelable,
            toastBuilder: (_) => LoadingWidget(msg: message),
            crossPage: false,
          )
        : BotToast.showLoading();
  }

  // Hide loading dialog
  static void hideProgress() {
    if (_cancelFunc != null) {
      _cancelFunc!();
      _cancelFunc = null;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/views/page/record/record_all_section_list_page.dart';
import 'package:path/path.dart';

import '../../../views/component/widgets/common_text.dart';
import '../../../views/theme/theme_plugin.dart';
import 'record_page.dart';


class RecordAllSection extends StatefulWidget {
  final RecordPageController controller;

  const RecordAllSection({
    super.key,
    required this.controller,
  });

  @override
  State<RecordAllSection> createState() => _RecordAllSectionState();
}


class _RecordAllSectionState extends State<RecordAllSection> {

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildWhiteTopSection(),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 0),
            child: GridView.count(
              crossAxisCount: 2,
              childAspectRatio: 185 / 100,
              mainAxisSpacing: 16.w,
              crossAxisSpacing: 16.w,
              children: _buildTypeCards(),
            ),
          ),
        ),
      ],
    );
  }


  Widget _buildWhiteTopSection() {
    return Container(
      width: double.infinity,
      height: 6.w,
      decoration: BoxDecoration(
        color: widget.controller.theme.themeData.colorScheme.surface,
        borderRadius: BorderRadius.only(bottomRight: Radius.circular(25.r)),
        boxShadow: [
          BoxShadow(
            color: widget.controller.theme.themeData.colorScheme.onSurface.withOpacity(0.15),
            offset: const Offset(0, 0),
            blurRadius: 18,
          ),
        ],
      ),
    );
  }


  List<Widget> _buildTypeCards() {
    final Map<String, List<Map<String, String>>> allActivities =
        widget.controller.allActivities;

    final now = DateTime.now();

    final List<Map<String, dynamic>> allTypes = [
      {'label': 'Feed', 'value': 'feed', 'icon': Icons.restaurant},
      {'label': 'Poop', 'value': 'poop', 'icon': Icons.cruelty_free},
      {'label': 'Walking', 'value': 'walk', 'icon': Icons.directions_walk},
      {'label': 'Social', 'value': 'social', 'icon': Icons.group},
      {'label': 'Medicine', 'value': 'med', 'icon': Icons.medication},
      {'label': 'Vaccine', 'value': 'vaccine', 'icon': Icons.vaccines},
      {'label': 'Grooming', 'value': 'grooming', 'icon': Icons.cut},
      {'label': 'Day care', 'value': 'daycare', 'icon': Icons.home_work},
      {'label': 'Vet', 'value': 'vet', 'icon': Icons.local_hospital},
      {'label': 'Other', 'value': 'other', 'icon': Icons.more_horiz},
    ];

    final Map<String, Color> iconColors = {
      'feed': const Color.fromRGBO(242, 211, 164, 1),
      'poop': const Color.fromRGBO(122, 41, 23, 1),
      'walk': const Color.fromRGBO(130, 196, 60, 1),
      'social': const Color.fromRGBO(56, 151, 240, 1),
      'med': const Color.fromRGBO(56, 151, 240, 1),
      'vaccine': const Color.fromRGBO(235, 195, 81, 1),
      'grooming': const Color.fromRGBO(161, 38, 255, 1),
      'daycare': const Color.fromRGBO(83, 225, 183, 1),
      'vet': const Color.fromRGBO(255, 197, 66, 1),
      'other': const Color.fromRGBO(209, 109, 106, 1),
    };

    List<Widget> result = [];

    for (final item in allTypes) {
      final type = item['value'];
      final records = allActivities.entries
          .expand((entry) => entry.value)
          .where((r) => r['type'] == type)
          .toList();

      int count = records.length;

      // 找出最近时间
      String recentText = 'No record yet';
      if (records.isNotEmpty) {
        final dates = allActivities.entries
            .where((entry) =>
            entry.value.any((r) => r['type'] == type))
            .map((entry) => DateFormat('yyyy-MM-dd').parse(entry.key))
            .toList();

        dates.sort((a, b) => b.compareTo(a));
        final recentDate = dates.first;
        final diff = recentDate.difference(now).inDays;

        if (diff == 0) {
          recentText = 'Recorded today';
        } else if (diff > 0) {
          // 未来时间
          if (diff == 1) {
            recentText = 'Recorded in 1 day';
          } else if (diff < 30) {
            recentText = 'Recorded in $diff days';
          } else if (diff < 365) {
            recentText = 'Recorded in ${diff ~/ 30} months';
          } else {
            recentText = 'Recorded in ${diff ~/ 365} years';
          }
        } else {
          // 过去时间
          final pastDays = -diff;
          if (pastDays == 1) {
            recentText = 'Recorded 1 day ago';
          } else if (pastDays < 30) {
            recentText = 'Recorded $pastDays days ago';
          } else if (pastDays < 365) {
            recentText = 'Recorded ${pastDays ~/ 30} months ago';
          } else {
            recentText = 'Recorded ${pastDays ~/ 365} years ago';
          }
        }
      }

      result.add(
          GestureDetector(
              onTap: () {
                Get.to(() => RecordTypeDetailPage(
                  controller: widget.controller,
                  type: type,
                  label: item['label'],
                ));
              },
          child: Container(
        width: 185.w,
        height: 100.w,
        padding: EdgeInsets.fromLTRB(12.w, 12.w, 9.w, 12.w),
        decoration: BoxDecoration(
          color: widget.controller.theme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.circular(5.r),
          boxShadow: [
            BoxShadow(
              color: const Color.fromRGBO(139, 158, 184, 0.2),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 28.w,
                  height: 28.w,
                  decoration: BoxDecoration(
                    color: iconColors[type]!.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(item['icon'], size: 16.w, color: iconColors[type]),
                ),
                SizedBox(width: 8.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonText(
                      item['label'],
                      widget.controller.theme.textStyleExtension.recordAllType,
                    ),
                    CommonText(
                      recentText,
                      widget.controller.theme.textStyleExtension.recordAllTime,
                    ),
                  ],
                )
              ],
            ),
            CommonText(
              '$count record${count == 1 ? '' : 's'}',
              widget.controller.theme.textStyleExtension.recordAllTime,
            )
          ],
        ),
      )),
      );
    }
    return result;
  }
}

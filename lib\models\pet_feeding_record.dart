import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_feeding_record.g.dart';

@JsonSerializable()
class PetFeedingRecord{
  String sid;
  int? feedTime;
  List<String>? foodTypes;
  String? amount;
  List<String>? images;
  bool? isSnack;
  String? notes;

  PetFeedingRecord({
    required this.sid,
    this.feedTime,
    this.foodTypes,
    this.amount,
    this.images,
    this.isSnack,
    this.notes,
  });

  factory PetFeedingRecord.fromJson(Map<String, dynamic> json) => _$PetFeedingRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetFeedingRecordToJson(this);

  static PetFeedingRecord create ({
    int? feedTime,
    List<String>? foodTypes,
    String? amount,
    List<String>? images,
    bool? isSnack,
    String? notes,
  }) {

    return PetFeedingRecord(
      sid: uuid.v4(),
      feedTime: feedTime,
      foodTypes: foodTypes,
      amount: amount,
      images: images,
      isSnack: isSnack,
      notes: notes,
    );
  }

  static copyFrom(PetFeedingRecord other) {
    return PetFeedingRecord(
      sid: other.sid,
      notes: other.notes,
      feedTime: other.feedTime,
      foodTypes: other.foodTypes,
      amount: other.amount,
      images: other.images,
      isSnack: other.isSnack,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

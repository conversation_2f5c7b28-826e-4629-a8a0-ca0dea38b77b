import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/page/record/record_all_section.dart';
import 'package:onenata_app/views/page/record/record_reminder_section.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/pet.dart';
import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../models/vo/pet_profile_vo.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/service_response.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../../component/widgets/common_record_widget.dart';
import 'add_record_page.dart';
import 'record_daily_section.dart';

class RecordPage extends StatefulWidget {
  const RecordPage({super.key});

  @override
  RecordPageState createState() => RecordPageState();
}

class RecordPageState extends State<RecordPage> with TickerProviderStateMixin {
  late Future<RecordPageController> _controller;
  late TabController _tabController;
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => RecordPageController().init());
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<RecordPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: LoadingWidget());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: \${snapshot.error}'));
        } else {
          final controller = snapshot.data!;

          return CommonPage(
            theme: controller.theme,
            backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
            body: Stack(
              children: [
                if (isExpanded)
                  Positioned.fill(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          isExpanded = false;
                        });
                      },
                      child: Container(color: Colors.transparent),
                    ),
                  ),
                Column(
                  children: [
                    SizedBox(height: 16.w),
                    _buildTopSection(controller),
                    SizedBox(height: 12.w),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: TabBar(
                        controller: _tabController,
                        labelColor:
                            controller.theme.themeData.colorScheme.primary,
                        unselectedLabelColor:
                            controller.theme.themeData.colorScheme.onSurface,
                        labelStyle:
                            controller.theme.textStyleExtension.petInfoBody,
                        unselectedLabelStyle:
                            controller.theme.textStyleExtension.petInfoTitle,
                        indicator: const BoxDecoration(),
                        dividerHeight: 0,
                        tabs: const [
                          Tab(text: "Diary"),
                          Tab(text: "Reminder"),
                          Tab(text: "All"),
                        ],
                      ),
                    ),
                    SizedBox(height: 12.w),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          RecordDailySection(
                            controller: controller,
                            isExpanded: isExpanded,
                            onExpandToggle: (value) {
                              setState(() {
                                isExpanded = value;
                              });
                            },
                          ),
                          RecordReminderSection(controller: controller),
                          RecordAllSection(controller: controller),
                        ],
                      ),
                    ),
                  ],
                ),
                Positioned(
                  bottom: 24.w,
                  right: 24.w,
                  child: GestureDetector(
                    onTap: () {
                      _showAddRecordBottomSheet(controller);
                    },
                    child: Container(
                      width: 56.w,
                      height: 56.w,
                      decoration: BoxDecoration(
                        color: controller.theme.themeData.colorScheme.onPrimary,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: controller.theme.themeData.colorScheme.primary,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: controller
                                .theme.colorExtension.eventPostTileShadow,
                            offset: const Offset(0, 8),
                            blurRadius: 16,
                            spreadRadius: -4,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.add,
                        color: controller.theme.themeData.colorScheme.primary,
                        size: 28.w,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildTopSection(RecordPageController controller) {
    return Container(
      height: 64.w,
      padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 12.w),
      child: Row(
        children: [
          IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(Icons.arrow_back,
                color: controller.theme.themeData.colorScheme.onSurface),
          ),
          SizedBox(width: 8.w),
          Container(
            width: 1.w,
            height: 22.w,
            color: controller.theme.themeData.colorScheme.tertiary,
          ),
          SizedBox(width: 8.w),
          CommonText(
            "Record",
            controller.theme.textStyleExtension.userProfileSettingBody,
          ),
          const Spacer(),
          Obx(() => Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: controller.selectedPet.value,
                    icon: Icon(
                      Icons.keyboard_arrow_down,
                      color: controller.theme.themeData.colorScheme.tertiary,
                    ),
                    dropdownColor: Colors.white,
                    items: controller.pets.map((pet) {
                      return DropdownMenuItem<String>(
                        value: pet.name,
                        child: Row(
                          children: [
                            FutureBuilder<Widget?>(
                              future: controller.petAvatarFutures[controller
                                  .petList!
                                  .firstWhere((p) => p.name == pet.name)
                                  .sid!],
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return SizedBox(width: 20.w, height: 20.w);
                                } else if (snapshot.hasError ||
                                    snapshot.data == null) {
                                  return CircleAvatar(
                                    radius: 10.w,
                                    backgroundImage: AssetImage(
                                        "assets/images/default_pet.png"),
                                  );
                                } else {
                                  return SizedBox(
                                    width: 20.w,
                                    height: 20.w,
                                    child: snapshot.data!,
                                  );
                                }
                              },
                            ),
                            SizedBox(width: 8.w),
                            SizedBox(
                              width: 40.w,
                              child: CommonText(
                                pet.name,
                                controller.theme.textStyleExtension
                                    .placeListItemMileage,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        controller.selectedPet.value = newValue;
                        controller.currentPetData.value = controller.pets
                            .firstWhere((pet) => pet.name == newValue);
                        controller.refreshAllActivities();
                      }
                    },
                  ),
                ),
              ))
        ],
      ),
    );
  }

  void _showAddRecordBottomSheet(RecordPageController controller) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(26.r)),
      ),
      backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
      builder: (context) {
        final allTypes = [
          {'label': 'Feed', 'value': 'feed', 'icon': Icons.restaurant},
          {'label': 'Poop', 'value': 'poop', 'icon': Icons.cruelty_free},
          {'label': 'Walking', 'value': 'walk', 'icon': Icons.directions_walk},
          {'label': 'Social', 'value': 'social', 'icon': Icons.group},
          {'label': 'Medicine', 'value': 'med', 'icon': Icons.medication},
          {'label': 'Vaccine', 'value': 'vaccine', 'icon': Icons.vaccines},
          {'label': 'Grooming', 'value': 'grooming', 'icon': Icons.cut},
          {'label': 'Day care', 'value': 'daycare', 'icon': Icons.home_work},
          {'label': 'Vet', 'value': 'vet', 'icon': Icons.local_hospital},
          {'label': 'Other', 'value': 'other', 'icon': Icons.more_horiz},
        ];

        final iconColors = {
          'feed': const Color.fromRGBO(242, 211, 164, 1),
          'poop': const Color.fromRGBO(122, 41, 23, 1),
          'walk': const Color.fromRGBO(130, 196, 60, 1),
          'social': const Color.fromRGBO(56, 151, 240, 1),
          'med': const Color.fromRGBO(56, 151, 240, 1),
          'vaccine': const Color.fromRGBO(235, 195, 81, 1),
          'grooming': const Color.fromRGBO(161, 38, 255, 1),
          'daycare': const Color.fromRGBO(83, 225, 183, 1),
          'vet': const Color.fromRGBO(255, 197, 66, 1),
          'other': const Color.fromRGBO(209, 109, 106, 1),
        };

        final now = DateTime.now();
        final allActivities = controller.allActivities;

        return SizedBox(
          height: 640.w,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 36.w,
                    height: 4.w,
                    margin: EdgeInsets.only(bottom: 12.w),
                    decoration: BoxDecoration(
                      color: controller.theme.themeData.colorScheme.tertiary,
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 4.w, bottom: 16.w),
                  child: CommonText(
                    'Please select a category',
                    controller.theme.textStyleExtension.recordListDescription,
                  ),
                ),
                Expanded(
                  child: GridView.count(
                    crossAxisCount: 2,
                    mainAxisSpacing: 16.w,
                    crossAxisSpacing: 16.w,
                    childAspectRatio: 185 / 100,
                    children: allTypes.map((item) {
                      final type = item['value'];
                      final label = item['label'];
                      final icon = item['icon'];

                      final records = allActivities.entries
                          .expand((e) => e.value)
                          .where((r) => r['type'] == type)
                          .toList();

                      int count = records.length;

                      String recentText = 'No record yet';
                      if (records.isNotEmpty) {
                        final dates = allActivities.entries
                            .where((entry) =>
                                entry.value.any((r) => r['type'] == type))
                            .map((entry) =>
                                DateFormat('yyyy-MM-dd').parse(entry.key))
                            .toList();
                        dates.sort((a, b) => b.compareTo(a));
                        final diff = dates.first.difference(now).inDays;
                        if (diff == 0) {
                          recentText = 'Recorded today';
                        } else if (diff > 0) {
                          recentText = 'Recorded in $diff days';
                        } else {
                          final past = -diff;
                          recentText = past == 1
                              ? 'Recorded 1 day ago'
                              : 'Recorded $past days ago';
                        }
                      }

                      return GestureDetector(
                        onTap: () async {
                          Navigator.pop(context);
                          final result = await Get.to(() => AddRecordPage(
                                parentController: controller,
                                type: type as String,
                                label: label,
                                selectedPet: controller.petList!.firstWhere(
                                    (p) =>
                                        p.name == controller.selectedPet.value),
                              ));
                          if (result == true) {
                            await controller.reloadPetsAndRefresh();
                            setState(() {});
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.fromLTRB(12.w, 12.w, 9.w, 12.w),
                          decoration: BoxDecoration(
                            color:
                                controller.theme.themeData.colorScheme.surface,
                            borderRadius: BorderRadius.circular(5.r),
                            boxShadow: [
                              BoxShadow(
                                color: const Color.fromRGBO(139, 158, 184, 0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 28.w,
                                    height: 28.w,
                                    decoration: BoxDecoration(
                                      color: iconColors[type]!.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(icon as IconData?,
                                        size: 16.w, color: iconColors[type]),
                                  ),
                                  SizedBox(width: 8.w),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CommonText(
                                        label as String,
                                        controller.theme.textStyleExtension
                                            .recordAllType,
                                      ),
                                      CommonText(
                                        recentText,
                                        controller.theme.textStyleExtension
                                            .recordAllTime,
                                      ),
                                    ],
                                  )
                                ],
                              ),
                              CommonText(
                                '$count record${count == 1 ? '' : 's'}',
                                controller
                                    .theme.textStyleExtension.recordAllTime,
                              )
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class RecordPageController extends GetxController {
  late ThemePlugin theme;
  final AuthService _authService = AuthService.instance;
  var userAccount = Rx<UserAccount?>(null);
  final PetService petService = PetService();
  List<Pet>? petList;
  UserData? userData;
  late ScrollController dateScrollController;

  final Map<String, Future<Widget?>> petAvatarFutures = {};
  final RxString selectedType = 'all'.obs;
  late RxString selectedPet = 'Unknown'.obs;
  late List<PetProfileVO> pets;
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final Rx<PetProfileVO> currentPetData = PetProfileVO.empty().obs;
  final Rx<DateTime> displayedMonth = DateTime.now().obs;

  final RxMap<String, List<Map<String, String>>> allActivities =
      <String, List<Map<String, String>>>{}.obs;

  Future<RecordPageController> init() async {
    theme = await Get.putAsync(() => ThemePlugin().init());
    dateScrollController = ScrollController();
    userAccount.value = _authService.userAccount.value;
    userData = _authService.userData.value;

    String? fcmToken = await FirebaseMessaging.instance.getToken();
    // String? fcmToken = await FirebaseMessaging.instance.getToken();
    petList = await petService.getOwnedPets(userAccount.value!.sid!);
    for (var pet in petList ?? []) {
      petAvatarFutures[pet.sid!] = ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: userAccount.value!.sid!,
        petId: pet.sid!,
        avatar: pet.avatar,
        size: 20.w,
      );
    }
    pets = petList!.map((pet) {
      return PetProfileVO(
        name: pet.name ?? "Unnamed",
        image: pet.avatar ?? "assets/images/default_pet.png",
        type: pet.type?.name ?? "Unknown",
        breed: pet.breed ?? "Unknown",
        gender: pet.gender?.name ?? "Unknown",
        weight: "N/A",
        birthday: pet.birthday != null
            ? DateFormat("d MMMM yyyy")
                .format(DateTime.fromMillisecondsSinceEpoch(pet.birthday!))
            : "unknown",
        home: "N/A",
        nextVaccine: "N/A",
      );
    }).toList();

    currentPetData.value = pets.first;
    if (petList!.isNotEmpty) {
      selectedPet.value = petList!.first.name!;
    }
    refreshAllActivities();
    return this;
  }

  // record_page_controller.dart

  Future<void> refreshAllActivities() async {
    if (petList == null || petList!.isEmpty) return;

    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);

    Map<String, List<Map<String, String>>> activities = {};

    final feedingRecords = await petService.getPetFeedingRecords(pet.sid!);

    for (var record in feedingRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.feedTime ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'feed',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.feedTime ?? 0)),
        'description':
            '${record.foodTypes?.join(", ") ?? ''} ${record.amount ?? ''}'
                .trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final poopRecords = await petService.getPetPoopRecords(pet.sid!);

    for (var record in poopRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.poopTime ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'poop',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.poopTime ?? 0)),
        'description':
            '${record.colorTags?.join(", ") ?? ''} ${record.shapeTags?.join(", ") ?? ''}'
                .trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final walkingRecords = await petService.getPetWalkingRecords(pet.sid!);

    for (var record in walkingRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'walk',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
        'description':
            '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0))} - '
                '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.endTime ?? 0))}, '
                '${record.distanceTags?.isNotEmpty == true ? record.distanceTags!.first + 'km' : ''}',
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final socialRecords = await petService.getPetSocialRecords(pet.sid!);

    for (var record in socialRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'social',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
        'description':
            '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0))} - '
                '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.endTime ?? 0))}, '
                'with '
                '${(record.targetTypeTags ?? []).join(", ")}, '
                '${(record.behaviorTags ?? []).join(", ")}',
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final medicineRecords = await petService.getPetMedicineRecords(pet.sid!);

    for (var record in medicineRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'med',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
        'description': '${record.medicineName ?? ''} '
                '${(record.dose ?? []).join(", ")} '
                '${(record.usage ?? []).join(", ")}'
            .trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final vaccineRecords = await petService.getPetVaccineRecords(pet.sid!);

    for (var record in vaccineRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'vaccine',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
        'description':
            '${record.vaccineName ?? ''} ${record.location ?? ''}'.trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final groomingRecords = await petService.getPetGroomingRecords(pet.sid!);

    for (var record in groomingRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'grooming',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
        'description': record.serviceType?.join(", ") ?? 'Grooming',
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final daycareRecords = await petService.getPetDaycareRecords(pet.sid!);
    for (var record in daycareRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'daycare',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
        'description':
            '${record.location ?? ''} ${record.content?.join(", ") ?? ''}'
                .trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final vetRecords = await petService.getPetVetRecords(pet.sid!);
    for (var record in vetRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'vet',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
        'description':
            '${record.description ?? ''} ${record.treat ?? ''}'.trim(),
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    final otherRecords = await petService.getPetOtherRecords(pet.sid!);
    for (var record in otherRecords) {
      final dateStr = DateFormat('yyyy-MM-dd')
          .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
      activities.putIfAbsent(dateStr, () => []);
      activities[dateStr]!.add({
        'sid': record.sid,
        'type': 'other',
        'time': DateFormat('HH:mm')
            .format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
        'description': record.title ?? 'Other',
        'remind': 'done',
      });
      activities[dateStr]!.sort((a, b) {
        final aTimeParts = a['time']!.split(':').map(int.parse).toList();
        final bTimeParts = b['time']!.split(':').map(int.parse).toList();
        final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
        final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
        return aMinutes.compareTo(bMinutes);
      });
    }

    allActivities.value = activities;
    allActivities.refresh();
  }

  // void refreshAllActivities() {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value, orElse: () => petList!.first);
  //
  //   final Map<String, List<Map<String, String>>> activities = {};
  //
  //   for (var record in pet.feedingRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.feedTime ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'feed',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.feedTime ?? 0)),
  //       'description': '${record.foodTypes?.join(", ") ?? ''} ${record.amount ?? ''}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.poopRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.poopTime ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'poop',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.poopTime ?? 0)),
  //       'description': '${record.colorTags?.join(", ") ?? ''} ${record.shapeTags?.join(", ") ?? ''}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.walkingRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'walk',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
  //       'description':
  //       '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0))} - '
  //           '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.endTime ?? 0))}, '
  //           '${record.distanceTags?.isNotEmpty == true ? record.distanceTags!.first + 'km' : ''}',
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.socialRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'social',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
  //       'description':
  //       '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0))} - '
  //           '${DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.endTime ?? 0))}, '
  //           'with '
  //           '${(record.targetTypeTags ?? []).join(", ")}, '
  //           '${(record.behaviorTags ?? []).join(", ")}',
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.medicineRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'med',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
  //       'description': '${record.medicineName ?? ''} '
  //           '${(record.dose ?? []).join(", ")} '
  //           '${(record.usage ?? []).join(", ")}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.vaccineRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'vaccine',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
  //       'description': '${record.vaccineName ?? ''} ${record.location ?? ''}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.groomingRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'grooming',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
  //       'description': record.serviceType?.join(", ") ?? 'Grooming',
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.daycareRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'daycare',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.startTime ?? 0)),
  //       'description': '${record.location ?? ''} ${record.content?.join(", ") ?? ''}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.vetRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'vet',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
  //       'description': '${record.description ?? ''} ${record.treat ?? ''}'.trim(),
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   for (var record in pet.otherRecords ?? []) {
  //     final dateStr = DateFormat('yyyy-MM-dd').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0));
  //     activities.putIfAbsent(dateStr, () => []);
  //     activities[dateStr]!.add({
  //       'sid': record.sid,
  //       'type': 'other',
  //       'time': DateFormat('HH:mm').format(DateTime.fromMillisecondsSinceEpoch(record.time ?? 0)),
  //       'description': record.title ?? 'Other',
  //       'remind': 'done',
  //     });
  //     activities[dateStr]!.sort((a, b) {
  //       final aTimeParts = a['time']!.split(':').map(int.parse).toList();
  //       final bTimeParts = b['time']!.split(':').map(int.parse).toList();
  //       final aMinutes = aTimeParts[0] * 60 + aTimeParts[1];
  //       final bMinutes = bTimeParts[0] * 60 + bTimeParts[1];
  //       return aMinutes.compareTo(bMinutes);
  //     });
  //   }
  //   allActivities.value = activities;
  // }

  Future<void> reloadPetsAndRefresh() async {
    petList = await petService.getOwnedPets(userAccount.value!.sid!);
    refreshAllActivities();
  }

  void changeDisplayedMonth(int year, int month) {
    displayedMonth.value = DateTime(year, month, 1);
  }

  // Future<void> deleteFeedingRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetFeedingRecord(pet.sid!, recordSid);
  // }
  Future<void> deleteFeedingRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetFeedingRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deletePoopRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetPoopRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }
  // Future<void> deletePoopRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetPoopRecord(pet.sid!, recordSid);
  // }

  Future<void> deleteWalkingRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetWalkingRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deleteSocialRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetSocialRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  // Future<void> deleteWalkingRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetWalkingRecord(pet.sid!, recordSid);
  // }
  //
  // Future<void> deleteSocialRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetSocialRecord(pet.sid!, recordSid);
  // }
  Future<void> deleteMedicineRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetMedicineRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deleteVaccineRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetVaccineRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deleteGroomingRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetGroomingRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  // Future<void> deleteMedicineRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetMedicineRecord(pet.sid!, recordSid);
  // }
  //
  // Future<void> deleteVaccineRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetVaccineRecord(pet.sid!, recordSid);
  // }
  //
  // Future<void> deleteGroomingRecord(String recordSid) async {
  //   final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
  //   await _petService.deletePetGroomingRecord(pet.sid!, recordSid);
  // }

  Future<void> deleteDaycareRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetDaycareRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deleteVetRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetVetRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }

  Future<void> deleteOtherRecord(String recordSid) async {
    final pet = petList!.firstWhere((p) => p.name == selectedPet.value);
    await petService.deletePetOtherRecord(pet.sid!, recordSid);
    await refreshAllActivities();
  }
}

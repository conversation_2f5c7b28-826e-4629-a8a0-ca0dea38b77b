import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

part 'geo_postal_address.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoPostalAddress {

  String? revision;
  String? regionCode;
  String? languageCode;
  String? postalCode;
  String? sortingCode; // id from map provider
  String? region; // country
  String? administrativeArea; // state OR province
  String? locality; // city
  String? subLocality;
  List<String>? addressLines;
  List<String>? recipients;
  String? organization;

  GeoPostalAddress({
    this.revision,
    this.regionCode,
    this.languageCode,
    this.postalCode,
    this.sortingCode,
    this.administrativeArea,
    this.locality,
    this.subLocality,
    this.addressLines,
    this.recipients,
    this.organization,
  });

  factory GeoPostalAddress.fromJson(Map<String, dynamic> json) => _$GeoPostalAddressFromJson(json);
  Map<String, dynamic> toJson() => _$GeoPostalAddressToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'sublocality') {
        jsonData['subLocality'] = value;
        return;
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoPostalAddress.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoPostalAddress.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory GeoPostalAddress.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoPostalAddress.fromJson(jsonData);
  }

  factory GeoPostalAddress.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoPostalAddress.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoPostalAddress? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    String? revision,
    String? regionCode,
    String? languageCode,
    String? postalCode,
    String? sortingCode,
    String? administrativeArea,
    String? locality,
    String? subLocality,
    List<String>? addressLines,
    List<String>? recipients,
    String? organization,
  }) {

    return GeoPostalAddress(
      revision: revision,
      regionCode: regionCode,
      languageCode: languageCode,
      postalCode: postalCode,
      sortingCode: sortingCode,
      administrativeArea: administrativeArea,
      locality: locality,
      subLocality: subLocality,
      addressLines: addressLines,
      recipients: recipients,
      organization: organization,
    );
  }

  static GeoPostalAddress copyFrom(GeoPostalAddress? other) {
    return GeoPostalAddress(
      revision: other?.revision,
      regionCode: other?.regionCode,
      languageCode: other?.languageCode,
      postalCode: other?.postalCode,
      sortingCode: other?.sortingCode,
      administrativeArea: other?.administrativeArea,
      locality: other?.locality,
      subLocality: other?.subLocality,
      addressLines: other?.addressLines,
      recipients: other?.recipients,
      organization: other?.organization,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

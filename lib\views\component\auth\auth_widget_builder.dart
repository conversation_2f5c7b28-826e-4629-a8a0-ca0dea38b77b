import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

import '../../../common/enum/media_type.dart';
import '../../../services/user_service.dart';

class AuthWidgetBuilder {

  static Widget buildWelcomeAvatar(ThemePlugin theme) {

    // Background
    CommonBar bg = CommonBar(
      shape: BoxShape.circle,
      color: theme.themeData.colorScheme.surface,
      radius: theme.layout.authWelcomeAvatarSize,
      hasShadow: true,
      shadowColor: theme.colorExtension.authAvatarShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Avatar
    CommonImageAsset avatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.authWelcomeAvatarSize,
      height: theme.layout.authWelcomeAvatarSize,
    );

    return CommonAvatar(
      hasBg: true,
      bg: bg,
      hasContent: true,
      content: avatar,
    );
  }

  static Widget buildBackButton(ThemePlugin theme, BuildContext context) {

    // Background
    CommonBar bg = CommonBar(
      color: theme.themeData.colorScheme.surface,
      width: theme.layout.authBackButtonBgSize,
      height: theme.layout.authBackButtonBgSize,
      circular: theme.layout.authBackButtonBgCircular,
      hasShadow: true,
      shadowColor: theme.colorExtension.authBackButtonShadow,
      shadowOffset: theme.layout.authBackButtonBgShadowOffset,
      shadowBlurRadius: theme.layout.authBackButtonBgShadowBlur,
    );

    // Icon
    CommonImageAsset icon = CommonImageAsset(
      path: ImageAssetConst.iconArrowLeft,
      color: theme.themeData.colorScheme.primary,
      width: theme.layout.authBackButtonIconWidth,
      height: theme.layout.authBackButtonIconHeight,
    );

    // Button
    return GestureDetector(
      onTap: () async {
        FocusScope.of(context).unfocus(); // ✅ Close keyboard first
        await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
        Get.back();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          bg,
          icon,
        ],
      ),
    );
  }

  static Widget buildAvatar(ThemePlugin theme, {Widget? avatar, bool? editable = false}) {

    // Background
    CommonBar bg = CommonBar(
      shape: BoxShape.circle,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha),
      radius: theme.layout.authAvatarSize,
      hasShadow: true,
      shadowColor: theme.colorExtension.authAvatarShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Avatar
    CommonImageAsset defaultAvatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.authAvatarSize,
      height: theme.layout.authAvatarSize,
    );

    // Camera bar
    CommonBar? cameraBar = !editable!
        ? null
        : CommonBar(
            color: theme.themeData.colorScheme.surface,
            width: theme.layout.profileAvatarCameraBarWidth,
            height: theme.layout.profileAvatarCameraBarHeight,
            circular: theme.layout.profileAvatarCameraBarCircular,
            hasShadow: true,
            shadowColor: theme.colorExtension.profileAvatarCameraShadow,
            shadowOffset: theme.layout.authAvatarShadowOffset,
            shadowBlurRadius: theme.layout.authAvatarShadowBlur,
          );

    // Camera icon
    CommonImageAsset? cameraIcon = !editable
        ? null
        : CommonImageAsset(
            path: ImageAssetConst.iconImage,
            width: theme.layout.profileAvatarCameraIconWidth,
            height: theme.layout.profileAvatarCameraIconHeight,
          );

    return CommonAvatar(
      hasBg: true,
      bg: bg,
      hasContent: true,
      content: avatar?? defaultAvatar,
      changeImgEnabled: editable,
      cameraIcon: !editable ? null : Stack(
        alignment: Alignment.center,
        children: [
          cameraBar!,
          cameraIcon!,
        ],
      ),
      cameraIconTopMargin: !editable ? null : theme.layout.authAvatarSize - 2.5.w,
    );
  }

  static Future<Widget?> buildUserAvatarWidget(ThemePlugin theme, {required String userId, required String avatar}) async {

    // relative path + name
    // avatar = 'b258fc381.png';
    String filePath  = LocalStorage.buildUserAvatarFilePath(userId: userId, fileName: avatar);
    Directory dirPath = LocalStorage.buildUserAvatarLocalDirectory(userId: userId, fileName: avatar);

    // local file
    File file = File(dirPath.path);

    if (await file.exists()) {
      logger.d('Try to load image from ${dirPath.path}');
      return CommonImageAsset(
        file: File(file.path),
        isCircle: true,
        width: theme.layout.authAvatarSize,
        height: theme.layout.authAvatarSize,
      );
    }
    // if avatar not exist at local, download avatar from network and save to local storage
    else {

      try{

        String url = await CloudStorage().getDownloadURL(filePath);

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          CloudStorage().downloadFile(filePath);
        });

        return CommonImageAsset(
          url: url,
          isCircle: true,
          width: theme.layout.authAvatarSize,
          height: theme.layout.authAvatarSize,
        );
      } catch (e) {
        logger.e('Image url not valid, use default avatar instead');
        return CommonImageAsset(
          path: ImageAssetConst.defaultAvatar,
          isCircle: true,
          width: theme.layout.authAvatarSize,
          height: theme.layout.authAvatarSize,
        );
      }
    }
  }

  static Future<Widget?> buildUserAvatar(ThemePlugin theme, {required String userId, required String? avatar}) async {

    if (avatar != null) {
      return await AuthWidgetBuilder.buildUserAvatarWidget(
          theme, userId: userId, avatar: avatar);
    } else {
      return CommonImageAsset(
        path: ImageAssetConst.defaultAvatar,
        isCircle: true,
        width: theme.layout.authAvatarSize,
        height: theme.layout.authAvatarSize,
      );
    }
  }

  static Widget buildBackgroundImage(ThemePlugin theme) {
    return Container(
      width: theme.layout.authBackgroundImageWidth,
      height: theme.layout.authBackgroundImageHeight,
      child: Stack(
        children: [
          Positioned(
            top: theme.layout.authBackgroundImagePaddingTop,
            left: theme.layout.authBackgroundImagePaddingLeft,
            child: CommonImageAsset(
                path: ImageAssetConst.welcomeImage,
                width: theme.layout.authBackgroundImageWidth,
                height: theme.layout.authBackgroundImageHeight),
          ),
        ],
      ),
    );
  }

  static Widget buildAvatarHeader(ThemePlugin theme, {Widget? avatar, bool? isWelcome = false, bool? editable = false, Widget? title}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: theme.layout.authTitleBarHeight),
        isWelcome!
            ? buildWelcomeAvatar(theme)
            : buildAvatar(avatar: avatar, theme, editable: editable),
        SizedBox(height: theme.layout.authAvatarTitleMarginTop),
        title ?? CommonText(
          'app.name'.t18,
          theme.textStyleExtension.authPageTitle,
        ),
      ],
    );
  }

  static Widget buildAvatarHeaderLine(ThemePlugin theme, BuildContext context, {Widget? avatar, Widget? title, bool? editable = false}) {
    return Container(
      width: screenSize.width,
      height: theme.layout.authAvatarHeaderLineHeight,
      color: theme.themeData.colorScheme.primary,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          buildAvatarHeader(theme, avatar: avatar, title: title, editable: editable),
          Positioned(
            top: theme.layout.authBackButtonIconPaddingTop,
            left: theme.layout.authBackButtonIconPaddingLeft,
            child: buildBackButton(theme, context),
          ),
        ],
      ),
    );
  }

  static Widget buildPositionAvatar(ThemePlugin theme, BuildContext context) {
    return Positioned(
      top: theme.layout.authBackButtonIconPaddingTop,
      left: theme.layout.authBackButtonIconPaddingLeft,
      child: buildBackButton(theme, context),
    );
  }

  static Widget buildLightRing(ThemePlugin theme) {

    // Bg dog image
    Widget bgImageDog = CommonImageAsset(
      path: '<EMAIL>',
      width: 265.w,
      height: 229.w,
    );

    // Bg cat image
    Widget bgImageCat = CommonImageAsset(
      path: '<EMAIL>',
      width: 201.5.w,
      height: 257.4.w,
    );

    // Bg light ring large
    Widget bgLightRingLarge = CommonBar(
      shape: BoxShape.circle,
      radius: 448.w,
      color: theme.colorExtension.authPendingVerifyLightRingLarge.withAlpha(0.07.colorAlpha),
    );

    // Bg light ring large
    Widget bgLightRingMedium = CommonBar(
      shape: BoxShape.circle,
      radius: 384.w,
      color: theme.colorExtension.authPendingVerifyLightRingMedium.withAlpha(0.04.colorAlpha),
    );

    // Bg light ring large
    Widget bgLightRingSmall = CommonBar(
      shape: BoxShape.circle,
      radius: 300.w,
      color: theme.colorExtension.authPendingVerifyLightRingSmall.withAlpha(0.02.colorAlpha),
    );

    // Bg light ring
    return Stack(
      alignment: Alignment.center,
      children: [
        bgLightRingLarge,
        bgLightRingMedium,
        bgLightRingSmall,
        Positioned(
          top: 6.2.w,
          left: 28.w,
          child: bgImageDog,
        ),
        Positioned(
          top: 76.w,
          left: 192.w,
          child: bgImageCat,
        ),
      ],
    );
  }

  static Widget buildWhiteSection(ThemePlugin theme) {
    return Column(
      children: [
        SizedBox(height: theme.layout.authAvatarHeaderLineHeight),
        Expanded(
          child: SizedBox(
            height: screenSize.height - theme.layout.authAvatarHeaderLineHeight,
            child: CommonBar(
              width: screenSize.width,
              height: screenSize.height - theme.layout.authAvatarHeaderLineHeight,
              color: theme.themeData.colorScheme.surface,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(theme.layout.authWhiteSectionCircular),
              ),
              hasShadow: true,
              shadowColor: theme.colorExtension.authWhiteSectionShadow,
              shadowBlurRadius: theme.layout.authWhiteSectionShadowBlur,
            ),
          ),
        )
      ],
    );
  }

  static Widget buildButton(
      ThemePlugin theme, CommonButtonController controller,
      {String? text, bool? isEnabled, AsyncVoidCallback? onPressed}) {
    return CommonButton2(
      isEnabled: isEnabled,
      controller: controller,
      width: theme.layout.authButtonWidth,
      height: theme.layout.authButtonHeight,
      color: theme.themeData.colorScheme.secondary,
      disabledColor: theme.themeData.colorScheme.tertiary,
      text: text,
      textStyle: theme.textStyleExtension.authPageButton,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton,
      circular: theme.layout.authButtonCircular,
      onPressed: onPressed,
    );
  }

  static Widget buildAlterButton(
      ThemePlugin theme, CommonButtonController controller,
      {String? text, AsyncVoidCallback? onPressed}) {
    return CommonButton2(
      controller: controller,
      width: theme.layout.authAlterButtonWidth,
      height: theme.layout.authAlterButtonHeight,
      color: Colors.white.withAlpha(0.0.colorAlpha),
      disabledColor: Colors.white.withAlpha(0.0.colorAlpha),
      text: text,
      textStyle: theme.textStyleExtension.authPageAlterButton,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton,
      hasRadius: false,
      onPressed: onPressed,
    );
  }

  static Widget buildTextButton(ThemePlugin theme, CommonButtonController controller, {String? text, AsyncVoidCallback? onPressed}) {

    return CommonButton2(
      controller: controller,
      width: theme.layout.authAlterButtonWidth,
      height: theme.layout.authAlterButtonHeight,
      color: Colors.white.withAlpha(0.0.colorAlpha),
      disabledColor: Colors.white.withAlpha(0.0.colorAlpha),
      text: text,
      textStyle: theme.textStyleExtension.authPageForgotPassword,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton,
      hasRadius: false,
      onPressed: onPressed,
    );
  }

  static Widget buildInputBoxEmail(
      ThemePlugin theme,
      TextEditingController textEditingController,
      CommonTextFieldController controller,
      {AsyncCallbackWithContextAndResult? onChanged,
      AsyncCallbackWithContext? onCleared}) {
    // Callbacks
    // final VoidCallback? onClear;
    // final VoidCallback? onTogglePasswordVisibility;
    // final ValueChanged<bool>? onChanged;

    return CommonTextField2(
      textEditingController: textEditingController,
      controller: controller,
      keyboardType: TextInputType.emailAddress,

      width: theme.layout.authTextFieldWidth,
      height: theme.layout.authTextFieldHeight,
      hasRadius: true,
      circular: theme.layout.authTextFieldCircular,
      iconColor: theme.themeData.colorScheme.tertiary,
      borderWidth: theme.layout.authTextFieldBorderWidth,
      borderColor: theme.themeData.colorScheme.tertiary,
      focusBorderColor: theme.themeData.colorScheme.secondary,
      mistakeBorderColor: theme.themeData.colorScheme.error,
      disabledBorderColor: theme.themeData.colorScheme.tertiary,

      hintText: 'auth.hint.email'.t18,
      hintStyle: theme.textStyleExtension.authPageHintText,
      textStyle: theme.textStyleExtension.authPageInputText,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText,
      disabledTextStyle: theme.textStyleExtension.authPageHintText,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical,

      enableClearIcon: true,
      clearIconSize: theme.layout.authTextFieldClearIconSize,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static Widget buildInputBoxPhone(
      ThemePlugin theme,
      TextEditingController textEditingController,
      CommonTextFieldController controller,
      {AsyncCallbackWithContextAndResult? onChanged,
      AsyncCallbackWithContext? onCleared}) {
    return CommonTextField2(
      textEditingController: textEditingController,
      controller: controller,
      keyboardType: TextInputType.phone,

      width: theme.layout.authTextFieldWidth,
      height: theme.layout.authTextFieldHeight,
      hasRadius: true,
      circular: theme.layout.authTextFieldCircular,
      iconColor: theme.themeData.colorScheme.tertiary,
      borderWidth: theme.layout.authTextFieldBorderWidth,
      borderColor: theme.themeData.colorScheme.tertiary,
      focusBorderColor: theme.themeData.colorScheme.secondary,
      mistakeBorderColor: theme.themeData.colorScheme.error,
      disabledBorderColor: theme.themeData.colorScheme.tertiary,

      hintText: 'auth.hint.phone'.t18,
      hintStyle: theme.textStyleExtension.authPageHintText,
      textStyle: theme.textStyleExtension.authPageInputText,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText,
      disabledTextStyle: theme.textStyleExtension.authPageHintText,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical,

      enableClearIcon: true,
      clearIconSize: theme.layout.authTextFieldClearIconSize,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static Widget buildInputBoxVerificationCode(
      ThemePlugin theme,
      TextEditingController textEditingController,
      CommonTextFieldController controller,
      {bool? enabled = false,
      bool? hasMistake = false,
      AsyncCallbackWithContextAndResult? onChanged,
      AsyncCallbackWithContext? onCleared}) {
    return CommonTextField2(
      textEditingController: textEditingController,
      controller: controller,
      keyboardType: TextInputType.number,
      isEnabled: enabled,

      width: theme.layout.authTextFieldWidth,
      height: theme.layout.authTextFieldHeight,
      hasRadius: true,
      circular: theme.layout.authTextFieldCircular,
      iconColor: theme.themeData.colorScheme.tertiary,
      borderWidth: theme.layout.authTextFieldBorderWidth,
      borderColor: theme.themeData.colorScheme.tertiary,
      focusBorderColor: theme.themeData.colorScheme.secondary,
      mistakeBorderColor: theme.themeData.colorScheme.error,
      disabledBorderColor: theme.themeData.colorScheme.tertiary,

      hintText: 'auth.hint.verification.code'.t18,
      hintStyle: theme.textStyleExtension.authPageHintText,
      textStyle: theme.textStyleExtension.authPageInputText,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText,
      disabledTextStyle: theme.textStyleExtension.authPageHintText,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical,

      enableClearIcon: true,
      clearIconSize: theme.layout.authTextFieldClearIconSize,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static Widget buildInputBoxPassword(
      ThemePlugin theme,
      TextEditingController textEditingController,
      CommonTextFieldController controller,
      {String? hint, bool? enabled = false,
      bool? hasMistake = false,
      AsyncCallbackWithContextAndResult? onChanged,
      AsyncCallbackWithContext? onCleared}) {
    return CommonTextField2(
      textEditingController: textEditingController,
      controller: controller,
      keyboardType: TextInputType.visiblePassword,
      // isEnabled: enabled,
      // hasMistake: hasMistake,

      width: theme.layout.authTextFieldWidth,
      height: theme.layout.authTextFieldHeight,
      hasRadius: true,
      circular: theme.layout.authTextFieldCircular,
      iconColor: theme.themeData.colorScheme.tertiary,
      borderWidth: theme.layout.authTextFieldBorderWidth,
      borderColor: theme.themeData.colorScheme.tertiary,
      focusBorderColor: theme.themeData.colorScheme.secondary,
      mistakeBorderColor: theme.themeData.colorScheme.error,
      disabledBorderColor: theme.themeData.colorScheme.tertiary,

      hintText: hint?? 'auth.hint.password'.t18,
      hintStyle: theme.textStyleExtension.authPageHintText,
      textStyle: theme.textStyleExtension.authPageInputText,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText,
      disabledTextStyle: theme.textStyleExtension.authPageHintText,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical,

      enableClearIcon: true,
      clearIconSize: theme.layout.authTextFieldClearIconSize,
      passwordVisibilityIconSize: theme.layout.authTextFieldPasswordVisibilityIconSize,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static Widget buildSignUpLabel(ThemePlugin theme) {

    // Title bg
    Widget titleBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    //
    // final BoxShape? shape;
    // final Color? color;
    // final double? opacity;
    //
    // final double? radius;
    // final double? width;
    // final double? height;
    // final double? circular;
    // final BorderRadius? borderRadius;
    // final EdgeInsetsGeometry? margin;
    //
    // final bool? hasBorder;
    // final Color? borderColor;
    // final double? borderWidth;
    // final double? borderOpacity;
    //
    // final bool? hasShadow;
    // final Color? shadowColor;
    // final Offset? shadowOffset;
    // final double? shadowBlurRadius;
    // final double? shadowSpreadRadius;


    // Title  text
    Widget titleText = CommonText(
      'auth.label.signup'.t18,
      theme.textStyleExtension.authPageTitle,
    );

    // Desc bg
    Widget descBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    // Desc  text
    Widget descText = CommonText(
      'auth.label.signup.phone'.t18,
      theme.textStyleExtension.authPageDesc,
    );

    return Stack(
      alignment: Alignment.topLeft,
      children: [
        // Title
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Stack(
              alignment: Alignment.centerLeft,
              children: [
                titleBg,
                titleText,
              ],
            ),
          ],
        ),
        // Desc
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Column(
              children: [
                SizedBox(height: 33.w),
                Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    descBg,
                    descText,
                  ],
                ),
              ],
            ),
          ],
        )
      ],
    );
  }

  static Widget buildCheckEmailLabel(ThemePlugin theme) {
    // Title bg
    Widget titleBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    //
    // final BoxShape? shape;
    // final Color? color;
    // final double? opacity;
    //
    // final double? radius;
    // final double? width;
    // final double? height;
    // final double? circular;
    // final BorderRadius? borderRadius;
    // final EdgeInsetsGeometry? margin;
    //
    // final bool? hasBorder;
    // final Color? borderColor;
    // final double? borderWidth;
    // final double? borderOpacity;
    //
    // final bool? hasShadow;
    // final Color? shadowColor;
    // final Offset? shadowOffset;
    // final double? shadowBlurRadius;
    // final double? shadowSpreadRadius;

    // Title  text
    Widget titleText = CommonText(
      'auth.label.verify'.t18,
      theme.textStyleExtension.authPageTitle,
    );

    // Desc bg
    Widget descBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    // Desc  text
    Widget descText = CommonText(
      'auth.label.verify.desc'.t18,
      theme.textStyleExtension.authPageDesc,
    );

    return Stack(
      alignment: Alignment.topLeft,
      children: [
        // Title
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Stack(
              alignment: Alignment.centerLeft,
              children: [
                titleBg,
                titleText,
              ],
            ),
          ],
        ),
        // Desc
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Column(
              children: [
                SizedBox(height: 33.w),
                Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    descBg,
                    descText,
                  ],
                ),
              ],
            ),
          ],
        )
      ],
    );
  }

  static Widget buildSignUpEmailLabel(ThemePlugin theme) {

    // Title bg
    Widget titleBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    //
    // final BoxShape? shape;
    // final Color? color;
    // final double? opacity;
    //
    // final double? radius;
    // final double? width;
    // final double? height;
    // final double? circular;
    // final BorderRadius? borderRadius;
    // final EdgeInsetsGeometry? margin;
    //
    // final bool? hasBorder;
    // final Color? borderColor;
    // final double? borderWidth;
    // final double? borderOpacity;
    //
    // final bool? hasShadow;
    // final Color? shadowColor;
    // final Offset? shadowOffset;
    // final double? shadowBlurRadius;
    // final double? shadowSpreadRadius;


    // Title  text
    Widget titleText = CommonText(
      'auth.label.signup'.t18,
      theme.textStyleExtension.authPageTitle,
    );

    // Desc bg
    Widget descBg = CommonBar(
      width: theme.layout.authLabelTitleWidth,
      height: theme.layout.authLabelDescHeight,
      opacity: 0,
    );

    // Desc  text
    Widget descText = CommonText(
      'auth.label.login.email'.t18,
      theme.textStyleExtension.authPageDesc,
    );

    return Stack(
      alignment: Alignment.topLeft,
      children: [
        // Title
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Stack(
              alignment: Alignment.centerLeft,
              children: [
                titleBg,
                titleText,
              ],
            ),
          ],
        ),
        // Desc
        Row(
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            Column(
              children: [
                SizedBox(height: 33.w),
                Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    descBg,
                    descText,
                  ],
                ),
              ],
            ),
          ],
        )
      ],
    );
  }

  static Widget buildLabel(ThemePlugin theme, {Widget? title, Widget? desc}) {

    // Title  text
    Widget titleText = title ??
        CommonText(
          'auth.label.signup'.t18,
          theme.textStyleExtension.authPageTitle,
        );

    // Desc  text
    Widget descText = desc ??
        CommonText(
          'auth.label.login.email'.t18,
          theme.textStyleExtension.authPageDesc,
        );

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            titleText,
          ],
        ),
        SizedBox(
          height: theme.layout.authLabelLineSpace,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            descText,
          ],
        )
      ],
    );
  }

  static Widget buildPasswordRequirementPanel(ThemePlugin theme) {

    // password requirement title
    Widget titleText = CommonText(
      'auth.hint.password.rule.title'.t18,
      theme.textStyleExtension.authPagePassTipTitle,
    );

    // password requirement 1
    Widget req1 = CommonText(
      'auth.hint.password.rule.1'.t18,
      theme.textStyleExtension.authPagePassTipBody,
    );

    // password requirement 2
    Widget req2 = CommonText(
      'auth.hint.password.rule.2'.t18,
      theme.textStyleExtension.authPagePassTipBody,
    );

    // password requirement 3
    Widget req3 = CommonText(
      'auth.hint.password.rule.3'.t18,
      theme.textStyleExtension.authPagePassTipBody,
    );

    // password requirement 4
    Widget req4 = CommonText(
      'auth.hint.password.rule.4'.t18,
      theme.textStyleExtension.authPagePassTipBody,
    );

    return Column(
      children: [
        SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
                titleText,
              ],
            )),
        SizedBox(height: theme.layout.authLabelLineSpace,),
        SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
                req1,
              ],
            )
        ),
        SizedBox(height: theme.layout.authLabelLineSpace,),
        SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
                req2,
              ],
            )
        ),
        SizedBox(height: theme.layout.authLabelLineSpace,),
        SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
                req3,
              ],
            )
        ),
        SizedBox(height: theme.layout.authLabelLineSpace,),
        SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
                req4,
              ],
            )
        ),
      ],
    );
  }

  // New method --------------------------------------------

  static Widget buildInputBoxEmail2(ThemePlugin theme, CommonTextField3Controller controller) {

    logger.d('Object ID: ${identityHashCode(controller)}');

    return CommonTextField3(
      controller: controller,
    );
  }

  static CommonTextField3Controller buildEmailAddressTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      {Rx<GlobalKey>? globalKey, Rx<AsyncCallbackWithContext?>? onCleared, Rx<AsyncCallbackWithContextAndResult?>? onChanged}){

    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.emailAddress.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: 'auth.hint.email'.t18.obs,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,

    );
  }

  static Widget buildInputBoxPassword2(ThemePlugin theme, CommonTextField3Controller controller) {

    logger.i('Object ID: ${identityHashCode(controller)}');

    return CommonTextField3(
      controller: controller,
    );
  }

  static CommonTextField3Controller buildPasswordTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      {RxString? hint, Rx<GlobalKey>? globalKey, Rx<AsyncCallbackWithContext?>? onCleared, Rx<AsyncCallbackWithContextAndResult?>? onChanged}){

    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.visiblePassword.obs,
      obscureText: true.obs,
      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hint?? 'auth.hint.password'.t18.obs,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      enablePasswordVisibilityIcon: true.obs,
      passwordVisibilityIconSize: theme.layout.authTextFieldPasswordVisibilityIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static Widget buildInputBoxPhone2(ThemePlugin theme, CommonTextField3Controller controller) {

    logger.i('Object ID: ${identityHashCode(controller)}');

    return CommonTextField3(
      controller: controller,
    );
  }

  static CommonTextField3Controller buildPhoneNumberTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      {Rx<GlobalKey>? globalKey, Rx<AsyncCallbackWithContext?>? onCleared, Rx<AsyncCallbackWithContextAndResult?>? onChanged}){

    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.phone.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: 'auth.hint.phone'.t18.obs,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,

    );
  }

  static Widget buildInputBoxVerificationCode2(ThemePlugin theme, CommonTextField3Controller controller) {

    logger.d('Object ID: ${identityHashCode(controller)}');

    return CommonTextField3(
      controller: controller,
    );
  }

  static CommonTextField3Controller buildVerificationCodeTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      {Rx<GlobalKey>? globalKey, Rx<AsyncCallbackWithContext?>? onCleared, Rx<AsyncCallbackWithContextAndResult?>? onChanged}){

    return CommonTextField3Controller(
      textEditingController: textEditingController,
      globalKey: globalKey,
      keyboardType: TextInputType.number.obs,
      isEnabled: false.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: 'auth.hint.verification.code'.t18.obs,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      enableClearIcon: true.obs,
      clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,

    );
  }

  static Widget buildButton2(ThemePlugin theme, CommonButton3Controller controller) {

    // logger.i('Object ID: ${identityHashCode(controller)}');

    return CommonButton3(
      // isEnabled: isEnabled,
      controller: controller,
    );
  }
}

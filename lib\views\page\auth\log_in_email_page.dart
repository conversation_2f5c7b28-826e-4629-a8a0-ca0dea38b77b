import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/auth_config.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/page/auth/forgot_password_page.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

import '../root_page.dart';

class LogInEmailPage extends StatefulWidget {
  const LogInEmailPage({super.key});
  @override
  LogInEmailPageState createState() => LogInEmailPageState();
}

class LogInEmailPageState extends State<LogInEmailPage> {
  late Future<LogInEmailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => LogInEmailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<LogInEmailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  AuthWidgetBuilder.buildWhiteSection(controller.theme),
                  Column(
                    children: [
                      // Avatar
                      SizedBox(
                        height: controller.theme.layout.authAvatarHeaderLineHeight
                      ),

                      // Sign up label
                      SizedBox(
                        height: 149.w,
                          child: Column(
                          children: [
                            Expanded(child: SizedBox.shrink()),
                            Obx(()=> AuthWidgetBuilder.buildLabel(
                                controller.theme,
                                title: controller.pageLabelTitle.value,
                                desc: controller.pageLabelDesc.value)
                            ),
                            SizedBox(height: 20.w,),
                          ],
                        )
                      ),

                      Expanded(
                        child: SingleChildScrollView(
                          padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).viewInsets.bottom,
                          ),
                          controller: controller.scrollController,
                          child: Column(
                          children: [
                            // email input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxEmail2(
                                  controller.theme, controller.usernameInputController,
                                )),
                            SizedBox(height: 20.w,),

                            // password input box
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildInputBoxPassword2(
                                  controller.theme, controller.passwordInputController,
                                )),
                            SizedBox(height: 8.w,),

                            // forgot password button
                            SizedBox(

                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 38.w,
                                  ),
                                  SizedBox(
                                    child: AuthWidgetBuilder.buildButton2(
                                      controller.theme,
                                      controller.forgotPasswordButtonController,
                                    ),
                                  ),
                                  Expanded(child: SizedBox.shrink())
                                ]
                              ),
                            ),

                            SizedBox(
                              height: screenSize.height
                                  - controller.theme.layout.authAvatarHeaderLineHeight
                                  - 149.w
                                  - 140.w
                                  - 24.w
                                  - 160.w,
                            ),
                            // Expanded(child: SizedBox.shrink()),

                            // submit button
                            SizedBox(
                                height: 60.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.buttonController,
                                )),
                            SizedBox(height: 20.w,),

                            // Change to email
                            SizedBox(
                                height: 50.w,
                                child: AuthWidgetBuilder.buildButton2(
                                  controller.theme,
                                  controller.alterButtonController,
                                )),
                            SizedBox(height: 30.w,),
                          ],
                        )),
                      ),
                    ],
                  ),
                  AuthWidgetBuilder.buildAvatarHeaderLine(controller.theme, context),
                ],
              ),
            );
          }
        });
  }
}

class LogInEmailPageController extends GetxController {

  // Services
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // TextEditingController
  final TextEditingController usernameInputBox = TextEditingController();
  late CommonTextField3Controller usernameInputController;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;

  final ScrollController scrollController = ScrollController();
  final _usernameKey = GlobalKey(debugLabel: 'usernameKey${DateTime.now().millisecondsSinceEpoch}');
  final _passwordKey = GlobalKey(debugLabel: 'passwordKey${DateTime.now().millisecondsSinceEpoch}');

  // Submit buttons
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  late CommonButton3Controller forgotPasswordButtonController;

  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);

  var buttonText = ''.obs;
  var onPressed = Rx<AsyncCallback?>(null);

  var isEmailAddressValid = false.obs;
  var isEmailAddressVerified = false.obs;
  var isPasswordValid = false.obs;
  var isVerificationCodeSent = false.obs;
  var isReSendEnabled = false.obs;
  var remainedTime = 30.obs;
  Timer? timer;

  final String validCode = '123';

  // Theme plugin
  late ThemePlugin theme;

  Future<LogInEmailPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageLabelTitle.value = CommonText(
      'auth.label.login'.t18,
      theme.textStyleExtension.authPageTitle
    );

    pageLabelDesc.value = CommonText(
      'auth.label.login.email'.t18,
      theme.textStyleExtension.authPageDesc
    );

    // build username input controller
    usernameInputController = AuthWidgetBuilder.buildEmailAddressTextFieldController(
      theme, usernameInputBox.obs,
      globalKey: _usernameKey.obs,
      onCleared: onEmailAddressCleared.obs,
      onChanged: isInputEmailAddressValid.obs,
    );
    // Listen to the focus node
    usernameInputController.textFieldFocusNode.addListener(() {
      usernameInputController.isFocused.value = usernameInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_usernameKey);
    });

    // build password input controller
    passwordInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, passwordInputBox.obs,
      globalKey: _passwordKey.obs,
      hint: 'auth.hint.password'.t18.obs,
      onCleared: onPasswordCleared.obs,
      onChanged: isInputPasswordValid.obs,
    );
    // Listen to the focus node
    passwordInputController.textFieldFocusNode.addListener(() {
      passwordInputController.isFocused.value = passwordInputController.textFieldFocusNode.hasFocus;
      _scrollToFocusedField(_passwordKey);
    });

    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.login.2'.t18.obs,
      onPressed: login.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.change.to.phone'.t18.obs,
      onPressed: alter.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    forgotPasswordButtonController = CommonButton3Controller(
      width: 105.w.obs,
      height: 20.w.obs,
      isEnabled: true.obs,
      text: 'auth.button.forgot.password'.t18.obs,
      onPressed: forgotPassword.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageForgotPassword.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    timer?.cancel();
    // scrollController.dispose();
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputController.textFieldFocusNode.unfocus();
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  void _scrollToFocusedField(GlobalKey key) {
    if (key.currentContext != null && ((key == _usernameKey && usernameInputController.textFieldFocusNode.hasFocus) ||
        (key == _passwordKey && passwordInputController.textFieldFocusNode.hasFocus))) {
      // Delay to ensure keyboard is shown before scrolling
      Future.delayed(Duration(milliseconds: 300), () {
        Scrollable.ensureVisible(
          key.currentContext!,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  Future<void> startRefreshEmailVerifyStatus() async {

    isEmailAddressVerified.value = false;
    timer?.cancel();

    timer = Timer.periodic(Duration(seconds: 5), (timer) async {

      bool? verified = await authService.checkEmailVerified();

      if (verified != null) {
        if (verified) {
          timer.cancel();

          // query user account from firestore
          String fid = authService.currentUser.value!.uid;
          UserAccount? account = await userDao.getUserAccountById(fid: fid);

          // account exists => set email verified to true
          if (account != null) {
            account.isEmailVerified = true;

            // update user account
            await userService.updateUserAccount(account);

            // TODO - Go to add pet page
            await resetState();
            Get.offAll(RootPage());
            return;
          }
        }
      } else {

        // TODO resend verification email
        // Get.to(LogInPage());
      }
    });
  }

  bool isButtonEnabled() {

    return isEmailAddressValid.isTrue && isPasswordValid.isTrue;
  }

  bool checkPasswordMistake() {
    bool hasMistake = isEmailAddressValid.isTrue && isPasswordValid.isFalse && passwordInputBox.text != '';
    return hasMistake;
  }

  Future<void> onEmailAddressCleared(BuildContext context) async {
    await resetEmailAddressState();
  }

  Future<void> onPasswordCleared(BuildContext context) async {

    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    passwordInputController.textFieldFocusNode.unfocus();
    isEmailAddressValid.value = true;
    isPasswordValid.value = false;
    isVerificationCodeSent.value = false;

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputEmailAddressValid({required BuildContext context, required bool result}) async {
    isEmailAddressValid.value = result;
    if (isEmailAddressValid.isFalse && usernameInputBox.text.length > 10) {
      usernameInputController.hasMistake.value = true;
    }


    if (isEmailAddressValid.isTrue && isPasswordValid.isTrue) {
      buttonController.isEnabled.value = true;
    }
    // else {
    //   buttonController.isEnabled.value = false;
    // }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputPasswordValid({required BuildContext context, required bool result}) async {
    isPasswordValid.value = result;

    passwordInputController.hasMistake.value = isPasswordValid.isFalse && passwordInputBox.text != '';

    if (isEmailAddressValid.isTrue && isPasswordValid.isTrue) {
      buttonController.isEnabled.value = true;
    }
    // else {
    //   buttonController.isEnabled.value = false;
    // }

    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> login() async {

    String email = usernameInputBox.text;
    String password = passwordInputBox.text;

    if (!RegExp(InputUtil.validEmailAddressRegEx).hasMatch(email)) {
      Get.snackbar('auth.error.title'.t18, 'auth.error.email.invalid'.t18);
      return;
    }

    // Login with email and password
    ServiceResponse<User> response = await authService.loginInWithEmailAndPassword(email, password);

    if (response.code == 200) {

      // save user account
      await userService.createUserAccount(authService.currentUser.value!, AuthChannel.emailPassword);

      // add auth record in Firestore and security storage
      await userService.recordAuthActivity(response.data!, AuthChannel.emailPassword);

      // Create user data if not exists
      if (authService.userData.value == null || authService.userData.value!.uid != authService.userAccount.value!.sid) {
        UserData? ud = await userService.getUserDataById(userId: authService.userAccount.value!.sid!);
        if (ud == null) {
          UserData data = UserData.create(
            uid: authService.userAccount.value!.sid!,
          );
          await userService.createUserData(data);
        } else {
          authService.userData.value = ud;
        }
      }

      // Email verified
      if (response.data!.emailVerified) {

        // Persist the user account and auth record in Firestore and secure storage
        // await userService.createUserAccount(response.data!, AuthChannel.emailPassword, salt: salt, hashedPassword: hashedPassword);
        // loggedInUser.value = response.data;

        await resetState();
        Get.to(RootPage());
        return;
      }
      // Email not verified
      else {

        // Disable all input boxes and buttons after sending the verification email
        usernameInputController.hasMistake.value = false;
        // usernameInputController.isEnabled.value = false;
        passwordInputController.obscureText.value = true;
        passwordInputController.hasMistake.value = false;
        // passwordInputController.isEnabled.value = false;
        buttonController.isEnabled.value = false;

        // Send verification email
        await response.data!.sendEmailVerification();

        // Redirect to verify page
        await resetState();
        Get.to(PendingVerifyPage());
        return;

        // Show message to user
        // pageLabelDesc.value = CommonText(
        //   'auth.label.login.email.sent'.t18,
        //   theme.textStyleExtension.authPageNotification,
        //   width: 350.w,
        //   height: 50.w,
        //   maxLines: 2,
        //   softWrap: true,
        // );

        // Refresh to check email verification status
        // await startRefreshEmailVerifyStatus();
      }
    }
    else {
      // Show message to user
      pageLabelDesc.value = CommonText(
        'auth.error.login.failed'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );
    }
  }

  Future<void> alter() async {
    await resetState();
    Get.to(()=> LogInPage());
  }

  Future<void> resetState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
        'auth.label.login.email'.t18,
        theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    usernameInputController.textFieldFocusNode.unfocus();
    passwordInputBox.text = '';
    passwordInputController.isEnabled.value = true;
    passwordInputController.hasMistake.value = false;
    passwordInputController.textFieldFocusNode.unfocus();

    // Reset states
    isEmailAddressValid.value = false;
    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    isVerificationCodeSent.value = false;
    isPasswordValid.value = false;

    // Button
    buttonController.text = 'auth.button.login.2'.t18.obs;
    buttonController.onPressed.value = login;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> resetEmailAddressState() async {

    // Cancel timer
    timer?.cancel();

    // Label
    pageLabelDesc.value = CommonText(
      'auth.label.login.email'.t18,
      theme.textStyleExtension.authPageDesc
    );

    // Text field
    usernameInputBox.text = '';
    usernameInputController.isEnabled.value = true;
    usernameInputController.hasMistake.value = false;
    usernameInputController.textFieldFocusNode.unfocus();

    // Reset states
    isEmailAddressValid.value = false;
    // isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    // isVerificationCodeSent.value = false;
    // isPasswordValid.value = false;

    // Button
    buttonController.text = 'auth.button.login.2'.t18.obs;
    buttonController.onPressed.value = login;
    buttonController.isEnabled.value = false;
    buttonController.isInProgress.value = false;
    await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> forgotPassword() async {
    await resetState();
    Get.to(()=> ForgotPasswordPage());
    return;
  }
}

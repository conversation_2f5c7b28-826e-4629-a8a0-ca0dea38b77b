import 'dart:async';
import 'dart:math' as logger;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../common/const/auth_config.dart';
import '../../../common/enum/auth_channel.dart';
import '../../../common/enum/supported_area_code.dart';
import '../../../dao/user_dao.dart';
import '../../../models/user_account.dart';
import '../../../services/auth_service.dart';
import '../../../services/service_response.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../auth/sign_up_email_page.dart';
import '../page_interceptor.dart';
import '../root_page.dart';

class UserSettingPasswordPage extends StatefulWidget {
  const UserSettingPasswordPage({super.key});

  @override
  UserSettingPasswordPageState createState() => UserSettingPasswordPageState();
}

class UserSettingPasswordPageState extends State<UserSettingPasswordPage> {
  late Future<UserSettingPasswordPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller =
        Get.putAsync(() => UserSettingPasswordPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingPasswordPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Obx(() {
                return Stack(
                  children: [
                    ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        //Get.back();
                        controller.clearStepInput(controller.step.value);
                        if (controller.step.value > 1 && controller.step.value<4) {
                          controller.step.value -= 1;
                        } else {
                          Get.back();
                        }
                      },
                      topic: "Password",
                      showAvatar: false,
                    ),
                    if (controller.step.value == 1)
                      buildVerificationMethodSection(controller),
                    if (controller.step.value == 2)
                      _buildCodeSection(controller),
                    if (controller.step.value == 3)
                      _buildOtpInputSection(controller),
                    if (controller.step.value == 4)
                      _buildOutputSection(controller),
                    if (controller.step.value != 1 &&
                        controller.step.value != 4)
                      ProfileWidgetBuilder.buildButton2(
                        controller.theme,
                        controller.buttonController,
                        721,
                      ),
                    if (controller.step.value != 1 &&
                        controller.step.value != 4)
                      ProfileWidgetBuilder.buildButton2(
                        controller.theme,
                        controller.alterButtonController,
                        781,
                      ),
                  ],
                );
              }),
            );
          }
        });
  }

  ///步骤 1
  Widget buildVerificationMethodSection(
      UserSettingPasswordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText(
            controller.theme, 128, 'Choose a verification method'),
        ProfileWidgetBuilder.buildUserSettingProfileSection(
            theme: controller.theme,
            top: 20,
            title: controller.userAccount.value!.phoneNumber??'userPhone',
            onRightIconTap: controller.nextStep,
            rightIconColor: controller.theme.themeData.colorScheme.onSurface),
        if (controller.userAccount.value?.isEmailVerified == true)
          ProfileWidgetBuilder.buildUserSettingProfileSection(
            theme: controller.theme,
            top: 20,
            title: controller.userAccount.value!.email ?? 'userEmail',
            rightIconColor: controller.theme.themeData.colorScheme.onSurface,
          ),
        // ProfileWidgetBuilder.buildUserSettingProfileSection(
        //     theme: controller.theme,
        //     top: 20,
        //     title: controller.userAccount.value!.email??'userEmail',
        //     //onRightIconTap: controller.nextStep,
        //     rightIconColor: controller.theme.themeData.colorScheme.onSurface),

        // ProfileWidgetBuilder.buildUserSettingProfileSection(
        //     theme: controller.theme,
        //     top: 20,
        //     title: controller.userAccount.value!.phoneNumber??'userPhone',//'**********',
        //     onRightIconTap: controller.nextStep,
        //     rightIconColor: controller.theme.themeData.colorScheme.onSurface),
      ],
    );
  }

  ///步骤 2
  Widget _buildCodeSection(UserSettingPasswordPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText(
            controller.theme, 128, 'Verification code'),
        SizedBox(height: 12.h),
        _buildCodeField(controller),
        SizedBox(
          height: 20.w,
        ),
        _buildResendSection(controller),
        //_buildInvalidCodeMessage(controller),
      ],
    );
  }

  Widget _buildCodeField(UserSettingPasswordPageController controller) {
    return Obx(
      () => Center(
        child: AuthWidgetBuilder.buildInputBoxVerificationCode(
          controller.theme,
          controller.codeController,
          controller.codeInputController,
          hasMistake: !controller.isVerificationCodeValid.value,
          onCleared: controller.onVerificationCodeCleared,
          onChanged: controller.isInputVerificationCodeValid,
          enabled: true,
        ),
      ),
    );
  }

  Widget _buildResendSection(UserSettingPasswordPageController controller) {
    return Obx(
      () => (controller.isVerificationCodeSent.isTrue)
          ? controller.isReSendEnabled.value
              ? GestureDetector(
                  onTap: () async {
                    await controller.sendVerificationCodeOfPhoneNumber();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      CommonText('auth.button.resend'.t18,
                          controller.theme.textStyleExtension.authPageResend),
                      SizedBox(
                        width: 38.w,
                      )
                    ],
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(() => CommonText(
                        'auth.button.resend.in.second'.t19({
                          'second': '${controller.remainedTime.value.toInt()}'
                        }),
                        controller
                            .theme.textStyleExtension.authPageResendDisabled)),
                    SizedBox(
                      width: 38.w,
                    )
                  ],
                )
          : SizedBox.shrink(),
    );
  }

  ///步骤 3
  Widget _buildOtpInputSection(UserSettingPasswordPageController controller) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      ProfileWidgetBuilder.buildText(controller.theme, 128, 'New password'),
      SizedBox(height: 12.h),
      Center(
        child: SizedBox(
          height: 60.w,
          child: AuthWidgetBuilder.buildInputBoxPassword2(
            controller.theme,
            controller.passwordInputController,
          ),
        ),
      ),
      SizedBox(
        height: 10.w,
      ),
      Center(
        child: SizedBox(
          height: 60.w,
          child: AuthWidgetBuilder.buildInputBoxPassword2(
            controller.theme,
            controller.confirmInputController,
          ),
        ),
      ),
      SizedBox(height: 20.w),
      SizedBox(
        child:
            AuthWidgetBuilder.buildPasswordRequirementPanel(controller.theme),
      ),
    ]);
  }

  ///步骤 4
  Widget _buildOutputSection(UserSettingPasswordPageController controller) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      ProfileWidgetBuilder.buildText(controller.theme, 128, 'New password'),
      ProfileWidgetBuilder.buildText2(controller.theme, 12,
          "Your password has been updated, use the new password\nnext time you log in."),
    ]);
  }
}

class UserSettingPasswordPageController extends GetxController {
  var userAccount = Rx<UserAccount?>(null);
  final UserDao userDao = UserDao();
  final UserService userService = UserService();
  final AuthService authService = AuthService.instance;

  final TextEditingController codeInputBox = TextEditingController();
  late CommonTextField3Controller codeInputController1;
  final TextEditingController passwordInputBox = TextEditingController();
  late CommonTextField3Controller passwordInputController;
  final TextEditingController confirmInputBox = TextEditingController();
  late CommonTextField3Controller confirmInputController;

  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  final CommonTextFieldController codeInputController =
      Get.put(CommonTextFieldController(), tag: 'user-setting-password-code');

  final TextEditingController codeController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController reenterPasswordController =
      TextEditingController();

  var isVerificationLinkSent = false.obs;
  var isPasswordValid = false.obs;
  var isPasswordConfirmed = false.obs;
  var verificationId = ''.obs;
  //var verificationCode = ''.obs;
  var isReSendEnabled = false.obs;
  var remainedTime = 30.obs;

  var isVerificationCodeSent = true.obs;
  var isVerificationCodeValid = false.obs;
  var isPhoneNumberValid = false.obs;
  var onPressed = Rx<AsyncCallback?>(null);
  var pageLabelTitle = Rx<Widget?>(null);
  var pageLabelDesc = Rx<Widget?>(null);
  var buttonText = ''.obs;

  //
  final RxInt step = 1.obs;
  final RxBool isCodeValid = true.obs;
  final RxBool canResend = false.obs;
  Timer? timer;

  late ThemePlugin theme;

  Future<UserSettingPasswordPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());
    userAccount.value = authService.userAccount.value;

    passwordInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, passwordInputBox.obs,
      hint: 'auth.hint.password'.t18.obs,
      onCleared: onPasswordCleared.obs,
      onChanged: isInputPasswordValid.obs,
    );

    confirmInputController = AuthWidgetBuilder.buildPasswordTextFieldController(
      theme, confirmInputBox.obs,
      hint: 'auth.hint.re-enter.password'.t18.obs,
      onCleared: onConfirmCleared.obs,
      onChanged: isInputConfirmValid.obs,
    );
    // Listen to the focus node
    confirmInputController.textFieldFocusNode.addListener(() {
      confirmInputController.isFocused.value =
          confirmInputController.textFieldFocusNode.hasFocus;
    });

    buttonController = CommonButton3Controller(
      isEnabled: false.obs,
      text: 'auth.button.send.link'.t18.obs,
      onPressed: onSubmitPressed.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.change.to.phone'.t18.obs,
      onPressed: alter.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    return this;
  }

  @override
  void onInit() {
    super.onInit();
    timer?.cancel();
    buttonController.isInProgress.value = false;
  }

  @override
  void onClose() {
    timer?.cancel();
    passwordInputController.textFieldFocusNode.dispose();
    confirmInputController.textFieldFocusNode.dispose();
    buttonController.isInProgress.value = false;
    super.onClose();
  }

  void clearStepInput(int step) {
    switch (step) {
      case 1:
        break;
      case 2:
        break;
      case 3:
        codeController.text = "";
        break;
      case 4:
        passwordController.text = "";
        reenterPasswordController.text = "";
        break;
    }
  }

  Future<void> nextStep() async {
    if (step.value == 1) {
      step.value = 2;
      await sendVerificationCodeOfPhoneNumber();
      startCountdown();
    }
    // else if (step.value == 2) {
    //   if (codeController.text == "123456") {
    //     step.value = 3; // **正确进入 Step 3**
    //     isCodeValid.value = true;
    //     buttonController.isEnabled.value = false;
    //   } else {
    //     isCodeValid.value = false; // **错误时标记无效**
    //     updateButtonState();
    //     //enableButton.value = false; // **按钮变灰**
    //   }
    // }
    else if (step.value == 2) {
      await verifyOtpFromNextStep();
    }
    else if (step.value == 3) {
      step.value = 4;
    }
  }

  void startCountdown() {
    isReSendEnabled.value = false;
    remainedTime.value = AuthConfig.resendVerificationCodeSuspendTIme;
    timer?.cancel();
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainedTime.value == 0) {
        isReSendEnabled.value = true;
        timer.cancel();
      } else {
        remainedTime.value--;
      }
    });
  }

  void updateButtonState() {
    if (step.value == 2) {
      final hasInput = codeController.text.isNotEmpty;
      final codeNotInvalidOrCountdown = isCodeValid.value || !canResend.value;

      buttonController.isEnabled.value = hasInput && codeNotInvalidOrCountdown;
    } else if (step.value == 3) {
      final pwd = passwordInputBox.text;
      final confirm = confirmInputBox.text;
      final isValidPwd = validatePassword(pwd);
      final isConfirmed = pwd == confirm;

      buttonController.isEnabled.value = isValidPwd && isConfirmed;
    } else {
      buttonController.isEnabled.value = false;
    }
  }

  bool validatePassword(String password) {
    return password.length >= 8 &&
        RegExp(r"[0-9]").hasMatch(password) &&
        RegExp(r"[A-Z]").hasMatch(password) &&
        RegExp(r"[#\$!%]").hasMatch(password);
  }

  Future<void> onVerificationCodeCleared(BuildContext context) async {
    isVerificationCodeSent.value = true;
    isVerificationCodeValid.value = false;
    onPressed.value = verifyOtp;
    FocusScope.of(context).unfocus(); // ✅ Close keyboard first
    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputVerificationCodeValid(
      {required BuildContext context, required bool result}) async {
    isVerificationCodeValid.value = result;
    onPressed.value = verifyOtp;
    // FocusScope.of(context).unfocus(); // ✅ Close keyboard first
    updateButtonState();
    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> sendVerificationCodeOfPhoneNumber() async {
    //if (!isPhoneNumberValid.value) return;

    String phoneNumber = authService.currentUser.value!.phoneNumber!;
    //String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';
    // String phoneNumber = InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text);
    // String phoneNumber = kReleaseMode ? InputUtil.toOriginPhoneNumber(phoneNumber: usernameInputBox.text) : '+***********';

    // Check existing
    // UserAccount? userAccount =
    //     await userDao.getUserAccountById(phoneNumber: phoneNumber);
    // if (userAccount != null) {
    //   Get.snackbar('auth.error.title'.t18, 'auth.error.number.exists'.t18);
    //   return;
    // }

    // Check existing
    ServiceResponse<String> response =
        await authService.sendVerificationCodePhoneNumber(phoneNumber);

    if (response.code == 201) {
      // save user account
      await userService.createUserAccount(
          authService.currentUser.value!, AuthChannel.sms);

      // add auth record in Firestore and security storage
      await userService.recordAuthActivity(
          authService.currentUser.value!, AuthChannel.sms);

      isReSendEnabled.value = false;
      timer?.cancel();

      // Login success
      Get.offAll(RootPage());
      return;
    } else if (response.code == 200) {
      verificationId.value = response.data!;
      isReSendEnabled.value = true;
      isVerificationCodeSent.value = true;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.sent'.t18,
        theme.textStyleExtension.authPageNotification,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      codeController.text = '';
      codeInputController.isEnabled.value = true;
      codeInputController.obscureText.value = true;
      codeInputController.hasMistake.value = false;

      buttonController.isEnabled.value = false;
      onPressed.value = verifyOtp;

      startCountdown();
    } else if (response.code == 408) {
      verificationId.value = response.data!;

      pageLabelDesc.value = CommonText(
        'auth.label.login.code.timeout'.t18,
        theme.textStyleExtension.authPageWarning,
        width: 350.w,
        height: 50.w,
        maxLines: 2,
        softWrap: true,
      );

      codeController.text = '';
      codeInputController.obscureText.value = true;
      codeInputController.hasMistake.value = false;
      codeInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      onPressed.value = sendVerificationCodeOfPhoneNumber;
    } else {
      Get.snackbar('auth.error.title'.t18, 'auth.error.desc'.t18);

      codeController.text = '';
      codeInputController.obscureText.value = true;
      codeInputController.hasMistake.value = false;
      codeInputController.isEnabled.value = false;

      isReSendEnabled.value = false;
      buttonController.isEnabled.value = true;
      onPressed.value = sendVerificationCodeOfPhoneNumber;
    }
  }

  Future<void> verifyOtp() async {
    String phoneNumber = authService.currentUser.value!.phoneNumber!;
    //String phoneNumber = '${SupportedAreaCode.northAmerica.code} ${usernameInputBox.text}';

    try {
      // Sign in the user with the sms code
      ServiceResponse<User> response = await authService.verifyCodeLogIn(
          phoneNumber, verificationId.value, codeController.text);

      if (response.code == 200) {
        // Update current user in auth service
        // authService.currentUser.value = response.data!;

        // save user account in Firestore and security storage
        await userService.createUserAccount(
            authService.currentUser.value!, AuthChannel.sms);

        // add auth record in Firestore and security storage
        await userService.recordAuthActivity(
            authService.currentUser.value!, AuthChannel.sms);

        isReSendEnabled.value = false;
        timer?.cancel();

        // TODO - Go to add pet page
        Get.offAll(RootPage());
        return;
      } else {
        pageLabelDesc.value = CommonText(
          'auth.error.code.invalid'.t18,
          theme.textStyleExtension.authPageWarning,
          width: 350.w,
          height: 50.w,
          maxLines: 2,
          softWrap: true,
        );

        codeController.text = '';
        codeInputController.obscureText.value = true;
        codeInputController.hasMistake.value = false;
        codeInputController.isEnabled.value = false;

        isReSendEnabled.value = false;
        buttonController.isEnabled.value = true;
        isVerificationCodeSent.value = false;
        onPressed.value = sendVerificationCodeOfPhoneNumber;
      }
    } catch (e) {
      isVerificationCodeValid.value = false;
    }
  }

  Future<void> isInputPasswordValid(
      {required BuildContext context, required bool result}) async {
    isPasswordValid.value = result;
    passwordInputController.hasMistake.value =
        isPasswordValid.isFalse && passwordInputBox.text != '';

    if (isPasswordValid.isTrue && isPasswordConfirmed.isTrue) {
      buttonController.isEnabled.value = true;
    } else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> isInputConfirmValid(
      {required BuildContext context, required bool result}) async {
    isPasswordConfirmed.value = result;
    confirmInputController.hasMistake.value =
        isPasswordConfirmed.isFalse && confirmInputBox.text != '';

    if (isPasswordValid.isTrue && isPasswordConfirmed.isTrue) {
      buttonController.isEnabled.value = true;
    } else {
      buttonController.isEnabled.value = false;
    }

    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> onPasswordCleared(BuildContext context) async {
    passwordInputBox.text = '';
    isPasswordValid.value = false;
    isPasswordConfirmed.value = false;
    isVerificationLinkSent.value = false;
    timer?.cancel();

    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> onConfirmCleared(BuildContext context) async {
    confirmInputBox.text = '';
    isPasswordConfirmed.value = false;
    isVerificationLinkSent.value = false;
    timer?.cancel();

    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> verifyOtpFromNextStep() async {
    try {
      ServiceResponse<User> response = await authService.verifyCodeLogIn(
        authService.currentUser.value!.phoneNumber!,
        verificationId.value,
        codeController.text,
      );

      if (response.code == 200) {
        step.value = 3;
        isCodeValid.value = true;
        buttonController.isEnabled.value = false;
      } else {
        isCodeValid.value = false;
        updateButtonState();
      }
    } catch (e) {
      isCodeValid.value = false;
      updateButtonState();
    }
  }

  Future<void> onSubmitPressed() async {
    // if (step.value == 2) {
    //   // Step 2: 验证验证码
    //   if (codeController.text != "123456") {
    //     isCodeValid.value = false;
    //     updateButtonState();
    //     return;
    //   }
    //   isCodeValid.value = true;
    //   step.value = 3;
    //   buttonController.isEnabled.value = false;
    //   return;
    // }
    if (step.value == 2) {
      try {
        ServiceResponse<User> response = await authService.verifyCodeLogIn(
          authService.currentUser.value!.phoneNumber!,
          verificationId.value,
          codeController.text,
        );
        if (response.code == 200) {
          isCodeValid.value = true;
          step.value = 3;
          buttonController.isEnabled.value = false;
        } else {
          isCodeValid.value = false;
          updateButtonState();
        }
      } catch (e) {
        isCodeValid.value = false;
        updateButtonState();
      }
      return;
    }

    if (step.value == 3) {
      // Step 3: 设置密码
      if (passwordInputBox.text != confirmInputBox.text) {
        pageLabelDesc.value = CommonText(
          'auth.error.password.not.match'.t18,
          theme.textStyleExtension.authPageWarning,
        );
        return;
      }

      if (passwordInputBox.text.isEmpty || confirmInputBox.text.isEmpty) {
        pageLabelDesc.value = CommonText(
          'Password fields cannot be empty.',
          theme.textStyleExtension.authPageWarning,
        );
        return;
      }

      await authService.currentUser.value?.updatePassword(passwordInputBox.text);
      //ServiceResponse<String?> response = await authService.updateUserPassword(oldPassword, passwordInputBox.text);

       if (ServiceResponse == 200) {
         step.value = 4; // 密码设置成功
       }
       else {
         Get.snackbar('auth.error.title'.t18, 'auth.error.reset.password.failed'.t18);
       }
      return;
    }

    // 如果不是 Step 2 或 3，默认不做事
    return;
  }

  Future<void> alter() async {
    timer?.cancel();
    buttonController.isInProgress.value = false;
    //Get.to(()=> SignUpPage());
  }
}

import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/views/theme/colors/colors_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';

import '../auth/auth_widget_builder.dart';
import '../widgets/common_i.dart';

class ProfileWidgetBuilder {
  static Widget buildWelcomeAvatar(ThemePlugin theme) {
    // Background
    CommonBar bg = CommonBar(
      shape: BoxShape.circle,
      color: theme.themeData.colorScheme.surface,
      radius: theme.layout.authWelcomeAvatarSize,
      hasShadow: true,
      shadowColor: theme.colorExtension.authAvatarShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Avatar
    CommonImageAsset avatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.authWelcomeAvatarSize,
      height: theme.layout.authWelcomeAvatarSize,
    );

    return CommonAvatar(
      hasBg: true,
      bg: bg,
      hasContent: true,
      content: avatar,
    );
  }

  static Widget buildBackButton(ThemePlugin theme, BuildContext context) {
    // Background
    CommonBar bg = CommonBar(
      color: theme.themeData.colorScheme.surface,
      width: theme.layout.authBackButtonBgSize,
      height: theme.layout.authBackButtonBgSize,
      circular: theme.layout.authBackButtonBgCircular,
      hasShadow: true,
      shadowColor: theme.colorExtension.authBackButtonShadow,
      shadowOffset: theme.layout.authBackButtonBgShadowOffset,
      shadowBlurRadius: theme.layout.authBackButtonBgShadowBlur,
    );

    // Icon
    CommonImageAsset icon = CommonImageAsset(
      path: ImageAssetConst.iconArrowLeft,
      color: theme.themeData.colorScheme.primary,
      width: theme.layout.authBackButtonIconWidth,
      height: theme.layout.authBackButtonIconHeight,
    );

    // Button
    return GestureDetector(
      onTap: () async {
        // FocusScope.of(context).unfocus(); // ✅ Close keyboard first
        await Future.delayed(
            Duration(milliseconds: 200)); // To ensure the keyboard is closed
        Get.back();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          bg,
          icon,
        ],
      ),
    );
  }

  static Widget buildAvatar(ThemePlugin theme, {bool? editable = false}) {
    // Background
    CommonBar bg = CommonBar(
      shape: BoxShape.circle,
      color: theme.themeData.colorScheme.surface,
      radius: theme.layout.authAvatarSize,
      hasShadow: true,
      shadowColor: theme.colorExtension.authAvatarShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Avatar
    CommonImageAsset avatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.authAvatarSize,
      height: theme.layout.authAvatarSize,
    );

    // Camera bar
    CommonBar? cameraBar = !editable!
        ? null
        : CommonBar(
            color: theme.themeData.colorScheme.surface,
            width: theme.layout.profileAvatarCameraBarWidth,
            height: theme.layout.profileAvatarCameraBarHeight,
            circular: theme.layout.profileAvatarCameraBarCircular,
            hasShadow: true,
            shadowColor: theme.colorExtension.profileAvatarCameraShadow,
            shadowOffset: theme.layout.authAvatarShadowOffset,
            shadowBlurRadius: theme.layout.authAvatarShadowBlur,
          );

    // Camera icon
    CommonImageAsset? cameraIcon = !editable
        ? null
        : CommonImageAsset(
            path: ImageAssetConst.iconImage,
            width: theme.layout.profileAvatarCameraIconWidth,
            height: theme.layout.profileAvatarCameraIconHeight,
          );

    return CommonAvatar(
      hasBg: true,
      bg: bg,
      hasContent: true,
      content: avatar,
      changeImgEnabled: editable,
      cameraIcon: !editable
          ? null
          : Stack(
              alignment: Alignment.center,
              children: [
                cameraBar!,
                cameraIcon!,
              ],
            ),
      cameraIconTopMargin:
          !editable ? null : theme.layout.authAvatarSize - 2.5.w,
    );
  }

  static Widget buildAvatarHeader(ThemePlugin theme,
      {bool? isWelcome = false, bool? editable = false, Widget? title}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: theme.layout.authTitleBarHeight),
        isWelcome!
            ? buildWelcomeAvatar(theme)
            : buildAvatar(theme, editable: editable),
        SizedBox(height: theme.layout.authAvatarTitleMarginTop),
        title ??
            CommonText(
              'app.name'.t18,
              theme.textStyleExtension.authPageTitle,
            ),
      ],
    );
  }

  static Widget buildAvatarHeaderLine(ThemePlugin theme, BuildContext context) {
    return Container(
      width: screenSize.width,
      height: theme.layout.authAvatarHeaderLineHeight,
      color: theme.themeData.colorScheme.primary,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          buildAvatarHeader(theme),
          Positioned(
            top: theme.layout.authBackButtonIconPaddingTop,
            left: theme.layout.authBackButtonIconPaddingLeft,
            child: buildBackButton(theme, context),
          ),
        ],
      ),
    );
  }

  static Future<Widget?> buildPetAvatarWidget(ThemePlugin theme,
      {required String userId, required String petId, required String avatar, double? size}) async {

    String filePath  = LocalStorage.buildPetAvatarFilePath(userId: userId, petId: petId, fileName: avatar);
    Directory dirPath = LocalStorage.buildPetAvatarLocalDirectory(userId: userId, petId: petId, fileName: avatar);

    // local file
    File file = File(dirPath.path);

    if (await file.exists()) {
      logger.d('Try to load image from ${dirPath.path}');
      return CommonImageAsset(
        file: File(file.path),
        isCircle: true,
        width: size?? theme.layout.profilePetAvatarSize,
        height: size?? theme.layout.profilePetAvatarSize,
      );
    }
    // if avatar not exist at local, download avatar from network and save to local storage
    else {

      try{

        String url = await CloudStorage().getDownloadURL(filePath);

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          CloudStorage().downloadFile(filePath);
        });

        return CommonImageAsset(
          url: url,
          isCircle: true,
          width: size?? theme.layout.profilePetAvatarSize,
          height: size?? theme.layout.profilePetAvatarSize,
        );
      } catch (e) {
        logger.e('Image url not valid, use default avatar instead');
        return CommonImageAsset(
          path: ImageAssetConst.defaultPetAvatar,
          isCircle: true,
          width: size?? theme.layout.profilePetAvatarSize,
          height: size?? theme.layout.profilePetAvatarSize,
        );
      }
    }
  }

  static Future<Widget?> buildPetAvatar(ThemePlugin theme, {required String userId, required String petId, required String? avatar, double? size}) async {

    if (avatar != null) {
      return await buildPetAvatarWidget(
          theme, userId: userId, petId: petId, avatar: avatar, size: size);
    } else {
      return CommonImageAsset(
        path: ImageAssetConst.defaultPetAvatar,
        isCircle: true,
        width: size?? theme.layout.profilePetAvatarSize,
        height: size?? theme.layout.profilePetAvatarSize,
      );
    }
  }

  static Widget buildAvatar1(ThemePlugin theme, {Widget? avatar, bool? editable = false}) {

    // Background
    CommonBar bg = CommonBar(
      shape: BoxShape.circle,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha),
      radius: theme.layout.authAvatarSize,
      hasShadow: true,
      shadowColor: theme.colorExtension.authAvatarShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Avatar
    CommonImageAsset defaultAvatar = CommonImageAsset(
      path: ImageAssetConst.nataIconCir,
      isCircle: true,
      width: theme.layout.authAvatarSize,
      height: theme.layout.authAvatarSize,
    );

    // Camera bar
    CommonBar? cameraBar = !editable!
        ? null
        : CommonBar(
      color: theme.themeData.colorScheme.surface,
      width: 46.w,
      height: 46.w,
      circular: 14.w,
      hasShadow: true,
      shadowColor: theme.colorExtension.profileAvatarCameraShadow,
      shadowOffset: theme.layout.authAvatarShadowOffset,
      shadowBlurRadius: theme.layout.authAvatarShadowBlur,
    );

    // Camera icon
    CommonImageAsset? cameraIcon = !editable
        ? null
        : CommonImageAsset(
      path: ImageAssetConst.iconImage,
      width: 20.w,
      height: 20.w,
      color: theme.themeData.colorScheme.secondary,
    );

    return CommonAvatar(
      hasBg: true,
      bg: bg,
      hasContent: true,
      content: avatar?? defaultAvatar,
      changeImgEnabled: editable,
      cameraIcon: !editable ? null : Stack(
        alignment: Alignment.center,
        children: [
          cameraBar!,
          cameraIcon!,
        ],
      ),
      cameraIconTopMargin: !editable ? null : theme.layout.authAvatarSize + 90.w,
    );
  }

  static Widget buildWhiteSection(ThemePlugin theme) {
    return ListView(
      children: [
        SizedBox(
          //height: theme.layout.authAvatarHeaderLineHeight - 46.w,
          height: theme.layout.authAvatarHeaderLineHeight,
        ),
        CommonBar(
          width: screenSize.width,
          height: screenSize.height - theme.layout.authAvatarHeaderLineHeight,
          color: theme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(theme.layout.authWhiteSectionCircular),
          ),
          hasShadow: true,
          shadowColor: theme.colorExtension.authWhiteSectionShadow,
          shadowBlurRadius: theme.layout.authWhiteSectionShadowBlur,
        ),
      ],
    );
  }

  static Widget buildWhiteSection2(ThemePlugin theme) {
    return ListView(
      children: [
        SizedBox(
          //height: theme.layout.authAvatarHeaderLineHeight - 46.w,
          height: theme.layout.authAvatarHeaderLineHeight2,
        ),
        CommonBar(
          width: screenSize.width,
          height: screenSize.height - theme.layout.authAvatarHeaderLineHeight2,
          color: theme.themeData.colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(theme.layout.authWhiteSectionCircular),
          ),
          hasShadow: true,
          shadowColor: theme.colorExtension.authWhiteSectionShadow,
          shadowBlurRadius: theme.layout.authWhiteSectionShadowBlur,
        ),
      ],
    );
  }

  static Widget buildPositionBackButton(
      ThemePlugin theme, BuildContext context) {
    return Positioned(
      top: theme.layout.authBackButtonIconPaddingTop,
      left: theme.layout.authBackButtonIconPaddingLeft,
      child: buildBackButton(theme, context),
    );
  }

  static Widget buildAvatarSection(ThemePlugin theme) {
    return Column(
      children: [
        SizedBox(height: theme.layout.authAvatarPaddingTop),
        Center(child: buildAvatar(theme)),
      ],
    );
  }
  static Widget buildTopSection(ThemePlugin theme, BuildContext context) {
    return Column(
      children: [
        // 顶部栏：背景色、返回按钮、标题
        Container(
          width: screenSize.width,
          height: theme.layout.authAvatarHeaderLineHeight,
          color: theme.themeData.colorScheme.primary,
          child: Stack(
            alignment: Alignment.center,
            children: [
              buildAvatarHeader(
                theme,
                title: CommonText(
                  'select.pet'.t18,
                  theme.textStyleExtension.appAvatarMedium,
                ),
              ),
              Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: theme.layout.authBackButtonIconPaddingTop,
                    left: theme.layout.authBackButtonIconPaddingLeft,
                  ),
                  child: buildBackButton(theme, context),
                ),
              ),
            ],
          ),
        ),

        // 头像部分
        //SizedBox(height: theme.layout.authAvatarPaddingTop),
        //Center(child: buildAvatar(theme)),
      ],
    );
  }

  static Widget buildButton(
      ThemePlugin theme, CommonButtonController controller,
      {String? text, bool? isEnabled, AsyncVoidCallback? onPressed}) {
    return CommonButton2(
      isEnabled: isEnabled,
      controller: controller,
      width: theme.layout.authButtonWidth,
      height: theme.layout.authButtonHeight,
      color: theme.themeData.colorScheme.secondary,
      disabledColor: theme.themeData.colorScheme.tertiary,
      text: text,
      textStyle: theme.textStyleExtension.authPageButton,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton,
      circular: theme.layout.authButtonCircular,
      onPressed: onPressed,
    );
  }

  static Widget buildAlterButton(
      ThemePlugin theme, CommonButtonController controller,
      {String? text, AsyncVoidCallback? onPressed}) {
    return CommonButton2(
      controller: controller,
      width: theme.layout.authAlterButtonWidth,
      height: theme.layout.authAlterButtonHeight,
      color: Colors.white.withAlpha(0.0.colorAlpha),
      disabledColor: theme.themeData.colorScheme.tertiary,
      text: text,
      textStyle: theme.textStyleExtension.authPageAlterButton,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton,
      hasRadius: false,
      onPressed: onPressed,
    );
  }

  static Widget buildTextButton() {
    return Container();
  }

  static Widget buildText(
      ThemePlugin theme, double distance, String inputText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: distance.w),
        Padding(
          padding: EdgeInsets.only(left: 38.w),
          child: CommonText(
            inputText,
            theme.textStyleExtension.userProfileSettingBody,
          ),
        ),
      ],
    );
  }

  static Widget buildText2(
      ThemePlugin theme, double distance, String inputText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: distance.w),
        Padding(
          padding: EdgeInsets.only(left: 38.w),
          child: CommonText(
            inputText,
            theme.textStyleExtension.placeListItemDateTime,
            maxLines: 10,
          ),
        ),
      ],
    );
  }

  static Widget buildLabel(ThemePlugin theme, {Widget? title, Widget? desc}) {
    // Title bg
    // Widget titleBg = CommonBar(
    //   width: theme.layout.authLabelTitleWidth,
    //   height: theme.layout.authLabelDescHeight,
    //   opacity: 0,
    // );

    //
    // final BoxShape? shape;
    // final Color? color;
    // final double? opacity;
    //
    // final double? radius;
    // final double? width;
    // final double? height;
    // final double? circular;
    // final BorderRadius? borderRadius;
    // final EdgeInsetsGeometry? margin;
    //
    // final bool? hasBorder;
    // final Color? borderColor;
    // final double? borderWidth;
    // final double? borderOpacity;
    //
    // final bool? hasShadow;
    // final Color? shadowColor;
    // final Offset? shadowOffset;
    // final double? shadowBlurRadius;
    // final double? shadowSpreadRadius;

    // Title  text
    Widget titleText = title ??
        CommonText(
          'auth.label.signup'.t18,
          theme.textStyleExtension.authPageTitle,
        );

    // Desc bg
    // Widget descBg = CommonBar(
    //   width: theme.layout.authLabelTitleWidth,
    //   height: theme.layout.authLabelDescHeight,
    //   opacity: 0,
    // );

    // Desc  text
    Widget descText = desc ??
        CommonText(
          'auth.label.login.email'.t18,
          theme.textStyleExtension.authPageDesc,
        );

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            titleText,
          ],
        ),
        SizedBox(
          height: theme.layout.authLabelLineSpace,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
            descText,
          ],
        )
      ],
    );
    //
    //
    //
    //   Stack(
    //   alignment: Alignment.topLeft,
    //   children: [
    //     // Title
    //     Row(
    //       children: [
    //         SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
    //         Stack(
    //           alignment: Alignment.centerLeft,
    //           children: [
    //             titleBg,
    //             titleText,
    //           ],
    //         ),
    //       ],
    //     ),
    //     SizedBox(height: theme.layout.authLabelLineSpace,),
    //     // Desc
    //     Row(
    //       mainAxisAlignment: MainAxisAlignment.start,
    //       children: [
    //         SizedBox(width: theme.layout.authLabelTitlePaddingLeft),
    //         descText,
    //         // Column(
    //         //   children: [
    //         //     SizedBox(height: 33.w),
    //         //     Stack(
    //         //       alignment: Alignment.centerLeft,
    //         //       children: [
    //         //         // descBg,
    //         //         descText,
    //         //       ],
    //         //     ),
    //         //   ],
    //         // ),
    //       ],
    //     )
    //   ],
    // );
  }

  static Widget buildSelectPetLabel(ThemePlugin theme, BuildContext context) {
    return Container(
      width: screenSize.width,
      height: theme.layout.authAvatarHeaderLineHeight,
      color: theme.themeData.colorScheme.primary,
      child: Stack(
        alignment: Alignment.center,
        children: [
          buildAvatarHeader(
            theme,
            title: CommonText(
              'select.pet'.t18,
              theme.textStyleExtension.appAvatarMedium,
            ),
          ),
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: EdgeInsets.only(
                top: theme.layout.authBackButtonIconPaddingTop,
                left: theme.layout.authBackButtonIconPaddingLeft,
              ),
              child: buildBackButton(theme, context),
            ),
          ),
        ],
      ),
    );
  }


  static Widget buildPetSelectCard({
    required ThemePlugin theme,
    required String imagePath,
    required void Function() onTap,
    required String name,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 160.w,
            height: 160.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50.r),
              image: DecorationImage(
                image: AssetImage(imagePath),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(height: 13.w),
          Text(
            name,
            style: theme.textStyleExtension.appAvatarSelect,
          ),
        ],
      ),
    );
  }

  static Widget buildPetDetailWhiteSection1() {
    return const CommonWhiteSection(
      width: 403,
      height: 149,
      top: 0,
      borderRadius: 0,
    );
  }

  static Widget buildPetDetailWhiteSection2() {
    return const CommonWhiteSection(
      width: 403,
      height: 211,
      top: 697,
      borderRadius: 26,
    );
  }

  static Widget buildPetDetailTopSection({
    required ThemePlugin theme,
    required RxInt currentStep,
    double top = 60, // 距离屏幕顶部的间距
  }) {
    return Column(
      children: [
        SizedBox(height: top.w), // 控制顶部间距
        Center(
          child: Column(
            children: [
              CommonText(
                'add.pet'.t18,
                theme.textStyleExtension.appAvatarSelect2,
              ),
              SizedBox(height: 8.w),
              Obx(() {
                String stepText = '';
                switch (currentStep.value) {
                  case 1:
                    stepText = 'breed'.t18;
                    break;
                  case 2:
                    stepText = 'name'.t18;
                    break;
                  case 3:
                    stepText = 'weight'.t18;
                    break;
                  case 4:
                    stepText = 'birthday'.t18;
                    break;
                  case 5:
                    stepText = 'gender'.t18;
                    break;
                  case 6:
                    stepText = 'country'.t18;
                    break;
                  default:
                    stepText = '';
                }
                return CommonText(
                  stepText,
                  theme.textStyleExtension.userAvatarLarge,
                  textAlign: TextAlign.center,
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  static Widget buildPetStepIndicator({
    required ThemePlugin theme,
    required RxInt currentStep,
    double top = 65,
    double right = 38,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.only(right: right.w),
          child: Align(
            alignment: Alignment.topRight,
            child: Obx(() {
              String stepText = 'step'.t18;
              String currentStepText = '${currentStep.value}';
              String totalStepText = '/6';

              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    stepText,
                    style: theme.textStyleExtension.authPagePassTipBody,
                  ),
                  SizedBox(height: 5.w),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: currentStepText,
                          style: theme.textStyleExtension.buttonMedium2,
                        ),
                        TextSpan(
                          text: totalStepText,
                          style: theme.textStyleExtension.authPagePassTipBody,
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }),
          ),
        ),
      ],
    );
  }

  static Widget buildPetProgressIndicator({
    required ThemePlugin theme,
    required RxInt currentStep,
    double top = 120,
    double left = 38,
    double width = 327,
    double height = 4,
    int totalSteps = 6,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.only(left: left.w),
          child: Obx(() => Stack(
            children: [
              Container(
                width: width.w,
                height: height.w,
                color: theme.themeData.colorScheme.tertiary,
              ),
              Container(
                width: (width * (currentStep.value / totalSteps)).w,
                height: height.w,
                color: theme.themeData.colorScheme.secondary,
              ),
            ],
          )),
        ),
      ],
    );
  }

  static Widget buildGenderButton(ThemePlugin theme, RxString selectedGender,
      String gender, IconData icon, Function(String) onTap) {
    return Obx(() {
      bool isSelected = selectedGender.value == gender;
      return GestureDetector(
        onTap: () => onTap(gender),
        child: Container(
          width: 126.5.w,
          height: 50.w,
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: isSelected
                  ? theme.themeData.colorScheme.secondary
                  : theme.themeData.colorScheme.tertiary,
              width: 1,
            ),
            color: theme.themeData.colorScheme.onPrimary,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 16.w,
                height: 16.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? theme.themeData.colorScheme.secondary
                        : theme.themeData.colorScheme.tertiary,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Center(
                        child: Container(
                          width: 8.w,
                          height: 8.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: theme.themeData.colorScheme.secondary,
                          ),
                        ),
                      )
                    : null,
              ),
              SizedBox(width: 8.w),
              Text(
                gender,
                style: isSelected
                    ? theme.textStyleExtension.dropdownItemSelected
                    : theme.textStyleExtension.dropdownItem,
              ),
              SizedBox(width: 8.w),
              Icon(icon,
                  color: gender == PetGender.boy.t18key.t18 ? Colors.blue : Colors.pink,
                  size: 20.sp),
            ],
          ),
        ),
      );
    });
  }

  static Widget buildSubmitSection({
    required ThemePlugin theme,
    required RxBool isEnabled,
    required CommonButtonController submitButtonController,
    required CommonButtonController skipButtonController,
    required AsyncVoidCallback onContinuePressed,
    required AsyncVoidCallback onSkipPressed,
    required String submitButtonText,
    required String skipButtonText,
  }) {
    return Stack(
      children: [
        Positioned(
          top: 721.w,
          left: 38.w,
          child: Obx(() => ProfileWidgetBuilder.buildButton(
                theme,
                submitButtonController,
                text: submitButtonText,
                isEnabled: isEnabled.value,
                onPressed: isEnabled.value ? onContinuePressed : null,
              )),
        ),
        Positioned(
          top: 800.w,
          left: 104.w,
          child: ProfileWidgetBuilder.buildAlterButton(
            theme,
            skipButtonController,
            text: skipButtonText,
            onPressed: onSkipPressed,
          ),
        ),
      ],
    );
  }

  // static Widget buildUserSettingTopSection({
  //   required ThemePlugin theme,
  //   required VoidCallback onBack,
  //   required String topic,
  //   double top = 38,
  //   double left = 14,
  //   bool showAvatar = true,
  // }) {
  //   return Positioned(
  //     width: 375.w,
  //     height: 66.w,
  //     top: top.w,
  //     left: left.w,
  //     child: Container(
  //       padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 24.w),
  //       child: Stack(
  //         children: [
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               GestureDetector(
  //                 onTap: onBack,
  //                 child: Icon(Icons.arrow_back, size: 20.sp),
  //               ),
  //               SizedBox(width: 8.w),
  //               Container(
  //                 width: 1.w,
  //                 height: 22.w,
  //                 color: theme.themeData.colorScheme.tertiary,
  //               ),
  //               SizedBox(width: 8.w),
  //               CommonText(
  //                 topic,
  //                 theme.textStyleExtension.userProfileSettingBody,
  //               ),
  //               const Spacer(),
  //               if (showAvatar)
  //                 ClipOval(
  //                   child: Container(
  //                     width: 42.w,
  //                     height: 42.w,
  //                     child: Image.asset(
  //                       "assets/images/profile.png",
  //                       fit: BoxFit.cover,
  //                     ),
  //                   ),
  //                 ),
  //             ],
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  static Widget buildUserSettingTopSection({
    required ThemePlugin theme,
    required VoidCallback onBack,
    required String topic,
    bool showAvatar = true,
    Widget? avatar,
  }) {
    return Padding(
      padding: EdgeInsets.only(top: 38.w, left: 14.w, right: 14.w),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 24.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: onBack,
              child: Icon(Icons.arrow_back, size: 20.sp),
            ),
            SizedBox(width: 8.w),
            Container(
              width: 1.w,
              height: 22.w,
              color: theme.themeData.colorScheme.tertiary,
            ),
            SizedBox(width: 8.w),
            CommonText(
              topic,
              theme.textStyleExtension.userProfileSettingBody,
            ),
            const Spacer(),
             if (showAvatar)
               Container(
                 width: 42.w,
                 height: 42.w,
                 child: AuthWidgetBuilder.buildAvatar(
                   theme,
                   avatar: avatar,
                 ),
               ),
               // ClipOval(
               //   child: Container(
               //     width: 42.w,
               //     height: 42.w,
               //     child: Image.asset(
               //       "assets/images/profile.png",
               //       fit: BoxFit.cover,
               //     ),
               //   ),
               // ),
          ],
        ),
      ),
    );
  }

  static Widget buildUserSettingProfileSection({
    required ThemePlugin theme,
    required double top,
    required String title,
    IconData icon=Icons.person_outline,
    bool showLeftIcon = false,
    Color? leftIconColor,
    Color? rightIconColor,
    VoidCallback? onRightIconTap,
  }) {
    final Color resolvedLeftIconColor =
        leftIconColor ?? theme.themeData.colorScheme.primary;
    final Color resolvedRightIconColor =
        rightIconColor ?? theme.themeData.colorScheme.primary;

    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          child: Container(
            height: 46.w,
            decoration: BoxDecoration(
              color: theme.themeData.colorScheme.onPrimary,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                if (showLeftIcon) ...[
                  Container(
                    width: 46.w,
                    height: 46.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(
                        color: resolvedLeftIconColor,
                        width: 1.2.w,
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        icon,
                        color: resolvedLeftIconColor,
                        size: 24.sp,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                ],
                Expanded(
                  child: CommonText(
                    title,
                    theme.textStyleExtension.userProfileSettingBody,
                  ),
                ),
                GestureDetector(
                  onTap: onRightIconTap, // 🆕 点击事件
                  child: Icon(
                    Icons.chevron_right,
                    size: 24.sp,
                    color: resolvedRightIconColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }


  static Widget buildUserSettingField({
    required double top,
    required double left,
    required double width,
    required double height,
    required RxString hintText,
    required TextEditingController textEditingController,
    required CommonTextFieldController fieldController,
  }) {
    return Positioned(
      top: top.w,
      left: left.w,
      child: Obx(() => CommonTextField2(
            textEditingController: textEditingController,
            controller: fieldController,
            hintText: hintText.value,
            width: width.w,
            height: height.w,
            circular: 16.w,
          )),
    );
  }

  static Widget buildChangedNotification({
    required RxBool isVisible,
    required ThemePlugin theme,
    required double top,
  }) {
    return Obx(() => isVisible.value
        ? Column(
      children: [
        SizedBox(height: top.w),
        Center(
          child: Container(
            width: 170.w,
            height: 35.w,
            padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 16.w),
            decoration: BoxDecoration(
              color: theme.themeData.colorScheme.primary,
              borderRadius: BorderRadius.circular(51.86.w),
            ),
            child: Center(
              child: Text(
                "save.notification".t18,
                style: theme.textStyleExtension.userProfileSettingNotification,
              ),
            ),
          ),
        ),
      ],
    )
        : const SizedBox.shrink());
  }

  static Widget buildSettingInputBox(
      ThemePlugin theme, CommonTextField3Controller controller, double top) {
    logger.i('Object ID: ${identityHashCode(controller)}');

    return Column(
      children: [
        SizedBox(height: top.w),
        Center(
          child: CommonTextField3(
            controller: controller,
          ),
        ),
      ],
    );
  }

  static CommonTextField3Controller buildVerificationCodeTextFieldController(ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}){
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      isEnabled: false.obs,
      //keyboardType: TextInputType..obs,
      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildEmailDisplayTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      isEnabled: false.obs,
      //keyboardType: TextInputType.name.obs,
      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildEmailTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      //isEnabled: false.obs,
      keyboardType: TextInputType.emailAddress.obs,
      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildValidCodeTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      //keyboardType: TextInputType.number.obs,
      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildFirstNameTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
      Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.name.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildLastNameTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
      Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.name.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildDisplayNameTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.name.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildAddressTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.streetAddress.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildBreedTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.text.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildNameTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.name.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildWeightTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.number.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildBirthdayTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.datetime.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }

  static CommonTextField3Controller buildVaccineTextFieldController(
      ThemePlugin theme,
      Rx<TextEditingController>? textEditingController,
      RxString hintText,
      {Rx<AsyncCallbackWithContext?>? onCleared,
        Rx<AsyncCallbackWithContextAndResult?>? onChanged}) {
    return CommonTextField3Controller(
      textEditingController: textEditingController,
      keyboardType: TextInputType.datetime.obs,

      width: theme.layout.authTextFieldWidth.obs,
      height: theme.layout.authTextFieldHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.authTextFieldCircular.obs,
      iconColor: theme.themeData.colorScheme.tertiary.obs,
      borderWidth: theme.layout.authTextFieldBorderWidth.obs,
      borderColor: theme.themeData.colorScheme.tertiary.obs,
      focusBorderColor: theme.themeData.colorScheme.secondary.obs,
      mistakeBorderColor: theme.themeData.colorScheme.error.obs,
      disabledBorderColor: theme.themeData.colorScheme.tertiary.obs,

      hintText: hintText,
      hintStyle: theme.textStyleExtension.authPageHintText.obs,
      textStyle: theme.textStyleExtension.authPageInputText.obs,
      mistakeTextStyle: theme.textStyleExtension.authPageInvalidText.obs,
      disabledTextStyle: theme.textStyleExtension.authPageHintText.obs,
      textPaddingHorizontal: theme.layout.authTextFieldPaddingHorizontal.obs,
      textPaddingVertical: theme.layout.authTextFieldPaddingVertical.obs,

      //enableClearIcon: true.obs,
      //clearIconSize: theme.layout.authTextFieldClearIconSize.obs,
      onCleared: onCleared,
      onChanged: onChanged,
    );
  }


  static Widget buildDropdownInput({
    required ThemePlugin theme,
    required String label,
    required RxString selected,
    required List<String> items,
    required String hintText,
    required Function(String) onChanged,
    double top = 0,
  }) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 38.w),
          child: SizedBox(
            width: 327.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textStyleExtension.userProfileSettingBody,
                ),
                SizedBox(height: 24.w),
                Obx(() {
                  final bool isValidValue = items.contains(selected.value);
                  final reorderedItems = isValidValue
                      ? [selected.value, ...items.where((item) => item != selected.value)]
                      : items;
                  return Container(
                    height: 60.w,
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: isValidValue
                            ? theme.themeData.colorScheme.secondary
                            : theme.themeData.colorScheme.tertiary,
                        width: 1.w,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: isValidValue ? selected.value : null,
                        hint: Text(
                          hintText,
                          style: theme.textStyleExtension.authPageHintText,
                        ),
                        isExpanded: true,
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          color: isValidValue
                              ? theme.themeData.colorScheme.secondary
                              : theme.themeData.colorScheme.tertiary,
                        ),
                        style: isValidValue
                            ? theme.textStyleExtension.authPageInputText
                            : theme.textStyleExtension.authPageHintText,
                        items: reorderedItems.map((val) {
                          return DropdownMenuItem<String>(
                            value: val,
                            child: Text(
                              val,
                              style: theme.textStyleExtension.authPageInputText,
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            onChanged(newValue);
                          }
                        },
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }



  static Widget buildButton2(
      ThemePlugin theme,
      CommonButton3Controller controller,
      double top,
      ) {
    return Column(
      children: [
        SizedBox(height: top.w),
        Center(
          child: CommonButton3(
            controller: controller,
          ),
        ),
      ],
    );
  }

  static Widget buildCountryField({
    required ThemePlugin theme,
    required RxString selectedCountry,
    required RxBool isCountrySelected,
    required double top,
    required void Function(String? newValue) onChanged,
  }) {
    return Obx(
          () => Column(
        children: [
          SizedBox(height: top.w),
          Center(
            child: Container(
              width: 327.w,
              height: 60.w,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 15.w),
              decoration: BoxDecoration(
                color: theme.themeData.colorScheme.onPrimary,
                borderRadius: BorderRadius.circular(16.r),
                border: Border.all(
                  color: isCountrySelected.value
                      ? theme.themeData.colorScheme.secondary
                      : theme.themeData.colorScheme.tertiary,
                  width: 1.w,
                ),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: selectedCountry.value,
                  icon: Icon(
                    Icons.keyboard_arrow_down,
                    color: theme.themeData.colorScheme.tertiary,
                  ),
                  isExpanded: true,
                  onTap: () {
                    // 保持颜色
                  },
                  onChanged: onChanged,
                  items: ["Canada", "China", "U.S"]
                      .map<DropdownMenuItem<String>>((String value) {
                    final bool isSelected = isCountrySelected.value && selectedCountry.value == value;
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: isSelected
                            ? theme.textStyleExtension.inputLarge
                            : theme.textStyleExtension.inputLargeHint,
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildDatePicker({
    required ThemePlugin theme,
    required Rx<DateTime> selectedDate,
    VoidCallback? onChanged,   // ⭐️ 新加这行
  }) {
    final DateTime initialDate = DateTime.now();
    void _showCupertinoDatePicker() {
      showCupertinoModalPopup(
        context: Get.context!,
        builder: (BuildContext context) {
          return Container(
            height: 300.w,
            color: Colors.white,
            child: Column(
              children: [
                Container(
                  height: 50.w,
                  alignment: Alignment.center,
                  child: Text(
                    "Please select date",
                    style: theme.textStyleExtension.recordTimePicker,
                  ),
                ),
                Expanded(
                  child: CupertinoDatePicker(
                    mode: CupertinoDatePickerMode.date,
                    initialDateTime: selectedDate.value,
                    onDateTimeChanged: (DateTime newDate) {
                      selectedDate.value = newDate;
                      if (onChanged != null) {
                        onChanged();   // ⭐️ 新加这行，日期改变时触发回调
                      }
                    },
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: GestureDetector(
            onTap: _showCupertinoDatePicker,
            child: Obx(() {
              final bool isDateChanged = selectedDate.value.year != initialDate.year ||
                  selectedDate.value.month != initialDate.month ||
                  selectedDate.value.day != initialDate.day;
              return Container(
                width: 355.w,
                height: 50.w,
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                decoration: BoxDecoration(
                  color: theme.themeData.colorScheme.onPrimary,
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: theme.themeData.colorScheme.tertiary,
                    width: 1.w,
                  ),
                ),
                child: Text(
                  '${selectedDate.value.year}-${selectedDate.value.month.toString().padLeft(2, '0')}-${selectedDate.value.day.toString().padLeft(2, '0')}',
                  style: isDateChanged
                      ? theme.textStyleExtension.authPageInputText.copyWith(
                    color: theme.themeData.colorScheme.secondary,
                  )
                      : theme.textStyleExtension.authPageHintText,
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_feeding_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetFeedingRecord _$PetFeedingRecordFromJson(Map<String, dynamic> json) =>
    PetFeedingRecord(
      sid: json['sid'] as String,
      feedTime: (json['feedTime'] as num?)?.toInt(),
      foodTypes: (json['foodTypes'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      amount: json['amount'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      isSnack: json['isSnack'] as bool?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PetFeedingRecordToJson(PetFeedingRecord instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'feedTime': instance.feedTime,
      'foodTypes': instance.foodTypes,
      'amount': instance.amount,
      'images': instance.images,
      'isSnack': instance.isSnack,
      'notes': instance.notes,
    };

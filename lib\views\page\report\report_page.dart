import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';

// import '../../../common/enum/pet_gender.dart';
// import '../../../common/enum/pet_type.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import '../../../controller/weatherController.dart';
import '../../../models/pet_daycare_record.dart';
import '../../../models/pet_grooming_record.dart';
import '../../../models/pet_growing_record.dart';
import '../../../models/user_account.dart';
import '../../../models/pet.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../models/vo/pet_profile_vo.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../../component/progress/loading_widget.dart';
import '../../component/widgets/common_page.dart';
import '../../component/widgets/common_text.dart';
import '../../theme/colors/color_extension.dart';
import '../../theme/layouts/theme_layout.dart';
import '../../theme/text_schemes/text_style_extension.dart';
import '../../theme/theme_plugin.dart';
import '../../theme/theme_service.dart';
import '../page_interceptor.dart';

class ReportPage extends StatefulWidget {
  const ReportPage({super.key});

  @override
  ReportPageState createState() => ReportPageState();
}

class ReportPageState extends State<ReportPage> {
  late Future<ReportPageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => ReportPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ReportPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: DefaultTabController(
                length: 2,
                child: Column(
                  children: [
                    _buildTopSection(controller),
                    // ✅ 添加 TabBar
                    Container(
                      color: Colors.white,
                      child: TabBar(
                        dividerColor: Colors.transparent,
                        indicatorColor: controller.theme.themeData.colorScheme.primary,
                        labelColor: controller.theme.themeData.colorScheme.onSurface,
                        unselectedLabelColor: Colors.grey,
                        labelStyle: TextStyle(
                          fontFamily: 'Manrope',
                          fontWeight: FontWeight.w600,
                          fontSize: 14.sp,
                        ),
                        tabs: const [
                          Tab(text: "Health"),
                          Tab(text: "Device"),
                        ],
                      ),
                    ),
                    // ✅ 添加 Tab 内容视图
                    Expanded(
                      child: TabBarView(
                        children: [
                          // ✅ Health 内容
                          SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: Column(
                              children: [
                                _buildDailyBriefCard(controller),
                                _buildHealthSummaryCard(controller),
                                _buildRecentDaycareCard(controller),
                                _buildRecentGroomingCard(controller),
                              ],
                            ),
                          ),

                          // ✅ Device 内容
                          SingleChildScrollView(
                            physics: const BouncingScrollPhysics(),
                            child: _buildDeviceCardsView(), // << 关键在这里
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
        });
  }

  Widget _buildDeviceCardsView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
      child: Wrap(
        spacing: 24.w,
        runSpacing: 24.w,
        children: [
          _buildDeviceCard(
            gradient: null,
            batteryPercent: 77,
            mainValue: "65%",
            unit: "remaining",
            middleText: "300g/day",
            bottomText: "Miro's feeder",
            footerText: "Feed",
            isFeeder: true,
          ),
          _buildDeviceCard(
            gradient: const LinearGradient(
              colors: [Color(0xFFC1C1C1), Color(0xFFFFFFFF)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            batteryPercent: 27,
            mainValue: "5366",
            unit: "steps",
            middleText: "360 kcal",
            bottomText: "Miro's collar",
          ),
          _buildDeviceCard(
            gradient: const LinearGradient(
              colors: [Color(0xFFC1C1C1), Color(0xFFFFFFFF)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            batteryPercent: 87,
            mainValue: "1809",
            unit: "steps",
            middleText: "612 kcal",
            bottomText: "Lily's collar",
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard({
    required String mainValue,
    required String unit,
    required String middleText,
    required int batteryPercent,
    required String bottomText,
    Gradient? gradient,
    String? footerText,
    bool isFeeder = false,
  }) {
    final batteryColor = batteryPercent >= 50
        ? const Color(0xFF82C43C)
        : const Color(0xFFE20337);

    return Container(
      width: 165.w,
      height: 202.w,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: gradient == null ? Colors.white : null,
        gradient: gradient,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF162233).withOpacity(0.08),
            offset: const Offset(0, 8),
            blurRadius: 16,
            spreadRadius: -4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部：大数字 + 单位
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                mainValue,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w600,
                  fontSize: 24.sp,
                ),
              ),
              SizedBox(width: 4.w),
              Text(
                unit,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w600,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.w),

          // 中间文本
          Text(
            middleText,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontWeight: FontWeight.w500,
              fontSize: 12.sp,
            ),
          ),
          SizedBox(height: 4.w),

          // Battery
          Text(
            "Battery $batteryPercent%",
            style: TextStyle(
              fontFamily: 'Manrope',
              fontWeight: FontWeight.w500,
              fontSize: 10.sp,
              color: batteryColor,
            ),
          ),
          SizedBox(height: 4.w),

          // 底部描述文字
          Text(
            bottomText,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontWeight: FontWeight.w700,
              fontSize: 10.sp,
              color: const Color(0xFFC6C6C6),
            ),
          ),

          // Bottom 按钮 (仅 Feeder 卡片显示)
          if (isFeeder && footerText != null) ...[
            const Spacer(),
            Center(
              child: Container(
                width: 100.w,
                height: 35.w,
                decoration: BoxDecoration(
                  color: const Color(0xFFF2D3A4),
                  borderRadius: BorderRadius.circular(10),
                ),
                alignment: Alignment.center, // ✅ 居中对齐
                child: Text(
                  footerText,
                  style: TextStyle(
                    fontFamily: 'Manrope',
                    fontWeight: FontWeight.w600,
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }


  // Widget _buildDailyBriefCard(ReportPageController controller) {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           "Daily Brief",
  //           style: TextStyle(
  //             fontFamily: 'Manrope',
  //             fontWeight: FontWeight.w500,
  //             fontSize: 12.w,
  //             color: Colors.grey,
  //           ),
  //         ),
  //         SizedBox(height: 6.w),
  //         Stack(
  //           clipBehavior: Clip.none,
  //           children: [
  //             ClipRRect(
  //               borderRadius: const BorderRadius.only(
  //                 bottomRight: Radius.circular(60),
  //               ),
  //               child: Container(
  //                 width: 383.w,
  //                 height: 180.w,
  //                 decoration: const BoxDecoration(
  //                   gradient: RadialGradient(
  //                     center: Alignment.topCenter,
  //                     radius: 5,
  //                     colors: [Color(0xFFFFFFFF), Color(0xFFFFD9A8)],
  //                     stops: [0.0, 1.0],
  //                   ),
  //                 ),
  //                 child: Stack(
  //                   children: [
  //                     // 背景图不受 padding 影响
  //                     Positioned.fill(
  //                       child: Image.asset(
  //                         "assets/images/Daily brief.png",
  //                         fit: BoxFit.cover,
  //                         alignment: Alignment.centerRight,
  //                       ),
  //                     ),
  //
  //                     // 内容带 padding
  //                     Padding(
  //                       padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
  //                       child: Row(
  //                         crossAxisAlignment: CrossAxisAlignment.center,
  //                         children: [
  //                           Expanded(
  //                             flex: 1,
  //                             child: Column(
  //                               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  //                               crossAxisAlignment: CrossAxisAlignment.start,
  //                               children: [
  //                                 _buildBriefItem("Great", "Status", Icons.graphic_eq, Colors.green),
  //                                 _buildBriefItem("Happy", "Mood", Icons.favorite_outline, Colors.orange),
  //                                 _buildBriefItem("Low activity", "Focus", Icons.podcasts, Colors.redAccent),
  //                                 _buildBriefItem(
  //                                   "Play with ${controller.selectedPet.value} with his favourite ball",
  //                                   "Suggestion",
  //                                   Icons.lightbulb_outline,
  //                                   Colors.purple,
  //                                 ),
  //                               ],
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //
  //             // 日期标签
  //             Positioned(
  //               top: 4.w,
  //               right: -6.w,
  //               child: Container(
  //                 padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
  //                 decoration: BoxDecoration(
  //                   color: Colors.white,
  //                   borderRadius: BorderRadius.circular(12),
  //                   boxShadow: [
  //                     BoxShadow(
  //                       color: const Color(0x338B9EB8),
  //                       offset: const Offset(0, 1),
  //                       blurRadius: 4,
  //                       spreadRadius: 0,
  //                     ),
  //                   ],
  //                 ),
  //                 child: Text(
  //                   "May 8", // 可替换为动态日期
  //                   style: TextStyle(
  //                     fontFamily: 'Manrope',
  //                     fontWeight: FontWeight.w600,
  //                     fontSize: 12.w,
  //                     color: Colors.black,
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildDailyBriefCard(ReportPageController controller) {
    final pet = controller.petList!
        .firstWhere((p) => p.name == controller.selectedPet.value);
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: controller.generateTodayBrief(pet.sid!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox(); // 或者 LoadingWidget()
        }

        final items = snapshot.data ?? [];

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Daily Brief",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 12.w,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 6.w),
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(60),
                    ),
                    child: Container(
                      width: 383.w,
                      height: 180.w,
                      decoration: const BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment.topCenter,
                          radius: 5,
                          colors: [Color(0xFFFFFFFF), Color(0xFFFFD9A8)],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child: Stack(
                        children: [
                          // 背景图
                          Positioned.fill(
                            child: Image.asset(
                              "assets/images/Daily brief.png",
                              fit: BoxFit.cover,
                              alignment: Alignment.centerRight,
                            ),
                          ),
                          // 正文内容
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: items.map((item) {
                                      return _buildBriefItem(
                                        item["label"] ?? "",
                                        item["type"] ?? "",
                                        item["icon"] ?? Icons.info,
                                        _getColorByType(item["type"] ?? ""),
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 日期标签
                  Positioned(
                    top: 4.w,
                    right: -6.w,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x338B9EB8),
                            offset: const Offset(0, 1),
                            blurRadius: 4,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Text(
                        DateFormat("MMM d").format(DateTime.now()), // 动态日期
                        style: TextStyle(
                          fontFamily: 'Manrope',
                          fontWeight: FontWeight.w600,
                          fontSize: 12.w,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBriefItem(String title, String subtitle, IconData icon, Color iconColor) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: iconColor, size: 18),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 14.w,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 8.w,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHealthSummaryCard(ReportPageController controller) {
    final pet = controller.petList!
        .firstWhere((p) => p.name == controller.selectedPet.value);

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: controller.generateHealthSummary(pet.sid!),
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.length < 5) {
          return const SizedBox();
        }

        final items = snapshot.data!;
        final now = DateTime.now();
        final weekAgo = now.subtract(const Duration(days: 7));
        final dateLabel =
            "${DateFormat('MMM d').format(weekAgo)} - ${DateFormat('MMM d').format(now)}";

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Health Summary",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 12.w,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 6.w),
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(60),
                    ),
                    child: Container(
                      width: 383.w,
                      height: 217.w,
                      decoration: const BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment.topCenter,
                          radius: 5,
                          colors: [Color(0xFFFFFFFF), Color(0xFFBABAFF)],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: Image.asset(
                              "assets/images/Health summary.png",
                              fit: BoxFit.cover,
                              alignment: Alignment.centerRight,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      _buildBriefItem(items[0]["label"], "Walking", Icons.directions_walk, Colors.green),
                                      _buildBriefItem(items[1]["label"], "Sleep", Icons.nights_stay_outlined, Colors.blue),
                                      _buildBriefItem(items[2]["label"], "Feed", Icons.rice_bowl, Colors.brown),
                                      _buildBriefItem(items[3]["label"], "Poop", Icons.water_drop, Colors.deepOrange),
                                      _buildBriefItem(items[4]["label"], "Vaccine", Icons.vaccines, Colors.amber),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 4.w,
                    right: -6.w,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0x338B9EB8),
                            offset: const Offset(0, 1),
                            blurRadius: 4,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Text(
                        dateLabel,
                        style: TextStyle(
                          fontFamily: 'Manrope',
                          fontWeight: FontWeight.w600,
                          fontSize: 12.w,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

// 🟢 第三卡片：Recent Daycare
  Widget _buildRecentDaycareCard(ReportPageController controller) {
    final pet = controller.petList!
        .firstWhere((p) => p.name == controller.selectedPet.value);
    final petId = pet.sid!;

    return FutureBuilder<List<PetDaycareRecord>>(
      future: controller._petService.getPetDaycareRecords(petId),
      builder: (context, snapshot) {
        final records = snapshot.data ?? [];
        PetDaycareRecord? recent = records.isNotEmpty ? (
            records..sort((a, b) => (b.startTime ?? 0).compareTo(a.startTime ?? 0))
        ).first : null;

        final location = recent?.location ?? "Unknown Location";
        final start = recent?.startTime;
        final end = recent?.endTime;

        String formattedDate = "";
        String timeRange = "";

        if (start != null && end != null) {
          final startDate = DateTime.fromMillisecondsSinceEpoch(start);
          final endDate = DateTime.fromMillisecondsSinceEpoch(end);
          formattedDate = DateFormat("MMM d, yyyy").format(startDate);
          timeRange = "${DateFormat("h:mm a").format(startDate)} - ${DateFormat("h:mm a").format(endDate)}";
        }

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Recent Daycare",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 12.w,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 6.w),
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(60),
                    ),
                    child: Container(
                      width: 383.w,
                      height: 135.w,
                      decoration: const BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment.topCenter,
                          radius: 5,
                          colors: [Color(0xFFFFFFFF), Color(0xFF53E1B7)],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: Image.asset(
                              "assets/images/Recent daycare.png",
                              fit: BoxFit.cover,
                              alignment: Alignment.centerRight,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(Icons.home_outlined, color: Color(0xFF00C9A7), size: 20),
                                    SizedBox(width: 6.w),
                                    Expanded(
                                      child: Text(
                                        "Last stay @ $location",
                                        style: TextStyle(
                                          fontFamily: 'Manrope',
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14.w,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8.w),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(width: 20),
                                    SizedBox(width: 6.w),
                                    Text(
                                      formattedDate,
                                      style: TextStyle(
                                        fontFamily: 'Manrope',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.w,
                                        color: Colors.black,
                                      ),
                                    ),
                                    SizedBox(width: 16.w),
                                    Text(
                                      timeRange,
                                      style: TextStyle(
                                        fontFamily: 'Manrope',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.w,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

// 🔵 第四卡片：Recent Grooming
  Widget _buildRecentGroomingCard(ReportPageController controller) {
    final pet = controller.petList!
        .firstWhere((p) => p.name == controller.selectedPet.value);
    final petId = pet.sid!;

    return FutureBuilder<List<PetGroomingRecord>>(
      future: controller._petService.getPetGroomingRecords(petId),
      builder: (context, snapshot) {
        final records = snapshot.data ?? [];
        PetGroomingRecord? recent = records.isNotEmpty ? (
            records..sort((a, b) => (b.time ?? 0).compareTo(a.time ?? 0))
        ).first : null;

        final location = recent?.location ?? "Unknown Location";
        final start = recent?.time;
        String formattedDate = "";
        String formattedTime = "";

        if (start != null) {
          final dateTime = DateTime.fromMillisecondsSinceEpoch(start);
          formattedDate = DateFormat("MMM d, yyyy").format(dateTime);
          formattedTime = DateFormat("h:mm a").format(dateTime);
        }

        final content = "Grooming Service";

        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Recent Grooming",
                style: TextStyle(
                  fontFamily: 'Manrope',
                  fontWeight: FontWeight.w500,
                  fontSize: 12.w,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 6.w),
              Stack(
                clipBehavior: Clip.none,
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      bottomRight: Radius.circular(60),
                    ),
                    child: Container(
                      width: 383.w,
                      height: 135.w,
                      decoration: const BoxDecoration(
                        gradient: RadialGradient(
                          center: Alignment.topCenter,
                          radius: 5,
                          colors: [Color(0xFFFFFFFF), Color(0xFF726F7D)],
                          stops: [0.0, 1.0],
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: Image.asset(
                              "assets/images/Recent grooming.png",
                              fit: BoxFit.cover,
                              alignment: Alignment.centerRight,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 16.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(Icons.cut, color: Colors.purple, size: 20),
                                    SizedBox(width: 6.w),
                                    Expanded(
                                      child: Text(
                                        "$content @ $location",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontFamily: 'Manrope',
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14.w,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 8.w),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(width: 20),
                                    SizedBox(width: 6.w),
                                    Text(
                                      formattedDate,
                                      style: TextStyle(
                                        fontFamily: 'Manrope',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.w,
                                        color: Colors.black,
                                      ),
                                    ),
                                    SizedBox(width: 16.w),
                                    Text(
                                      formattedTime,
                                      style: TextStyle(
                                        fontFamily: 'Manrope',
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.w,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }



  Widget _buildTopSection(ReportPageController controller) {
    return Column(children: [
      Container(
        height: 100.w,
        padding: EdgeInsets.symmetric(horizontal: 38.w, vertical: 12.w),
        child: Row(
            children: [
              IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Icon(Icons.arrow_back,
                    color: controller.theme.themeData.colorScheme.onSurface),
              ),
              SizedBox(width: 8.w),
              Container(
                width: 1.w,
                height: 22.w,
                color: controller.theme.themeData.colorScheme.tertiary,
              ),
              SizedBox(width: 8.w),
              CommonText(
                "Pet Profile",
                controller.theme.textStyleExtension.userProfileSettingBody,
              ),
              const Spacer(),
              Obx(() => Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: controller.selectedPet.value,
                    icon: Icon(Icons.keyboard_arrow_down,
                        color: controller
                            .theme.themeData.colorScheme.tertiary),
                    dropdownColor: Colors.white,
                    items: controller.pets.map((pet) {
                      return DropdownMenuItem<String>(
                        value: pet.name,
                        child: Row(
                          children: [
                            Container(
                              width: 20.w,
                              height: 20.w,
                              child: buildPetAvatarWidget(
                                controller.theme,
                                controller.userAccount.value!.sid!,
                                controller.petList!
                                    .firstWhere(
                                      (p) =>
                                  p.name ==
                                      controller.selectedPet.value,
                                  orElse: () =>
                                  controller.petList!.first,
                                )
                                    .sid!,
                                controller.petList!
                                    .firstWhere(
                                      (p) =>
                                  p.name ==
                                      controller.selectedPet.value,
                                  orElse: () =>
                                  controller.petList!.first,
                                )
                                    .avatar,
                                128.w,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Container(
                              width: 40.w,
                              child: CommonText(
                                pet.name,
                                controller.theme.textStyleExtension
                                    .placeListItemMileage,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        controller.selectedPet.value = newValue;
                        controller.currentPetData.value = controller.pets
                            .firstWhere((pet) => pet.name == newValue);
                      }
                    },
                  ),
                ),
              )),
            ],
          ),
      ),
    ]);
  }

  Widget buildPetAvatarWidget(
      ThemePlugin theme,
      String userId,
      String petId,
      String? avatar,
      double size,
      ) {
    return FutureBuilder<Widget?>(
      future: ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: userId,
        petId: petId,
        avatar: avatar,
        size: size,
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(width: size.w, height: size.w);
        } else if (snapshot.hasError || snapshot.data == null) {
          return CircleAvatar(
            radius: size / 2.w,
            backgroundImage: AssetImage("assets/images/default_pet.png"),
          );
        } else {
          return AuthWidgetBuilder.buildAvatar(theme, avatar: snapshot.data);
        }
      },
    );
  }
  Color _getColorByType(String type) {
    switch (type) {
      case "Status":
        return Colors.green;
      case "Mood":
        return Colors.orange;
      case "Focus":
        return Colors.redAccent;
      case "Suggestion":
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

}

class ReportPageController extends GetxController {
  late ThemePlugin theme;
  final AuthService _authService = AuthService.instance;
  var userAccount = Rx<UserAccount?>(null);
  final PetService _petService = PetService();
  List<Pet>? petList;
  UserData? userData;

  final Rxn<PetDaycareRecord> recentDaycare = Rxn<PetDaycareRecord>();
  final Rxn<PetGroomingRecord> recentGrooming = Rxn<PetGroomingRecord>();
  final Map<String, Future<Widget?>> petAvatarFutures = {};

  late RxString selectedPet = 'Unknown'.obs;

  late List<PetProfileVO> pets;

  final Rx<PetProfileVO> currentPetData = PetProfileVO.empty().obs;

  Future<ReportPageController> init() async {
    await PageInterceptor.pageAuthCheck();
    await Get.putAsync(() => WeatherNotificationController().init());
    final Pet? passedPet = (Get.arguments as Map<String, dynamic>?)?['selectedPet'];

    theme = await Get.putAsync(() => ThemePlugin().init());
    userAccount.value = _authService.userAccount.value;
    userData = _authService.userData.value;

    petList = await _petService.getOwnedPets(userAccount.value!.sid!);
    pets = [];

    for (var pet in petList ?? []) {
      PetGrowingRecord? record =
      await _petService.getLatestPetGrowingRecordById(pet.sid!);
      double? weight = record?.weight;
      pets.add(PetProfileVO(
        name: pet.name ?? "Unnamed",
        image: pet.avatar ?? "assets/images/default_pet.png",
        type: pet.type == PetType.cat
            ? PetType.cat.t18key.t18
            : pet.type == PetType.dog
            ? PetType.dog.t18key.t18
            : PetType.others.t18key.t18,
        breed: pet.breed ?? "unknown",
        gender: pet.gender == PetGender.boy
            ? PetGender.boy.t18key.t18
            : pet.gender == PetGender.girl
            ? PetGender.girl.t18key.t18
            : "unknown",
        weight: weight != null ? "${weight.toStringAsFixed(2)} kg" : "unknown",
        birthday: pet.birthday != null
            ? DateFormat("d MMMM yyyy")
            .format(DateTime.fromMillisecondsSinceEpoch(pet.birthday!))
            : "unknown",
        home: '',
        nextVaccine: "N/A",
      ));

      petAvatarFutures[pet.sid!] = ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: userAccount.value!.sid!,
        petId: pet.sid!,
        avatar: pet.avatar,
        size: 128.w,
      );
    }

    if (passedPet != null) {
      final found = pets.firstWhere(
            (p) => p.name == passedPet.name,
        orElse: () => pets.first,
      );
      currentPetData.value = found;
      selectedPet.value = found.name;
    } else {
      if (pets.isNotEmpty) {
        currentPetData.value = pets.first;
        selectedPet.value = currentPetData.value.name;
      }
    }
    return this;
  }

  Future<List<Map<String, dynamic>>> generateTodayBrief(String petId) async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    final walks = await _petService.getTodayPetWalkingRecords(petId);

    final todayWalks = walks.where((r) {
      final t = DateTime.fromMillisecondsSinceEpoch(r.startTime ?? 0);
      return t.isAfter(todayStart) && t.isBefore(todayEnd);
    }).toList();

    final activity = todayWalks.isEmpty ? "No activity" : "Active";

    return [
      {
        "icon": Icons.favorite,
        "label": "Great",
        "type": "Status",
      },
      {
        "icon": Icons.emoji_emotions,
        "label": "Happy",
        "type": "Mood",
      },
      {
        "icon": Icons.sports_mma,
        "label": activity,
        "type": "Focus",
      },
      {
        "icon": Icons.lightbulb_outline,
        "label": "Play with ${selectedPet.value} with his favourite ball",
        "type": "Suggestion",
      },
    ];
  }

  Future<List<Map<String, dynamic>>> generateHealthSummary(String petId) async {
    final now = DateTime.now();
    final weekAgo = now.subtract(const Duration(days: 7));

    final walks = await _petService.getPetWalkingRecords(petId);
    final recentWalks = walks.where((r) {
      final t = DateTime.fromMillisecondsSinceEpoch(r.startTime ?? 0);
      return t.isAfter(weekAgo) && t.isBefore(now);
    }).toList();
    final avgSteps = (recentWalks.length / 7).toStringAsFixed(1);

    final feedings = await _petService.getPetFeedingRecords(petId);
    final recentFeeds = feedings.where((r) {
      final t = DateTime.fromMillisecondsSinceEpoch(r.feedTime ?? 0);
      return t.isAfter(weekAgo) && t.isBefore(now);
    }).toList();
    final feedingSummary = "Normal intake for ${recentFeeds.length} days";

    final poops = await _petService.getPetPoopRecords(petId);
    final recentPoops = poops.where((r) {
      final t = DateTime.fromMillisecondsSinceEpoch(r.poopTime ?? 0);
      return t.isAfter(weekAgo) && t.isBefore(now);
    }).toList();
    final lastPoop = recentPoops.isNotEmpty ? recentPoops.last.colorTags ?? "Unknown" : "None";

    final vaccines = await _petService.getPetVaccineRecords(petId);
    vaccines.sort((a, b) => (b.time ?? 0).compareTo(a.time ?? 0));
    final lastVaccineDays = vaccines.isNotEmpty
        ? ((now.millisecondsSinceEpoch - (vaccines.first.time ?? 0)) / (1000 * 60 * 60 * 24)).round()
        : -1;

    return [
      {
        "icon": Icons.directions_walk,
        "label": "$avgSteps steps / day",
      },
      {
        "icon": Icons.bedtime,
        "label": "8.2h / day",
      },
      {
        "icon": Icons.restaurant,
        "label": feedingSummary,
      },
      {
        "icon": Icons.pets,
        "label": "$lastPoop poop",
      },
      {
        "icon": Icons.event,
        "label": lastVaccineDays >= 0 ? "In $lastVaccineDays days" : "No record",
      },
    ];
  }
}


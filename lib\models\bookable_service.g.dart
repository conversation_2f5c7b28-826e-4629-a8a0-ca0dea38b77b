// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bookable_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BookableService _$BookableServiceFromJson(Map<String, dynamic> json) =>
    BookableService(
      storeId: json['storeId'] as String,
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      serviceCategory:
          $enumDecode(_$ServiceCategoryEnumMap, json['serviceCategory']),
      serviceBreed: $enumDecode(_$ServiceBreedEnumMap, json['serviceBreed']),
      description: json['description'] as String?,
      status: $enumDecode(_$StoreServiceStatusEnumMap, json['status']),
      staffIds:
          (json['staffIds'] as List<dynamic>).map((e) => e as String).toList(),
      minDuration: (json['minDuration'] as num).toInt(),
      maxDuration: (json['maxDuration'] as num).toInt(),
      defaultDuration: (json['defaultDuration'] as num).toInt(),
      basePrice: (json['basePrice'] as num).toDouble(),
      currency: $enumDecode(_$CurrencyEnumMap, json['currency']),
      maxCapacityPerSlot: (json['maxCapacityPerSlot'] as num).toInt(),
      isOnlineBookingEnabled:
          JsonUtil.boolFromJson(json['isOnlineBookingEnabled']),
      requiresApproval: JsonUtil.boolFromJson(json['requiresApproval']),
      cancellationPolicy: json['cancellationPolicy'] as String?,
      servicePhotos: (json['servicePhotos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      totalBookings: (json['totalBookings'] as num).toInt(),
      completedBookings: (json['completedBookings'] as num).toInt(),
      id: (json['id'] as num?)?.toInt(),
      sid: json['sid'] as String?,
      name: json['name'] as String?,
      isValid: JsonUtil.boolFromJson(json['isValid']),
      isSynced: JsonUtil.boolFromJson(json['isSynced']),
      createdBy: json['createdBy'] as String?,
      createDate: (json['createDate'] as num?)?.toInt(),
      updatedBy: json['updatedBy'] as String?,
      updateDate: (json['updateDate'] as num?)?.toInt(),
      reviewedBy: json['reviewedBy'] as String?,
      reviewDate: (json['reviewDate'] as num?)?.toInt(),
      approveStatus: JsonUtil.boolFromJson(json['approveStatus']),
      notes: json['notes'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$BookableServiceToJson(BookableService instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sid': instance.sid,
      'name': instance.name,
      'isValid': JsonUtil.boolToJson(instance.isValid),
      'isSynced': JsonUtil.boolToJson(instance.isSynced),
      'createdBy': instance.createdBy,
      'createDate': instance.createDate,
      'updatedBy': instance.updatedBy,
      'updateDate': instance.updateDate,
      'reviewedBy': instance.reviewedBy,
      'reviewDate': instance.reviewDate,
      'approveStatus': JsonUtil.boolToJson(instance.approveStatus),
      'notes': instance.notes,
      'tags': instance.tags,
      'storeId': instance.storeId,
      'serviceId': instance.serviceId,
      'serviceName': instance.serviceName,
      'serviceCategory': _$ServiceCategoryEnumMap[instance.serviceCategory]!,
      'serviceBreed': _$ServiceBreedEnumMap[instance.serviceBreed]!,
      'description': instance.description,
      'status': _$StoreServiceStatusEnumMap[instance.status]!,
      'staffIds': instance.staffIds,
      'minDuration': instance.minDuration,
      'maxDuration': instance.maxDuration,
      'defaultDuration': instance.defaultDuration,
      'basePrice': instance.basePrice,
      'currency': _$CurrencyEnumMap[instance.currency]!,
      'maxCapacityPerSlot': instance.maxCapacityPerSlot,
      'isOnlineBookingEnabled':
          JsonUtil.boolToJson(instance.isOnlineBookingEnabled),
      'requiresApproval': JsonUtil.boolToJson(instance.requiresApproval),
      'cancellationPolicy': instance.cancellationPolicy,
      'servicePhotos': instance.servicePhotos,
      'totalBookings': instance.totalBookings,
      'completedBookings': instance.completedBookings,
    };

const _$ServiceCategoryEnumMap = {
  ServiceCategory.grooming: 'GROOMING',
  ServiceCategory.veterinary: 'VETERINARY',
  ServiceCategory.daycare: 'DAYCARE',
  ServiceCategory.boarding: 'BOARDING',
  ServiceCategory.training: 'TRAINING',
  ServiceCategory.other: 'OTHER',
};

const _$ServiceBreedEnumMap = {
  ServiceBreed.dog: 'DOG',
  ServiceBreed.cat: 'CAT',
  ServiceBreed.other: 'OTHER',
};

const _$StoreServiceStatusEnumMap = {
  StoreServiceStatus.active: 'ACTIVE',
  StoreServiceStatus.inactive: 'INACTIVE',
  StoreServiceStatus.suspended: 'SUSPENDED',
};

const _$CurrencyEnumMap = {
  Currency.cad: 'CAD',
  Currency.usd: 'USD',
  Currency.eur: 'EUR',
};

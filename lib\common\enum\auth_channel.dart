import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum AuthChannel {

  emailPassword('CRED', 'auth.channel.email.password'),
  sms('SMS', 'auth.channel.sms'),
  ;

  final String code; // Stored code in database
  final String t18key; // Translation key
  const AuthChannel(this.code, this.t18key);

  factory AuthChannel.fromCode(String code) {
    return AuthChannel.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

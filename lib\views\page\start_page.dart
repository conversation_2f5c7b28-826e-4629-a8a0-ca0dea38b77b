import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onenata_app/views/page/appointment/appointment_page.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/page/record/record_page.dart';
import 'package:onenata_app/views/page/reward/rewards_page.dart';
import 'package:onenata_app/views/page/store_detail_page.dart';

import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/theme/theme_service.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';
import 'package:onenata_app/views/page/page_i.dart';
import 'package:onenata_app/views/page/page_interceptor.dart';
import 'package:onenata_app/views/component/widgets/common_page.dart';

class StartPage extends StatefulWidget {
  const StartPage({super.key});

  @override
  StartPageState createState() => StartPageState();
}

class StartPageState extends State<StartPage> {

  // Declare your controller here
  late Future<StartPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => StartPageController().init());
  }

  @override
  Widget build(BuildContext context) {

    return FutureBuilder<StartPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              body: Center (
                child: LoadingWidget(),
              ),
            );
          }
        });
  }
}

class StartPageController extends GetxController {

  // Theme plugin
  late ThemePlugin theme;

  // Variables
  final RxInt counter = 0.obs;

  // Async initialization
  Future<StartPageController> init() async {

    // Initialize theme service for global
    await Get.putAsync(() => ThemeService().init());

    // Initialize theme plugin
    theme = await Get.putAsync(() => ThemePlugin().init());

    // Validate auth state
    //await PageInterceptor.rootAuthCheck(page: RootPage());
    //await PageInterceptor.rootAuthCheck(page: UserProfilePage());
    await PageInterceptor.rootAuthCheck(page: AppointmentPage());
    return this;
  }

  void incrementCounter() {
    counter.value += 1;
  }
}

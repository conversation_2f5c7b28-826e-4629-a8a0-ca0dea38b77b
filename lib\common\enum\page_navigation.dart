/// The code should follow the bottom navigation bar index
enum PageNavigation {

  wellness(0, 'WLN', 'app.navigation.wellness'),
  shop(1, 'SHP', 'app.navigation.shop'),
  home(2, 'HME', 'app.navigation.home'),
  community(3, 'COM', 'app.navigation.community'),
  profile(4, 'PRF', 'app.navigation.profile'),
  ;

  final int key; // Order of the tab controller
  final String code; // Stored code in database
  final String t18key; // i18n code
  const PageNavigation(this.key, this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory PageNavigation.fromKey(int key) {
    return PageNavigation.values.firstWhere((element) => element.key == key, orElse: () => throw ArgumentError('Invalid key: $key'));
  }
  factory PageNavigation.fromCode(String code) {
    return PageNavigation.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

extension PageNavigationExtension on PageNavigation {
  String get assetPath => 'icon_navi_$<EMAIL>';
}

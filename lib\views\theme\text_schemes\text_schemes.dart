enum TextSchemes {
  //
  // // Built-in text schemes
  // headlineLarge, // headline large
  // headlineMedium, // headline medium
  // headlineSmall, // headline small
  // titleLarge, // title large
  // titleMedium, // title medium
  // titleSmall, // title small
  // bodyLarge, // body large
  // bodyMedium, // body medium
  // bodySmall, // body small
  // labelLarge, // label large
  // labelMedium, // label medium
  // labelSmall, // label small
  //
  // // Custom text schemes
  // headlineLargeBold, // headline large, bold
  // headlineLargeLight, // headline large, light
  // headlineMediumBold, // headline medium, bold
  // headlineMediumLight, // headline medium, light
  // headlineSmallBold, // headline small, bold
  // headlineSmallLight, // headline small, light
  // titleLargeBold, // title large, bold
  // titleLargeLight, // title large, light
  // titleMediumBold, // title medium, bold
  // titleMediumLight, // title medium, light
  // titleSmallBold, // title small, bold
  // titleSmallLight, // title small, light
  // bodyLargeBold, // body large, bold
  // bodyLargeLight, // body large, light
  // bodyMediumBold, // body medium, bold
  // bodyMediumLight, // body medium, light
  // bodySmallBold, // body small, bold
  // bodySmallLight, // body small, light
  // labelLargeBold, // label large, bold
  // labelLargeLight, // label large, light
  // labelMediumBold, // label medium, bold
  // labelMediumLight, // label medium, light
  // labelSmallBold, // label small, bold
  // labelSmallLight, // label small, light

  // Common widgets
  buttonLargeFilled, // button in login page, filled
  buttonLargeOutlined, // button in login page, outlined
  buttonMedium, // button in function page
  buttonMedium2, // button of other choices 2
  inputLarge, // input box in login page, filled
  inputLargeHint, // input box in login page, hint
  inputLargeError, // input box in login page, error
  inputMedium, // input box in function page, empty
  inputMediumHint, // input box in function page, hint
  dropdownItem, // dropdown item in function page, unselected
  dropdownItemSelected, // dropdown item in function page, selected
  radioItem, // radio item in function page, unselected
  radioItemSelected, // radio item in function page, selected
  bottomItem, // bottom item in function page, unselected
  bottomItemSelected, // bottom item in function page, selected

  // Auth page
  appAvatarLarge, // app avatar in function page, large
  appAvatarLarge2, // app avatar in function page, large2
  appAvatarMedium, // app avatar in function page, medium
  authPageTitle, // label in auth page, title
  authPageDesc, // label in auth page, desc
  authPagePassTipTitle, // label in auth page, pass tip
  authPagePassTipBody, // label in auth page, pass tip2
  authPageResend, // label in auth page, resend
  authPageInvalid, // label in auth page, invalid

  // Function page
  userAvatar, // user avatar in function page
  petAvatar, // pet avatar in function page
  petAvatarMore, // add pet avatar in function page
  petAvatarLargeBold, // pet avatar in function page, large, bold
  petAvatarLargeLight, // pet avatar in function page, large, light
  petInfoTitle, // pet info title in function page
  petInfoBody, // pet info body in function page
  petInfoListTitle, // pet info list title in function page
  petInfoListBody, // pet info list body in function page
  contactTitle, // contact title in function page
  contactInfo, // contact info in function page
  postGridItemMedium, // post grid label item in function page, medium
  postGridItemSmall, // post grid label item in function page, small
  postStatisticTitle, // post statistic title in function page
  postStatisticBody, // post statistic body in function page
  eventListItemMedium, // event list item in function page, medium
  eventListItemSmall, // event list item in function page, small
  placeListItemMedium, // event list item in function page, medium
  placeListItemSmall, // event list item in function page, small
  placeListItemSmall2, // event list item in function page, small2
  ;
}

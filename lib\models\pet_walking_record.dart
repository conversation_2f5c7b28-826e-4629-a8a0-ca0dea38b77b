import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_walking_record.g.dart';

@JsonSerializable()
class PetWalkingRecord{
  String sid;
  int? startTime;
  int? endTime;
  List<String>? distanceTags;
  String? location;
  List<String>? images;
  String? notes;

  PetWalkingRecord({
    required this.sid,
    this.startTime,
    this.endTime,
    this.distanceTags,
    this.location,
    this.images,
    this.notes,
  });

  factory PetWalkingRecord.fromJson(Map<String, dynamic> json) =>
      _$PetWalkingRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetWalkingRecordToJson(this);

  static create({
    int? startTime,
    int? endTime,
    List<String>? distanceTags,
    String? location,
    List<String>? images,
    String? notes,
  }) {
    return PetWalkingRecord(
      sid: uuid.v4(),
      startTime: startTime,
      endTime: endTime,
      distanceTags: distanceTags,
      location: location,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetWalkingRecord other) {
    return PetWalkingRecord(
      sid: other.sid,
      notes: other.notes,
      startTime: other.startTime,
      endTime: other.endTime,
      distanceTags: other.distanceTags,
      location: other.location,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

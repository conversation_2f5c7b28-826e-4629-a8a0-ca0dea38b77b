import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'base_full_model.dart';

part 'pet_vet_record.g.dart';

@JsonSerializable()
class PetVetRecord{
  String sid;
  int? time;
  String? location;
  String? description;
  String? treat;
  List<String>? images;
  String? notes;

  PetVetRecord({
    required this.sid,
    this.time,
    this.location,
    this.description,
    this.treat,
    this.images,
    this.notes,
  });

  factory PetVetRecord.fromJson(Map<String, dynamic> json) =>
      _$PetVetRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetVetRecordToJson(this);


  static create({
    int? time,
    String? location,
    String? description,
    String? treat,
    List<String>? images,
    String? notes,
  }) {
    return PetVetRecord(
      sid: uuid.v4(),
      time: time,
      location: location,
      description: description,
      treat: treat,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetVetRecord other) {
    return PetVetRecord(
      sid: other.sid,
      notes: other.notes,
      time: other.time,
      location: other.location,
      description: other.description,
      treat: other.treat,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

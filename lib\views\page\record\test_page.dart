// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:onenata_app/models/models_i.dart';
//
// import '../../../models/pet.dart';
// import '../../../models/pet_feeding_record.dart';
// import '../../../models/user_account.dart';
// import '../../../models/user_data.dart';
// import '../../../services/auth_service.dart';
// import '../../../services/pet_service.dart';
// import '../../../services/user_service.dart';
// import '../../../views/component/widgets/common_text.dart';
// import '../../../views/theme/theme_plugin.dart';
// import '../../../vo/pet_profile_vo.dart';
// import '../../component/progress/loading_widget.dart';
// import '../../component/record/record_widget_builder.dart';
// import '../../component/widgets/common_page.dart';
// import '../../component/widgets/common_text_field.dart';
// import '../../component/widgets/common_text_field3.dart';
// import '../page_interceptor.dart';
// import 'record_page.dart';
//
// class TestPage extends StatefulWidget {
//   const TestPage({super.key});
//
//   @override
//   State<TestPage> createState() => TestPageState();
// }
//
// class TestPageState extends State<TestPage> {
//   late Future<TestPageController> _controller;
//   late TabController _tabController;
//   bool isExpanded = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _controller = Get.putAsync(() => TestPageController().init());
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return FutureBuilder<TestPageController>(
//       future: _controller,
//       builder: (context, snapshot) {
//         if (snapshot.connectionState == ConnectionState.waiting) {
//           return const Center(child: LoadingWidget());
//         } else if (snapshot.hasError) {
//           return Center(child: Text('Error: ${snapshot.error}'));
//         } else {
//           final controller = snapshot.data!;
//           return CommonPage(
//             theme: controller.theme,
//             backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
//             body: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Expanded(
//                   child: SingleChildScrollView(
//                     padding: EdgeInsets.only(bottom: 24.w),
//                     physics: const BouncingScrollPhysics(),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Container(
//                           width: 28.w,
//                           height: 28.w,
//                           decoration: BoxDecoration(
//                               shape: BoxShape.circle, color: Color(0xFFFFF111)),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         }
//       },
//     );
//   }
// }
//
// class TestPageController extends GetxController {
//   late final ThemePlugin theme;
//   final AuthService _authService = AuthService.instance;
//   final UserService _userService = UserService();
//   final PetService _petService = PetService();
//   var userAccount = Rx<UserAccount?>(null);
//   UserData? userData;
//   List<Pet>? petList;
//   PetFeedingRecord? record;
//
//   Future<TestPageController> init() async {
//     await PageInterceptor.pageAuthCheck();
//
//     // Loading theme
//     theme = await Get.putAsync(() => ThemePlugin().init());
//     userAccount.value = _authService.userAccount.value;
//     //userAccount.value.fid;
//     //userData=await _userService.getUserDataById(userId:userAccount.value?.sid);
//     userData = _authService.userData.value;
//     userAccount.value = _authService.userAccount.value;
//     petList = await _petService.getOwnedPets(userAccount.value!.sid!);
//
//     final pet = petList!.first;
//     final PetFeedingRecord newRecord = PetFeedingRecord.create(
//       foodTypes: ['test food'],
//       amount: '100g',
//     );
//     //await _petService.recordPetFeeding(pet.sid!, newRecord);
//     //await _petService.deletePetFeedingRecord(pet.sid!, "e804bf76-ab75-4e33-bc84-472b1f20af33");
//     // await _petService.recordPetFeeding(newRecord);
//     // await _petService.recordPetPoop(newRecord2);
//     //await _petService.recordPetDaycare(newRecord3);
//     print(userAccount);
//     print(pet);
//     print(newRecord);
//     return this;
//   }
// }

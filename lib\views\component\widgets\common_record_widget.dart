import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../theme/theme_plugin.dart';
import 'common_text.dart';

class CommonRecordWidget extends StatelessWidget {
  final String type;
  final String time;
  final String description;
  final VoidCallback onDelete;
  final ThemePlugin theme;
  final VoidCallback? onTap;

  const CommonRecordWidget({
    super.key,
    required this.theme,
    required this.type,
    required this.time,
    required this.description,
    required this.onDelete,
    this.onTap,
  });

  IconData _getIcon() {
    switch (type) {
      case 'feed':
        return Icons.restaurant;
      case 'poop':
        return Icons.cruelty_free;
      case 'walk':
        return Icons.directions_walk;
      case 'social':
        return Icons.group;
      case 'med':
        return Icons.medication;
      case 'vaccine':
        return Icons.vaccines;
      case 'grooming':
        return Icons.cut;
      case 'daycare':
        return Icons.home_work;
      case 'vet':
        return Icons.local_hospital;
      case 'other':
        return Icons.more_horiz;
      case 'note':
        return Icons.edit_note;
      case 'event':
        return Icons.event;
      default:
        return Icons.pets;
    }
  }

  Color _getIconColor() {
    switch (type) {
      case 'feed':
        return const Color.fromRGBO(242, 211, 164, 1);
      case 'poop':
        return const Color.fromRGBO(122, 41, 23, 1);
      case 'walk':
        return const Color.fromRGBO(130, 196, 60, 1);
      case 'social':
        return const Color.fromRGBO(56, 151, 240, 1);
      case 'med':
        return const Color.fromRGBO(56, 151, 240, 1);
      case 'vaccine':
        return const Color.fromRGBO(235, 195, 81, 1);
      case 'grooming':
        return const Color.fromRGBO(161, 38, 255, 1);
      case 'daycare':
        return const Color.fromRGBO(83, 225, 183, 1);
      case 'vet':
        return const Color.fromRGBO(255, 197, 66, 1);
      case 'other':
        return const Color.fromRGBO(209, 109, 106, 1);
      case 'note':
        return const Color.fromRGBO(140, 140, 140, 1);
      case 'event':
        return const Color.fromRGBO(80, 80, 160, 1);
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: ValueKey(time + description),
      direction: DismissDirection.endToStart,
      background: Container(),
      secondaryBackground: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.only(right: 24.w),
        child: Icon(Icons.delete,
            color: theme.themeData.colorScheme.tertiary, size: 19),
      ),
      confirmDismiss: (direction) async {
        bool confirmed = false;
        await showModalBottomSheet(
          context: context,
          isScrollControlled: false,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          backgroundColor: theme.themeData.colorScheme.onPrimary,
          builder: (BuildContext context) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 38.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "You are deleting this record",
                    style: theme.textStyleExtension.recordDelete,
                  ),
                  SizedBox(height: 20.w),
                  GestureDetector(
                    onTap: () {
                      confirmed = true;
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(vertical: 16.w),
                      decoration: BoxDecoration(
                        color: theme.themeData.colorScheme.secondary, // 紫色按钮
                        borderRadius: BorderRadius.circular(30.5.r),
                      ),
                      child: Center(
                        child: Text("Continue",
                            style: theme.textStyleExtension.buttonLargeFilled),
                      ),
                    ),
                  ),
                  SizedBox(height: 20.w),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Text("Cancel",
                        style: theme.textStyleExtension.recordDeleteCancel),
                  ),
                ],
              ),
            );
          },
        );

        return confirmed;
      },
      onDismissed: (_) {
        onDelete();
      },
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 16.w),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: theme.themeData.colorScheme.surface,
            borderRadius: BorderRadius.circular(14.r),
            boxShadow: [
              BoxShadow(
                color: theme.colorExtension.eventEventTileShadow1,
                offset: const Offset(0, 4),
                blurRadius: 20,
                spreadRadius: -2,
              ),
              BoxShadow(
                color: theme.colorExtension.eventEventTileShadow2,
                offset: const Offset(0, 0),
                blurRadius: 5,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 52.w,
                height: 52.w,
                decoration: BoxDecoration(
                  color: _getIconColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(52.r),
                ),
                child: Icon(
                  _getIcon(),
                  color: _getIconColor(),
                  size: 28.w,
                ),
              ),
              SizedBox(width: 16.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonText(
                    time,
                    theme.textStyleExtension.recordListTime,
                  ),
                  SizedBox(height: 4.w),
                  Container(
                    width: 250.w,
                    child: CommonText(
                      description,
                      theme.textStyleExtension.recordListDescription,
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class CommonRecordWidgetController {
  late ThemePlugin theme;
  Future<CommonRecordWidgetController> init() async {
    theme = await Get.putAsync(() => ThemePlugin().init());
    return this;
  }
}

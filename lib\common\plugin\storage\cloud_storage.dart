import 'dart:io';
import 'dart:typed_data';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:dio/dio.dart';

import 'package:onenata_app/common/plugin/logger/logger_i.dart';

import 'local_storage.dart';

class CloudStorage {

  final FirebaseStorage _storage = FirebaseStorage.instance;
  final LocalStorage _localStorage = LocalStorage.instance;

  // Save file to firebase storage -------------------------------------------------------------------------------------

  /// Save file on to Firebase storage
  /// [firebaseFilePath] is the file path + name on Firebase storage
  Future<String> uploadFile(String firebaseFilePath, {File? file, Uint8List? bytes}) async {

    Reference ref = _storage.ref().child(firebaseFilePath);
    UploadTask uploadTask;

    if (bytes != null) {
      uploadTask = ref.putData(bytes);
    }
    else {
      uploadTask = ref.putFile(file!);
    }

    // Wait for upload to complete
    TaskSnapshot snapshot = await uploadTask;

    logger.i("✅ File uploaded successfully!");
    return await snapshot.ref.getDownloadURL();
  }

  // Download file from firebase storage ----------------------------------------------------------------------------------

  /// Get download URL of a file in Firebase storage
  /// [firebaseFilePath] is the file path + name on Firebase storage
  Future<String> getDownloadURL(String firebaseFilePath) async {
    Reference ref = _storage.ref().child(firebaseFilePath);
    String url = await ref.getDownloadURL();
    return url;
  }

  /// Download from cloud storage, then save to local storage
  /// [filePath] is the file path + name
  Future<void> downloadFile(String filePath) async {

    String downloadUrl = await getDownloadURL(filePath);

    // Prepare the local path and file name
    final localPath = Directory("${LocalStorage.appDocumentsDir.path}/$filePath");

    // Download the file
    await Dio().download(downloadUrl, localPath.path);

    final file = File(localPath.path);
    logger.d((await file.exists()) ? 'Image downloaded' : 'Image has not been downloaded');
  }
}

import 'dart:io';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/utils/credential_util.dart';

import 'date_time_util.dart';

class FileUtil {

  static String? getFileType(String filePath) {
    final tmp = filePath.split(".");
    if (tmp.length > 1) {
      return tmp.last;
    }
    return null;
  }

  static String? getFileName(String filePath) {
    final tmp = filePath.split("/");
    if (tmp.length > 1) {
      if (tmp.last == '') {
        return null;
      }
      return tmp.last;
    }
    return filePath;
  }

  /// Build the resource path for the file only containing the path without document dir or file name
  /// For firebase storage, the resource path is the key of the object in the bucket
  /// [mType] media type determines the location format of the file
  /// [pType] publish type determines media location
  static String buildUserResourcePath (MediaType mType, String userId, {PostType? pType, String? petId, String? publishId}) {

    String userPath = "${StoragePathConst.storagePathUser}/$userId";

    switch(mType) {

      case MediaType.userAvatar:
        return "$userPath/${StoragePathConst.storagePathUserAvatar}";

      case MediaType.petAvatar:
        return "$userPath/${StoragePathConst.storagePathPet}/$petId/${StoragePathConst.storagePathPetAvatar}";

      case MediaType.image:
      case MediaType.video: {

        if (pType == PostType.moment) {
          return '$userPath/${StoragePathConst.storagePathPublishMoment}/$publishId/${StoragePathConst.storagePathPublishMedia}';
        } else if (pType == PostType.reHome) {
          return '$userPath/${StoragePathConst.storagePathPublishReHome}/$publishId/${StoragePathConst.storagePathPublishMedia}';
        } else if (pType == PostType.help) {
          return '$userPath/${StoragePathConst.storagePathPublishHelp}/$publishId/${StoragePathConst.storagePathPublishMedia}';
        } else {
          return '$userPath/${StoragePathConst.storagePathPublishEvent}/$publishId/${StoragePathConst.storagePathPublishMedia}';
        }
      }
    }
  }

  /// build file name with uuid + file type
  static String buildFileName (String fileType) {
    return '${uuid.v4()}.$fileType';
  }
}

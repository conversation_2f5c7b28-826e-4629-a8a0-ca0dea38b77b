import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class LogInEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();


  final RxBool isEmailEntered = false.obs;
  final RxBool isPasswordEntered = false.obs;
  final RxBool enableButton = false.obs;
  final RxBool isEmailValid = false.obs;
  final RxBool isPasswordValid = false.obs;
  final RxString buttonText = 'Login'.obs;
  final RxBool isPasswordVisible = false.obs;
  final String validCode = '123abcA!';
  //final RxBool isPasswordChecked = false.obs;
  final RxInt failedAttempts = 0.obs;
  final RxBool isLocked = false.obs;
  final RxInt lockoutCountdown = 300.obs;
  Timer? lockoutTimer;

  @override
  void onInit() {
    super.onInit();
    resetState();
  }
  @override
  void dispose() {
    resetState();
    super.dispose();
  }
  void resetState() {
    emailController.clear();
    passwordController.clear();
    isEmailEntered.value = false;
    isPasswordEntered.value = false;
    enableButton.value = false;
    isEmailValid.value = false;
    isPasswordValid.value = false;
    isPasswordVisible.value = false;
    buttonText.value = 'Login';
    //isPasswordChecked.value = false;
    failedAttempts.value = 0;
    isLocked.value = false;
    lockoutCountdown.value = 0;
    lockoutTimer?.cancel();
    lockoutTimer = null;
    updateButtonState();
  }

  bool validateEmail(String email) {
    return RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
        .hasMatch(email);
  }

  bool validatePassword(String password) {
    return password.length >= 8 &&
        RegExp(r"[0-9]").hasMatch(password) &&
        RegExp(r"[A-Z]").hasMatch(password) &&
        RegExp(r"[#\$!%]").hasMatch(password);
  }

  //void updateButtonState() {
    //bool meetsPasswordRules = validatePassword(passwordController.text);
    //isPasswordValid.value = isPasswordChecked.value
      //  ? passwordController.text == validCode
        //: meetsPasswordRules;

    //enableButton.value = isEmailEntered.value &&
    //    isPasswordEntered.value &&
     //   isEmailValid.value &&
     //   isPasswordValid.value;
  //}

  void updateButtonState() {
    bool meetsPasswordRules = validatePassword(passwordController.text);
    isPasswordValid.value = passwordController.text == validCode || meetsPasswordRules;

    enableButton.value = isEmailEntered.value &&
        isPasswordEntered.value &&
        isEmailValid.value &&
        isPasswordValid.value;
  }


  void onSubmit() {
    if (isLocked.value) return;

    //isPasswordChecked.value = true;

    if (passwordController.text == validCode) {
      Get.toNamed('/selectPet');
    } else {
      failedAttempts.value++;
      isPasswordValid.value = false;

      if (failedAttempts.value >= 1) {
        lockLogin();
      }
    }
  }
  void lockLogin() {
    isLocked.value = true;
    failedAttempts.value = 0;
    passwordController.clear();
    isPasswordEntered.value = false;
    isPasswordValid.value = false;
    enableButton.value = false;
    lockoutCountdown.value = 3;
    lockoutTimer?.cancel();
    lockoutTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (lockoutCountdown.value == 0) {
        unlockLogin();
        timer.cancel();
      } else {
        lockoutCountdown.value--;
      }
    });
    updateButtonState();
  }
  void unlockLogin() {
    isLocked.value = false;
    lockoutCountdown.value = 0;
    //enableButton.value = true;
    //passwordController.clear();
    //isPasswordEntered.value = false;
    //isPasswordValid.value = false;
    updateButtonState();
  }
}

import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';

import 'package:get/get.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';

class LocalStorage extends GetxService {

  static LocalStorage get instance => Get.find();
  static Directory appDocumentsDir = Directory('');

  Future<LocalStorage> init() async {
    appDocumentsDir = await getApplicationDocumentsDirectory();
    return this;
  }

  // Build file for local storage --------------------------------------------------------------------------------------

  /// Build the resource path containing the local storage location but without file name
  Future<Directory> buildUserResourceDirectory(MediaType mType, String userId, PostType pType, {String? petId, String? publishId}) async {

    String resPath = FileUtil.buildUserResourcePath(mType, userId, pType: pType, petId: petId, publishId: publishId);

    final filePath = Directory("${appDocumentsDir.path}/$resPath");
    if (!await filePath.exists()) {
      await filePath.create(recursive: true);
    }
    return filePath;
  }

  static String buildUserAvatarFilePath ({required String userId, required String fileName}) {
    String resourcePath = FileUtil.buildUserResourcePath(MediaType.userAvatar, userId);
    return '$resourcePath/$fileName';
  }

  static Directory buildUserAvatarLocalDirectory ({required String userId, required String fileName}) {
    return Directory("${appDocumentsDir.path}/${buildUserAvatarFilePath(userId: userId, fileName: fileName)}");
  }

  static String buildPetAvatarFilePath ({required String userId, required String petId, required String fileName}) {
    String resourcePath = FileUtil.buildUserResourcePath(MediaType.petAvatar, userId, petId: petId);
    return '$resourcePath/$fileName';
  }

  static Directory buildPetAvatarLocalDirectory ({required String userId, required String petId, required String fileName}) {
    return Directory("${appDocumentsDir.path}/${buildPetAvatarFilePath(userId: userId, petId: petId, fileName: fileName)}");
  }

  // Save file to document directory ----------------------------------------------------------------------------------

  /// Save file to the app document directory
  Future<void> saveImageToAppDocumentDir(Directory dir, {required String filename, Uint8List? bytes, String? base64, String? content, FileFormat format = FileFormat.bytes}) async {

    final file = File('${dir.path}/$filename');

    if (format == FileFormat.base64) {
      // Decode Base64 string to bytes first
      Uint8List bytes = base64Decode(base64!);
      await file.writeAsBytes(bytes);
    }
    else if (format == FileFormat.string) {
      await file.writeAsString(content!);
    }
    else {
      await file.writeAsBytes(bytes!);
    }
  }

  /// Save image to specific location as parameter
  Future<void> saveImageToPath({required String path, required String filename, Uint8List? bytes, String? base64, String? content, FileFormat format = FileFormat.bytes}) async {

    final filePath = Directory("${appDocumentsDir.path}/$path");
    if (!await filePath.exists()) {
      await filePath.create(recursive: true);
    }
    await saveImageToAppDocumentDir(filePath, filename: filename, bytes: bytes, base64: base64, content: content, format: format);
  }

  // Save media file to gallery ---------------------------------------------------------------------------------------

  /// Save media file in base64 format to gallery
  Future<void> saveBase64ImageToGallery(String content, {int quality = 100, String? name}) async {

    if (await Permission.storage.isPermanentlyDenied) {
      // Open app settings
      await openAppSettings();
    }

    if (await Permission.storage.request().isGranted || await Permission.photos.request().isGranted) {
      // Decode Base64 string to bytes
      Uint8List bytes = base64Decode(content);
      final result = await ImageGallerySaverPlus.saveImage(bytes, quality: quality, name: name);
      logger.i(result.toString());
    }
  }

  /// Save media file in bytes format to gallery
  Future<void> saveBytesImageToGallery(Uint8List bytes, {int quality = 100, String? name}) async {

    if (await Permission.storage.isPermanentlyDenied) {
      // Open app settings
      await openAppSettings();
    }

    if (await Permission.storage.request().isGranted || await Permission.photos.request().isGranted) {
      // Permissions are granted, proceed with saving the image
      final result = await ImageGallerySaverPlus.saveImage(bytes, quality: quality, name: name);
      logger.i(result.toString());
    }
  }
}

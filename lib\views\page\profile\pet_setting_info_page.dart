import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import '../../../common/utils/date_time_util.dart';
import '../../../models/pet.dart';
import '../../../models/pet_growing_record.dart';
import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/pet_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class PetSettingInfoPage extends StatefulWidget {
  const PetSettingInfoPage({super.key});

  @override
  PetSettingInfoPageState createState() => PetSettingInfoPageState();
}

class PetSettingInfoPageState extends State<PetSettingInfoPage> {
  late Future<PetSettingInfoPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => PetSettingInfoPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<PetSettingInfoPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: SingleChildScrollView(
                //physics: const BouncingScrollPhysics(),
                //padding: EdgeInsets.symmetric(horizontal: 38.w),
                //     child: Column(
                //       crossAxisAlignment: CrossAxisAlignment.start,
                //       children: [
                //         // 顶部栏
                //         ProfileWidgetBuilder.buildUserSettingTopSection(
                //           theme: controller.theme,
                //           onBack: () {
                //             Get.back();
                //           },
                //           topic: "Profile",
                //           showAvatar: false,
                //         ),
                //         //SizedBox(height: 24.w),
                //         _buildAvatar(controller),
                //         SizedBox(height: 24.w),
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Name'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.firstnameInputController, 0),
                //         SizedBox(height: 16.w),
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Type'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Breed'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Weight'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Birthday'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Gender'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildText(controller.theme, 0, 'Next vaccine'),
                //         ProfileWidgetBuilder.buildSettingInputBox(controller.theme, controller.lastnameInputController, 0),
                //         SizedBox(height: 24.w),
                //
                //         ProfileWidgetBuilder.buildChangedNotification(
                //           isVisible: controller.isChangedNotificationVisible,
                //           theme: controller.theme,
                //           top: 0,
                //         ),
                //         SizedBox(height: 24.w),
                //
                //         ProfileWidgetBuilder.buildButton2(
                //           controller.theme,
                //           controller.buttonController,
                //           0,
                //         ),
                //         SizedBox(height: 16.w),
                //
                //         ProfileWidgetBuilder.buildButton2(
                //           controller.theme,
                //           controller.alterButtonController,
                //           0,
                //         ),
                //         SizedBox(height: 60.w),
                //       ],
                //     ),
                //   ),
                // );
                child: Stack(
                  children: [
                    ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Profile",
                      showAvatar: false,
                    ),
                    _buildAvatar(controller),
                    ProfileWidgetBuilder.buildText(
                        controller.theme, 246, 'Name'),
                    ProfileWidgetBuilder.buildSettingInputBox(
                        controller.theme, controller.nameInputController, 286),
                    // ProfileWidgetBuilder.buildText(
                    //     controller.theme, 366, 'Type'),
                    ProfileWidgetBuilder.buildDropdownInput(
                      theme: controller.theme,
                      label: 'Type',
                      selected: controller.selectedType,
                      items: [PetType.cat.t18key.t18, PetType.dog.t18key.t18, PetType.others.t18key.t18],
                      //hintText: 'Select Type',
                      hintText: controller.passedPet!.type?.name.toLowerCase() ?? 'Select Type',
                      onChanged: (val) => controller.selectedType.value = val,
                      top: 366,
                    ),
                    ProfileWidgetBuilder.buildText(
                        controller.theme, 486, 'Breed'),
                    ProfileWidgetBuilder.buildSettingInputBox(
                        controller.theme, controller.breedInputController, 526),
                    ProfileWidgetBuilder.buildText(
                        controller.theme, 606, 'Weight'),
                    ProfileWidgetBuilder.buildSettingInputBox(controller.theme,
                        controller.weightInputController, 646),
                    ProfileWidgetBuilder.buildText(
                        controller.theme, 726, 'Birthday'),
                    ProfileWidgetBuilder.buildSettingInputBox(controller.theme,
                        controller.birthdayInputController, 766),
                    ProfileWidgetBuilder.buildDropdownInput(
                      theme: controller.theme,
                      label: 'Gender',
                      selected: controller.selectedGender,
                      items: [PetGender.boy.t18key.t18, PetGender.girl.t18key.t18],
                      hintText: controller.passedPet?.gender == null
                          ? 'Select Gender'
                          : (controller.passedPet!.gender == PetGender.boy ? PetGender.boy.t18key.t18 : PetGender.girl.t18key.t18),
                      onChanged: (val) => controller.selectedGender.value = val,
                      top: 846,
                    ),
                    ProfileWidgetBuilder.buildText(
                        controller.theme, 966, 'Next vaccine'),
                    ProfileWidgetBuilder.buildSettingInputBox(controller.theme,
                        controller.vaccineInputController, 1006),
                    ProfileWidgetBuilder.buildChangedNotification(
                      isVisible: controller.isChangedNotificationVisible,
                      theme: controller.theme,
                      top: 1086,
                    ),
                    ProfileWidgetBuilder.buildButton2(
                      controller.theme,
                      controller.buttonController,
                      1304,
                    ),
                    ProfileWidgetBuilder.buildButton2(
                      controller.theme,
                      controller.alterButtonController,
                      1364,
                    ),
                  ],
                ),
              ),
            );
          }
        });
  }

//   Widget _buildAvatar(PetSettingInfoPageController controller) {
//     return Column(
//       children: [
//         SizedBox(height: 90.w), // 原来的 top 距离
//         Center(
//           child: GestureDetector(
//             onTap: controller.pickImage,
//             child: AuthWidgetBuilder.buildAvatar(
//               controller.theme,
//               avatar: controller.pageHeaderUserAvatar.value,
//               editable: true,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }

  Widget _buildAvatar(PetSettingInfoPageController controller) {
    return Column(
      children: [
        SizedBox(height: 90.w),
        Center(
          child: GestureDetector(
            onTap: controller.pickImage,
            child: controller.pageHeaderUserAvatar.value != null
                ? controller.pageHeaderUserAvatar.value!
                : AuthWidgetBuilder.buildAvatar(
              controller.theme,
              avatar: null,
              editable: true,
            ),
          ),
        ),
      ],
    );
  }
}

class PetSettingInfoPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  final PetService _petService = PetService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;
  final RxString selectedGender = ''.obs;
  final RxString selectedType = ''.obs;
  late Pet? passedPet;

  final TextEditingController nameInputBox = TextEditingController();
  late CommonTextField3Controller nameInputController;
  final TextEditingController breedInputBox = TextEditingController();
  late CommonTextField3Controller breedInputController;
  // final TextEditingController typeInputBox = TextEditingController();
  // late CommonTextField3Controller typeInputController;
  final TextEditingController weightInputBox = TextEditingController();
  late CommonTextField3Controller weightInputController;
  final TextEditingController birthdayInputBox = TextEditingController();
  late CommonTextField3Controller birthdayInputController;
  // final TextEditingController genderInputBox = TextEditingController();
  // late CommonTextField3Controller genderInputController;
  final TextEditingController vaccineInputBox = TextEditingController();
  late CommonTextField3Controller vaccineInputController;

  // final TextEditingController firstnameInputBox = TextEditingController();
  // late CommonTextField3Controller firstnameInputController;
  // final TextEditingController lastnameInputBox = TextEditingController();
  // late CommonTextField3Controller lastnameInputController;

  final RxBool isFirstNameEntered = false.obs;
  final RxBool isLastNameEntered = false.obs;
  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;
  Pet? currentPet;

  Future<PetSettingInfoPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    passedPet = (Get.arguments as Map<String, dynamic>?)?['selectedPet'];

    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    //userAccount.value.fid;
    //userData=await _userService.getUserDataById(userId:userAccount.value?.sid);
    UserData? userData = _authService.userData.value;
    // firstNameHint=(userData?.firstName?? 'userFirstName').obs;
    // lastNameHint=(userData?.lastName?? 'userLastName').obs;

    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(theme,
        userId: _authService.userAccount.value!.sid!, avatar: userData?.avatar);

    breedInputController = ProfileWidgetBuilder.buildBreedTextFieldController(
      theme, breedInputBox.obs, "breed.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    nameInputController = ProfileWidgetBuilder.buildNameTextFieldController(
      theme, nameInputBox.obs, "name.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    weightInputController = ProfileWidgetBuilder.buildWeightTextFieldController(
      theme, weightInputBox.obs, "weight.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    birthdayInputController =
        ProfileWidgetBuilder.buildBirthdayTextFieldController(
      theme, birthdayInputBox.obs, "birthday.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );
    vaccineInputController =
        ProfileWidgetBuilder.buildVaccineTextFieldController(
      theme, vaccineInputBox.obs, "birthday.hind".t18.obs,
      //onCleared: onPhoneNumberCleared.obs,
      // onChanged: isInputPhoneNumberValid.obs,
    );

    // firstnameInputController = ProfileWidgetBuilder.buildFirstNameTextFieldController(
    //     theme, firstnameInputBox.obs,firstNameHint
    //   //onCleared: onPhoneNumberCleared.obs,
    //   // onChanged: isInputPhoneNumberValid.obs,
    // );
    // lastnameInputController = ProfileWidgetBuilder.buildLastNameTextFieldController(
    //     theme, lastnameInputBox.obs,lastNameHint
    //   //onCleared: onPhoneNumberCleared.obs,
    //   // onChanged: isInputPhoneNumberValid.obs,
    // );
    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'save.changes'.t18.obs,
      onPressed: saveChanges.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'cancel'.t18.obs,
      onPressed: onCancelPressed.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );


    nameInputController.hintText.value = nameInputController.hintText.value.isEmpty
        ? "Your pet's name"
        : nameInputController.hintText.value;

    breedInputController.hintText.value = breedInputController.hintText.value.isEmpty
        ? "Your pet's breed"
        : breedInputController.hintText.value;

    weightInputController.hintText.value =
    weightInputController.hintText.value.isEmpty ? "kg" : weightInputController.hintText.value;

    birthdayInputController.hintText.value =
    birthdayInputController.hintText.value.isEmpty ? "DD/MM/YY" : birthdayInputController.hintText.value;

    vaccineInputController.hintText.value = "N/A";


    if (passedPet != null) {
      currentPet = passedPet;

      // 获取最新体重
      PetGrowingRecord? record = await _petService.getLatestPetGrowingRecordById(passedPet!.sid!);
      double? latestWeight = record?.weight;

      // 设置 hintText
      nameInputController.hintText.value = passedPet!.name ?? "Your pet's name";
      breedInputController.hintText.value = passedPet!.breed ?? "Your pet's breed";
      weightInputController.hintText.value =
      latestWeight != null ? "${latestWeight.toStringAsFixed(2)} kg" : "kg";

      birthdayInputController.hintText.value = passedPet!.birthday != null
          ? DateFormat("dd/MM/yyyy").format(
          DateTime.fromMillisecondsSinceEpoch(passedPet!.birthday!))
          : "DD/MM/YY";

      vaccineInputController.hintText.value = "N/A";

      // selectedType.value = passedPet.type?.name.toLowerCase() ?? '';
      // selectedGender.value = passedPet.gender == PetGender.boy ? "Male" : "Female";

      pageHeaderUserAvatar.value = await ProfileWidgetBuilder.buildPetAvatar(
        theme,
        userId: _authService.userAccount.value!.sid!,
        petId: passedPet!.sid!,
        avatar: passedPet!.avatar,
        size: 125,
      );
    }

    nameInputBox.addListener(validateInputs);
    breedInputBox.addListener(validateInputs);
    weightInputBox.addListener(validateInputs);
    birthdayInputBox.addListener(validateInputs);
    vaccineInputBox.addListener(validateInputs); // optional

    selectedType.listen((_) => validateInputs());
    selectedGender.listen((_) => validateInputs());

    return this;
  }

  //void saveChanges() {
  Future<void> saveChanges() async {
    if (passedPet == null || passedPet!.sid == null) return;

    final String name = nameInputBox.text.trim();
    final String breed = breedInputBox.text.trim();
    final String weightStr = weightInputBox.text.trim();
    final String birthdayStr = birthdayInputBox.text.trim();
    final String vaccine = vaccineInputBox.text.trim();
    final String type = selectedType.value;
    final String gender = selectedGender.value;

    double? weight = double.tryParse(weightStr.replaceAll(RegExp(r'[^0-9.]'), ''));

    int? birthdayMillis;
    try {
      DateTime birthdayDate = DateFormat("dd/MM/yyyy").parseStrict(birthdayStr);
      if (birthdayDate.isBefore(DateTime.now())) {
        birthdayMillis = DateTimeUtil.normalizeDate(birthdayDate).millisecondsSinceEpoch;
      } else {
        print("Birthday must be before today");
      }
    } catch (e) {
      print("Invalid birthday format: $birthdayStr");
    }

    // 构造更新用的新 Pet 实例
    Pet updatedPet = Pet.copyFrom(passedPet!);

    if (name.isNotEmpty) {
      updatedPet.name = name;
    }
    if (breed.isNotEmpty) {
      updatedPet.breed = breed;
    }
    if (type.isNotEmpty) {
      updatedPet.type = type == PetType.dog.t18key.t18
          ? PetType.dog
          : type == PetType.cat.t18key.t18
          ? PetType.cat
          : PetType.others;
    }
    if (gender.isNotEmpty) {
      updatedPet.gender = gender == PetGender.boy.t18key.t18 ? PetGender.boy : PetGender.girl;
    }
    if (birthdayMillis != null) {
      updatedPet.birthday = birthdayMillis;
    }

    await _petService.updateOwnedPet(updatedPet);

    if (weight != null && weight > 0) {
      PetGrowingRecord record = PetGrowingRecord.create(
        uid: _authService.userAccount.value!.sid!,
        pid: passedPet!.sid!,
        weight: double.parse(weight.toStringAsFixed(2)),
      );
      await _petService.recordPetGrowing(record);
    }

    isChangedNotificationVisible.value = true;
    Future.delayed(const Duration(seconds: 3), () {
      isChangedNotificationVisible.value = false;
    });

    Get.back(); // 返回上一页
  }

  void validateInputs() {
    final name = nameInputBox.text.trim();
    final breed = breedInputBox.text.trim();
    final weightStr = weightInputBox.text.trim();
    final birthdayStr = birthdayInputBox.text.trim();
    final type = selectedType.value.trim();
    final gender = selectedGender.value.trim();

    bool hasInput = name.isNotEmpty ||
        breed.isNotEmpty ||
        weightStr.isNotEmpty ||
        birthdayStr.isNotEmpty ||
        type.isNotEmpty ||
        gender.isNotEmpty;

    bool isBirthdayValid = true;
    if (birthdayStr.isNotEmpty) {
      try {
        DateTime date = DateFormat("dd/MM/yyyy").parseStrict(birthdayStr);
        isBirthdayValid = date.isBefore(DateTime.now());
      } catch (_) {
        isBirthdayValid = false;
      }
    }

    bool isWeightValid = true;
    if (weightStr.isNotEmpty) {
      // ✅ 正则：整数或最多两位小数
      final regex = RegExp(r'^\d+(\.\d{1,2})?$');
      isWeightValid = regex.hasMatch(weightStr);
    }

    final isFormValid = hasInput && isBirthdayValid && isWeightValid;
    buttonController.isEnabled.value = isFormValid;
  }

  Future<void> onCancelPressed() async {
    Get.back();
  }

  Future<void> pickImage() async {
    // upload image
    XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      String uid = _authService.userAccount.value!.sid!;
      await _userService.uploadUserAvatar(uid: uid, image: image);
    }
  }
}

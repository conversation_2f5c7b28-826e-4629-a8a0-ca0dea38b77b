import 'package:flutter/material.dart';

class LoadingWidget extends StatelessWidget {
  final String? msg;

  const LoadingWidget({super.key, this.msg});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(15))),
        width: 130,
        height: 130,
        child: Column(
          children: [
            const Spacer(),
            <PERSON><PERSON><PERSON><PERSON>(
                width: 32,
                height: 32,
                child: const CircularProgressIndicator(
                    color: Colors.blueAccent, backgroundColor: Colors.white)),
            SizedBox(height: 15),
            Text(
              msg ?? "Loading...",
              style: TextStyle(fontSize: 14, color: Colors.white),
              softWrap: true,
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
            const Spacer(),
          ],
        ));
  }
}

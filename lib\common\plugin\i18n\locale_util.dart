import 'dart:ui';

import 'package:get/get.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';

class LocaleUtil {

  static Locale systemLocale() {
    return PlatformDispatcher.instance.locale;
  }

  static String systemLocaleCode() {
    Locale sysLocale = PlatformDispatcher.instance.locale;
    return sysLocale.toString().replaceAll('_', '-');
  }

  static Future<Locale?> storedLocale() async {
    return await _readStoredPreferenceLocale();
  }

  static Future<List<Locale>> supportedLocales() async {

    // Map over the SupportedLocales enum and build a Future<Locale> for each one
    List<Locale?> locales = await Future.wait(
        SupportedLocales.values.map((locale) {
          return localeWithoutRegion(locale.code);
        }).toList()
    );

    // Filter out null values
    return locales.whereType<Locale>().toList();
  }

  static Future<void> setPreferenceLocale(Locale locale) async {

    // Update app global variable
    preferenceLocale = locale;

    // Save to storage
    await _writeStoredPreferenceLocale(locale);

    // Take effect
    await _updatePreferenceLocale(locale);
  }

  static Future<Locale?> _readStoredPreferenceLocale() async {

    if (Storage.instance.hasData(StorageKeys.preferenceLocale)) {

      String localeCode = await Storage.instance.read(StorageKeys.preferenceLocale);
      return buildLocale(localeCode);
    }

    return null;
  }

  static Future<void> _writeStoredPreferenceLocale(Locale locale) async {
    String localeCode = locale.toString().replaceAll('_', '-');
    await Storage.instance.write(StorageKeys.preferenceLocale, localeCode);
  }

  static Future<void> _updatePreferenceLocale(Locale locale) async {
    await Get.updateLocale(locale);
  }

  static Future<Locale?> buildLocale(String localeCode) async {

    // Update app global variable
    List<String> localeParts = localeCode.split('-');

    // Only language code
    if (localeParts.length == 1) {
      return Locale(localeCode);
    }
    // Lang code + script code
    else if (localeParts.length == 2) {
      return Locale(localeParts[0], localeParts[1]);
    }
    // Lang code + script code + country code
    else if (localeParts.length == 3) {
      return Locale.fromSubtags(
        languageCode: localeParts[0],
        scriptCode: localeParts[1],
        countryCode: localeParts[2],
      );
    }
    return null;
  }

  static Future<Locale> buildLocaleWithoutRegion(Locale locale) async {

    return Locale.fromSubtags(languageCode: locale.languageCode, scriptCode: locale.scriptCode);
  }

  static Future<Locale?> localeWithoutRegion(String localeCode) async {

    Locale? fullLocale = await buildLocale(localeCode);
    if (fullLocale == null) {
      return null;
    }
    return await buildLocaleWithoutRegion(fullLocale);
  }

}

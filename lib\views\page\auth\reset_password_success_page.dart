import 'dart:io';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';

class ResetPasswordSuccessPage extends StatefulWidget {
  const ResetPasswordSuccessPage({super.key});

  @override
  ResetPasswordSuccessPageState createState() =>
      ResetPasswordSuccessPageState();
}

class ResetPasswordSuccessPageState extends State<ResetPasswordSuccessPage> {
  late Future<ResetPasswordSuccessPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller =
        Get.putAsync(() => ResetPasswordSuccessPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ResetPasswordSuccessPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;

            return CommonPage(
                theme: controller.theme,
                backgroundColor: controller.theme.themeData.colorScheme.primary,
                body: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    SizedBox(
                      child: AuthWidgetBuilder.buildLightRing(controller.theme),
                    ),
                    AuthWidgetBuilder.buildWhiteSection(controller.theme),
                    Column(
                      children: [
                        SizedBox(
                            height: controller.theme.layout.authAvatarHeaderLineHeight +
                                controller.theme.layout.authAvatarTitleMarginTop + controller.theme.layout.authWhiteSectionCircular / 2
                        ),
                        SizedBox(
                          height: 120.w,
                          child: Obx(()=> AuthWidgetBuilder.buildLabel(controller.theme, title: controller.pageLabelTitle.value, desc: controller.pageLabelDesc.value)),
                        ),
                        Expanded(child: SizedBox.shrink()),
                        SizedBox(
                          height: 60.w,
                            child: AuthWidgetBuilder.buildButton2(
                              controller.theme,
                              controller.buttonController,
                            )),
                        SizedBox(height: 35.w,)
                      ],
                    ),
                    // Back button
                    Positioned(
                      top: 60.w,
                      left: 24.w,
                      child: AuthWidgetBuilder.buildBackButton(controller.theme, context),
                    ),
                  ],
                )
            );
          }
        });
  }
}

class ResetPasswordSuccessPageController extends GetxController{

  final AuthService authService = AuthService.instance;
  final UserService userService = UserService();

  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;

  var pageLabelTitle = Rx<CommonText?>(null);
  var pageLabelDesc = Rx<CommonText?>(null);

  Future<ResetPasswordSuccessPageController> init() async {

    if (authService.currentUser.value == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAll(ResetPasswordSuccessPage());
        return;
      });
    }

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    // Init page label
    pageLabelTitle.value = CommonText(
      'auth.label.reset.password.success.title'.t18,
      theme.textStyleExtension.authPageTitle,
    );
    pageLabelDesc.value = CommonText(
      'auth.label.reset.password.success.desc'.t18,
      theme.textStyleExtension.authPageDesc,
      width: 350.w,
      height: 70.w,
      maxLines: 3,
      softWrap: true,
    );

    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'auth.button.login.2'.t18.obs,
      onPressed: onPressed.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );

    return this;
  }

  Future<void> onPressed() async {
    Get.offAll(()=> LogInEmailPage());
    return;
  }

}

import 'package:json_annotation/json_annotation.dart';

part 'appointment_time_info.g.dart';

@JsonSerializable()
class AppointmentTimeInfo {
  String date;                    // YYYY-MM-DD
  String startTime;               // HH:MM
  String endTime;                 // HH:MM
  int duration;                   // 时长（分钟）
  List<String> timeSlots;         // 时间段列表

  AppointmentTimeInfo({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.timeSlots,
  });

  factory AppointmentTimeInfo.fromJson(Map<String, dynamic> json) =>
      _$AppointmentTimeInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AppointmentTimeInfoToJson(this);

  Map<String, dynamic> toFirestoreData() {
    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[_camelCaseToSnakeCase(key)] = value;
    });
    return dbData;
  }

  static String _camelCaseToSnakeCase(String input) {
    final RegExp exp = RegExp(r'(?<=[a-z])[A-Z]');
    return input.replaceAllMapped(exp, (Match m) => ('_${m.group(0)!}')).toLowerCase();
  }
}

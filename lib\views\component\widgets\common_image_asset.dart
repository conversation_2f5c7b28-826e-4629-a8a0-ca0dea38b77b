import 'dart:io';
import 'package:flutter/material.dart';

// Load image from assets/images
class CommonImageAsset extends StatelessWidget {

  final String? path; // file from asset bundles
  final File? file; // file from document directory
  final String? url; // file from network
  final bool? isCircle;

  final double width;
  final double height;
  final double? circular;
  final BoxFit? fit;
  final Color? color;
  final BlendMode? colorBlendMode;

  const CommonImageAsset({
    super.key,
    this.path,
    this.file,
    this.url,
    this.isCircle = false,
    required this.width,
    required this.height,
    this.circular,
    this.fit = BoxFit.cover,
    this.color,
    this.colorBlendMode,
  });

  @override
  Widget build(BuildContext context) {

    Widget? child;

    if (path != null) {
      child = Image.asset('assets/images/$path',
          width: width,
          height: height,
          color: color,
          colorBlendMode: colorBlendMode,
          fit: fit);

    }
    else if (file != null) {
      child = Image.file(
          file!,
          width: width,
          height: height,
          color: color,
          colorBlendMode: colorBlendMode,
          fit: fit);
    }
    else {
      child = Image.network(
          url!,
          width: width,
          height: height,
          color: color,
          colorBlendMode: colorBlendMode,
          fit: fit);
    }

    return isCircle!
        ? ClipOval(child: child)
        : circular == null
        ? child
        : ClipRRect(
      borderRadius: BorderRadius.circular(circular!),
      child: child,);
  }
}

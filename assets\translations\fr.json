{"common.button.ok": "OK", "common.button.confirm": "Confirmer", "common.button.cancel": "Annuler", "common.button.acknowledge": "Reconnu", "common.exception.title": "<PERSON><PERSON>ur temporaire", "common.today": "Auj.", "common.today.is": "Auj. est", "common.today.back": "< Auj.", "common.age.year": "ann<PERSON>", "common.age.month": "mois", "common.app.init": "Initialisation de l'app. Ne pas quitter...", "api.exception.not_found": "Non trouvé", "api.exception.duplicate": "Doublon, existe déjà", "api.exception.network.issue.title": "Problème de réseau", "api.exception.network.issue.timeout": "<PERSON><PERSON><PERSON>", "api.exception.network.issue.connection": "Mauvaise connexion", "api.exception.network.issue.cert": "Certificat incorrect", "api.exception.network.issue.unknown": "<PERSON><PERSON><PERSON> inconnue", "api.exception.service_not_available": "<PERSON><PERSON><PERSON><PERSON>, nous rencontrons un problème pour traiter votre demande, veuillez réessayer plus tard", "api.exception.not_authenticated": "Veuillez vous connecter pour continuer", "api.exception.client.error": "Nous nous excusons pour le désagrément. Veuillez réessayer plus tard. Si le problème persiste, vous pouvez entrer le résultat manuellement en cliquant sur le bouton en bas à droite ou nous contacter.", "api.response.status.code.400": "400, <PERSON><PERSON><PERSON><PERSON> requête", "api.response.status.code.401": "401, Non autorisé", "api.response.status.code.403": "403, Pa<PERSON> de permission", "api.response.status.code.404": "404, Non trouvé", "api.response.status.code.405": "405, Méthode non autorisée", "api.response.status.code.408": "408, <PERSON><PERSON><PERSON>u<PERSON> d<PERSON>", "api.response.status.code.409": "409, <PERSON><PERSON><PERSON> de ressource", "api.response.status.code.500": "500, <PERSON><PERSON><PERSON> serveur", "api.response.status.code.501": "501, Méthode non implémentée", "api.response.status.code.502": "502, <PERSON><PERSON><PERSON><PERSON> passerelle", "api.response.status.code.503": "503, Service non disponible", "api.response.status.code.504": "504, <PERSON><PERSON><PERSON> passerelle dé<PERSON>", "api.response.status.code.505": "505, Version HTTP non supportée", "save": "Enregistrer", "success.key": "Su<PERSON>ès", "fail.key": "Échec", "error.key": "Une erreur s'est produite", "action.frequently": "Traitement des données, veuil<PERSON>z réessayer plus tard", "app.title": "Initialisation de l'app. Ne pas quitter...", "device.home.title": "<PERSON><PERSON>", "device.home.search.hint": "Rechercher nom d'appareil ou marque...", "device.home.stats.online": "Appareils En Ligne", "device.home.stats.offline": "Appareils Hors Ligne", "device.home.stats.total": "Total Appareils", "device.home.status.online": "En Ligne", "device.home.status.offline": "<PERSON><PERSON>", "device.home.pets.count": "Animaux associés: {count}", "device.home.unknown.device": "Appareil <PERSON>nu", "device.home.firmware.version": "Firmware: {version}", "device.home.options.title": "Options de l'Appareil", "device.home.options.settings": "Paramètres de l'Appareil", "device.home.options.manage.pets": "<PERSON><PERSON><PERSON> les Animaux", "device.home.options.device.info": "Informations de l'Appareil", "device.home.options.delete": "Supprimer l'Appareil", "device.home.info.title": "Informations de l'Appareil", "device.home.info.device.type": "Type d'Appareil", "device.home.info.manufacturer": "Fabricant", "device.home.info.serial.number": "Numéro de Série", "device.home.info.firmware.version": "Version Firmware", "device.home.info.hardware.version": "Version Matériel", "device.home.info.sync.status": "État de Synchronisation", "device.home.info.synced": "Synchronisé", "device.home.info.not.synced": "Non Synchronisé", "device.home.delete.title": "Supprimer l'Appareil", "device.home.delete.confirm": "Êtes-vous sûr de vouloir supprimer l'appareil \"{name}\"? Cette action ne peut pas être annulée.", "device.home.delete.success": "L'appareil \"{name}\" a été supprimé", "device.home.close": "<PERSON><PERSON><PERSON>", "device.home.delete.action": "<PERSON><PERSON><PERSON><PERSON>", "device.add.title": "Ajouter un Appareil", "device.add.save": "Enregistrer", "device.add.basic.info": "Informations de Base", "device.add.device.name": "Nom de l'Appareil", "device.add.device.name.hint": "Entrez le nom de l'appareil (optionnel)", "device.add.device.type": "Type d'Appareil", "device.add.device.model": "<PERSON><PERSON><PERSON><PERSON>ppa<PERSON>il", "device.add.manufacturer.info": "Informations du Fabricant", "device.add.manufacturer": "Fabricant", "device.add.manufacturer.hint": "Entrez le nom du fabricant", "device.add.serial.number": "Numéro de Série", "device.add.serial.number.hint": "Entrez le numéro de série de l'appareil", "device.add.version.info": "Informations de Version", "device.add.firmware.version": "Version du Firmware", "device.add.firmware.version.hint": "Entrez la version du firmware (optionnel)", "device.add.hardware.version": "Version du Matériel", "device.add.hardware.version.hint": "Entrez la version du matériel (optionnel)", "device.add.associated.pets": "An<PERSON><PERSON>", "device.add.select.pets": "Sélectionnez les animaux à associer", "device.add.add.device": "Ajouter l'Appareil", "device.add.field.required": "{field} est requis", "device.add.select": "Sélectionner {field}", "device.add.required.fields.missing": "Veuillez remplir tous les champs obligatoires", "device.add.success": "Appareil ajouté avec succès", "device.add.error": "Échec de l'ajout de l'appareil, veuil<PERSON><PERSON> réessayer", "device.home.empty.title": "Aucun appareil", "device.home.empty.subtitle": "Appuyez sur le bouton d'ajout en bas à droite\npour commencer à ajouter votre premier appareil", "geo.search.hint": "Rechercher des lieux, magasins ou services", "geo.business.hours.unknown": "Horaires d'ouverture inconnus", "geo.business.status.unknown": "Statut commercial inconnu", "geo.business.status.open": "Ouvert", "geo.business.status.closed": "<PERSON><PERSON><PERSON>", "geo.business.time.unknown": "Heure inconnue", "geo.business.opens": "ouvre", "geo.business.closes": "ferme", "geo.business.hours.unavailable": "Informations d'horaires indisponibles", "geo.report.hours": "Signaler les horaires", "geo.report.correction": "Signaler une correction", "geo.place.unknown": "<PERSON><PERSON> inconnu", "geo.report.hours.title": "Signaler les Horaires d'Ouverture", "geo.report.type.title": "Sélectionner le type de signalement", "geo.report.type.business.hours.title": "Signaler les horaires d'ouverture", "geo.report.type.business.hours.description": "Fournir les informations d'horaires pour ce lieu", "geo.report.type.current.status.title": "Signaler le statut actuel", "geo.report.type.current.status.description": "Signaler si ce lieu est actuellement ouvert", "geo.report.type.correction.title": "Correction d'information", "geo.report.type.correction.description": "Signaler des erreurs dans les informations existantes", "geo.report.business.hours.form.title": "<PERSON><PERSON><PERSON>z remplir les horaires d'ouverture", "geo.report.current.status.title": "Veuillez sélectionner le statut actuel", "geo.report.correction.form.title": "Correction d'information", "geo.report.correction.form.description": "Veuillez fournir des informations détaillées sur ce qui doit être corrigé dans les notes ci-dessous", "geo.report.status.open.now": "Actuellement ouvert", "geo.report.status.closed.now": "Actuellement fermé", "geo.report.status.temporarily.closed": "Temporairement fermé", "geo.report.status.permanently.closed": "Fermé définitivement", "geo.weekday.sunday": "<PERSON><PERSON>", "geo.weekday.monday": "<PERSON>n", "geo.weekday.tuesday": "Mar", "geo.weekday.wednesday": "<PERSON><PERSON>", "geo.weekday.thursday": "<PERSON><PERSON>", "geo.weekday.friday": "Ven", "geo.weekday.saturday": "Sam", "geo.time.open": "Heure d'ouverture", "geo.time.close": "Heure de fermeture", "geo.report.notes.title": "Notes supplémentaires (optionnel)", "geo.report.notes.hint": "Veuillez saisir toute information supplémentaire...", "geo.report.submit": "Soumettre le signalement", "geo.report.success.title": "Signalement soumis", "geo.report.success.message": "Merci pour votre signalement ! Nous traiterons les informations que vous avez fournies dès que possible.", "geo.report.submitted": "Signalement soumis, merci pour votre retour !", "geo.report.correction.title": "Signaler une Correction", "geo.report.current.status": "Veuillez sélectionner le statut d'exploitation actuel", "geo.report.notes": "Notes Supplémentaires (Optionnel)", "unknown": "Inconnu", "Open": "Ouvert", "Closed": "<PERSON><PERSON><PERSON>", "Opens": "<PERSON><PERSON><PERSON>", "Closes": "<PERSON><PERSON><PERSON>", "place.unknown": "<PERSON><PERSON>", "place.reviews": "avis", "place.business.hours": "Heures d'Ouverture", "place.hours.unknown": "Heures inconnues", "place.address": "<PERSON><PERSON><PERSON>", "place.checkin": "S'enregistrer"}
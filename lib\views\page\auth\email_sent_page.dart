import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

class EmailSentPage extends StatefulWidget {
  const EmailSentPage({super.key});

  @override
  EmailSentPageState createState() => EmailSentPageState();
}

class EmailSentPageState extends State<EmailSentPage> {
  late Future<EmailSentPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => EmailSentPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<EmailSentPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  _buildBackButton(),
                  _buildAvatar(),
                  _buildWhiteSection(),
                  _buildWelcomeText(controller),
                  _buildTextLabel(controller),
                ],
              ),
            );
          }
        });
  }

  Widget _buildBackButton() {
    return CommonBackButton(
      onPressed: () {
        Get.back();
      },
    );
  }

  Widget _buildAvatar() {
    return Positioned(
      width: 447.11.w,
      height: 443.96.w,
      left: -22.w,
      child: Image.asset(
        'assets/images/welcome.png',
      ),
    );
  }

  Widget _buildWhiteSection() {
    return const CommonWhiteSection(
      width: 403,
      height: 639,
      top: 222,
      borderRadius: 85,
    );
  }

  Widget _buildWelcomeText(EmailSentPageController controller) {
    return Positioned(
      top: 274.w,
      left: 38.w,
      child: Text(
        'email.sent'.t18,
        style: controller.theme.textStyleExtension.authPageTitle,
      ),
    );
  }

  Widget _buildTextLabel(EmailSentPageController controller) {
    return Positioned(
      top: 307.w,
      left: 38.w,
      child: Text(
        'please.check.email'.t18,
        style: controller.theme.textStyleExtension.authPageDesc,
      ),
    );
  }
}

class EmailSentPageController extends GetxController {

  // Theme plugin
  late ThemePlugin theme;

  Future<EmailSentPageController> init() async {

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    return this;
  }
}

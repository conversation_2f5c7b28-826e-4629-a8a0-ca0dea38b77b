// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_photo.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoPhoto _$GeoPhotoFromJson(Map<String, dynamic> json) => GeoPhoto(
      name: json['name'] as String?,
      widthPx: (json['widthPx'] as num?)?.toInt(),
      heightPx: (json['heightPx'] as num?)?.toInt(),
      authorAttributions: (json['authorAttributions'] as List<dynamic>?)
          ?.map((e) => GeoAuthorAttribution.fromJson(e as Map<String, dynamic>))
          .toList(),
      flagContentUri: json['flagContentUri'] as String?,
      googleMapsUri: json['googleMapsUri'] as String?,
    );

Map<String, dynamic> _$GeoPhotoToJson(GeoPhoto instance) => <String, dynamic>{
      'name': instance.name,
      'widthPx': instance.widthPx,
      'heightPx': instance.heightPx,
      'authorAttributions': instance.authorAttributions,
      'flagContentUri': instance.flagContentUri,
      'googleMapsUri': instance.googleMapsUri,
    };

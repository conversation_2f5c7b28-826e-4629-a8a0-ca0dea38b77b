import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_owner_record.g.dart';

/// The record of decouple of the ownership between a pet and a user.
/// Current owner will be stored in the pet itself.
@JsonSerializable()
class PetOwnerRecord extends BaseFullModel {

  String uid;
  String pid;

  PetOwnerRecord({
    required this.uid,
    required this.pid,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory PetOwnerRecord.fromJson(Map<String, dynamic> json) => _$PetOwnerRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetOwnerRecordToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory PetOwnerRecord.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return PetOwnerRecord.fromJson(jsonData);
  }

  factory PetOwnerRecord.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return PetOwnerRecord.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String collection ()=> 'pet_owner_record';

  static create ({
    required String uid,
    required String pid,
  }) {

    return PetOwnerRecord(
      sid: uuid.v4(),
      uid: uid,
      pid: pid,
      isValid: true,
    );
  }

  static copyFrom(PetOwnerRecord other) {
    return PetOwnerRecord(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      pid: other.pid,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

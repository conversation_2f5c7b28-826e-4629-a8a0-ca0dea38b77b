import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
// import '../../component/geo/geo_filter_button.dart';
// import '../../component/geo/geo_place_info_card.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/services/services_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'package:onenata_app/views/component/geo/geo_location_widget_builder.dart';
import 'package:onenata_app/views/page/page_interceptor.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_plugin.dart';

class MapPage extends StatefulWidget {
  const MapPage({super.key});

  @override
  MapPageState createState() => MapPageState();
}

class MapPageState extends State<MapPage> {
  late Future<MapPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => MapPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<MapPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  // google map
                  Obx(() => GoogleMap(
                        myLocationEnabled: false,
                        // myLocationEnabled: controller.geoLocationServiceEnabled.isTrue,
                        myLocationButtonEnabled: false,
                        mapType: MapType.normal,
                        // cloudMapId: ApiConst.gmStyleId, TODO change to cloudMapId when flutter google map plugin supports
                        style: controller.mapStyle.value,
                        initialCameraPosition:
                            GeoLocationService.buildCameraPosition(
                                controller.currentLocation.value ??
                                    GeoLocationConst.defaultLatLng),
                        onMapCreated: (GoogleMapController gmc) async {
                          controller.mapReady.value = true;
                          controller.map.complete(gmc);
                        },
                        onCameraIdle: () async {
                          // Stop upload and update pet location periodically
                          controller.endTimer();
                          // if the interval larger than 2 seconds, then update the map
                          if (DateTimeUtil.currentMilliseconds() -
                                  controller.lastDragTime >
                              2000) {
                            controller.lastDragTime =
                                DateTimeUtil.currentMilliseconds();
                            await controller.execTask();
                          }
                        },
                        markers: controller.markers.value ?? {},
                        circles: controller.circles.value ?? {},
                      )),

                  // Search
                  Positioned(
                    top: 50,
                    left: 16,
                    right: 16,
                    child: Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        CommonTextField3(
                          controller: controller.textQueryInputController,
                        ),
                        Container(
                          width:
                              controller.theme.layout.geoSearchTextFieldWidth,
                          height:
                              controller.theme.layout.geoSearchTextFieldHeight,
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.only(left: 13.5.w),
                          child: GeoLocationWidgetBuilder.buildSearchTextButton(
                              controller.theme, context, onPressed: () async {
                            await controller.clickSearchText();
                          }),
                        ),
                      ],
                    ),

                    // Container(
                    //   decoration: BoxDecoration(
                    //     color: Colors.white,
                    //     borderRadius: BorderRadius.circular(16),
                    //     boxShadow: [
                    //       BoxShadow(
                    //         color: Colors.black.withOpacity(0.1),
                    //         blurRadius: 8,
                    //         offset: const Offset(0, 2),
                    //       ),
                    //     ],
                    //   ),
                    //
                    //   //Search Container
                    //   child: Row(
                    //     children: [
                    //       Container(
                    //         padding: const EdgeInsets.all(12),
                    //         child: const Icon(Icons.search, color: Colors.grey),
                    //       ),
                    //       Expanded(
                    //         child: TextField(
                    //           controller: controller.searchController,
                    //           decoration: InputDecoration(
                    //             hintText: 'mappage.search.hint'.t18,
                    //             border: InputBorder.none,
                    //             hintStyle: TextStyle(color: Colors.grey),
                    //           ),
                    //           onSubmitted: (value) => {}
                    //             // todo search places
                    //               // controller.searchPlaces(value),
                    //         ),
                    //       ),
                    //       // Profile avatar
                    //       GestureDetector(
                    //         onTap: () {
                    //           final PageNavigationController navController =
                    //               Get.find<PageNavigationController>();
                    //           navController
                    //               .navigateToPage(PageNavigation.profile);
                    //         },
                    //         child: Container(
                    //           width: 40,
                    //           height: 40,
                    //           margin: const EdgeInsets.all(8),
                    //           child: AuthWidgetBuilder.buildAvatar(
                    //             controller.theme,
                    //             avatar: controller.pageHeaderUserAvatar.value,
                    //           ),
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ),

                  // Filter Button
                  Positioned(
                    top: 120.w,
                    left: 16.w,
                    right: 16.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              // Filter for Dog park
                              CommonButton3(
                                  controller:
                                      controller.filterButtonControllerOutdoor),
                              SizedBox(width: 10.w),

                              // Favourite
                              CommonButton3(
                                  controller:
                                      controller.filterButtonControllerFavor),
                              SizedBox(width: 10.w),

                              // Filter for Vet
                              CommonButton3(
                                  controller:
                                      controller.filterButtonControllerVet),
                              SizedBox(width: 10.w),

                              // Filter for Pet store
                              CommonButton3(
                                  controller: controller
                                      .filterButtonControllerPetStore),
                              SizedBox(width: 10.w),

                              // Filter for Dog friendly restaurant
                              CommonButton3(
                                  controller: controller
                                      .filterButtonControllerDogFriendlyRestaurant),
                            ],
                          ),
                        ),
                        SizedBox(height: 5.w),
                        Obx(
                          () => controller.isOutdoorSubDisplay.isTrue
                              ? SizedBox(
                                  width: controller
                                      .theme.layout.geoFilterButtonWidthPark,
                                  child: CommonButton3(
                                      controller: controller
                                          .filterButtonControllerPark),
                                )
                              : SizedBox.shrink(), // 不显示
                        ),
                        SizedBox(height: 5.w),
                        Obx(
                          () => controller.isOutdoorSubDisplay.isTrue
                              ? SizedBox(
                                  width: controller
                                      .theme.layout.geoFilterButtonWidthTrail,
                                  child: CommonButton3(
                                      controller: controller
                                          .filterButtonControllerTrail),
                                )
                              : SizedBox.shrink(),
                        ),
                      ],
                    ),
                  ),

                  // Weather for today
                  Positioned(
                    top: 175,
                    right: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(100),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.wb_sunny,
                              color: Colors.amber, size: 18),
                          const SizedBox(width: 6),

                          // TODO: weather needs to be get from APi
                          Text(
                            '18 °C',
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Back to current position
                  Align(
                      alignment: Alignment.bottomRight,
                      child: Container(
                          margin: EdgeInsets.only(
                            right: controller
                                .theme.layout.geoMyLocationIconMarginRight,
                            bottom: controller
                                .theme.layout.geoMyLocationIconMarginBottom,
                          ),
                          child: GestureDetector(
                            onTap: () async {
                              await controller.navigateToCurrentLocation();
                            },
                            child: GeoLocationWidgetBuilder.buildMyLocationIcon(
                                controller.theme),
                          ))),

                  // Test button
                  Align(
                      alignment: Alignment.bottomLeft,
                      child: Container(
                          margin: EdgeInsets.only(
                            left: controller
                                .theme.layout.geoMyLocationIconMarginRight,
                            bottom: controller
                                .theme.layout.geoMyLocationIconMarginBottom,
                          ),
                          child: GestureDetector(
                            onTap: controller.loadOtherPets,
                            child: GeoLocationWidgetBuilder.buildMyLocationIcon(
                                controller.theme),
                          ))),

                  // floating actions
                  // Obx(() => controller.mapReady.isTrue &&
                  //         controller.selectedPlace.value != null
                  //     ? Positioned(
                  //         bottom: 16,
                  //         left: 16,
                  //         right: 16,
                  //         child: PlaceInfoCard(
                  //           place: controller.selectedPlace.value!,
                  //           onClose: () => controller.clearSelectedPlace(),
                  //           onNavigate: () => controller.navigateToPlace(
                  //               controller.selectedPlace.value!),
                  //           onAddToFavorites: () => controller.toggleFavorite(
                  //               controller.selectedPlace.value!),
                  //           isFavorite: controller.isPlaceFavorite(
                  //               controller.selectedPlace.value!),
                  //         ),
                  //       )
                  //     : const SizedBox.shrink()),
                ],
              ),
            );
          }
        });
  }
}

class MapPageController extends GetxController {
  // Theme plugin
  late ThemePlugin theme;

  // Geo location
  final Completer<GoogleMapController> map = Completer();
  final GeoLocationService geoLocationService = GeoLocationService();
  RxBool geoLocationServiceEnabled = true.obs;
  RxBool mapReady = false.obs;
  Rx<LatLng?> currentLocation = Rx<LatLng?>(null);
  Timer? timer;
  RxBool isTimerRunning = false.obs;
  int lastDragTime = 0;

  //User Files and pet
  final AuthService authService = AuthService.instance;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);

  final PetService _petService = PetService();
  List<Pet>? petList;

  // Map related
  var markers = Rx<Set<Marker>?>(null);
  var circles = Rx<Set<Circle>?>(null);

  // Search controller
  final TextEditingController searchController = TextEditingController();
  RxString selectedFilter = ''.obs;
  Rx<Map<String, dynamic>?> selectedPlace = Rx<Map<String, dynamic>?>(null);

  // TextEditingController
  final TextEditingController textQueryInputBox = TextEditingController();
  late CommonTextField3Controller textQueryInputController;
  late CommonButton3Controller searchButtonController;

  // TODO to create a dropdown list for dog parks filter button?
  // List<Map<String, dynamic>> dogParks = [];
  // List<Map<String, dynamic>> offLeashTrails = [];
  // List<Map<String, dynamic>> vets = [];
  // List<Map<String, dynamic>> petStores = [];
  // List<Map<String, dynamic>> dogFriendlyRestaurants = [];
  // RxList<Map<String, dynamic>> favorites = <Map<String, dynamic>>[].obs;

  // Geo places
  var mapStyle = ''.obs;
  final GeoLocationService geoService = GeoLocationService();
  var searchedMarkers = Rx<Set<Marker>?>(null);
  var parkMarkers = Rx<Set<Marker>?>(null);
  var trailMarkers = Rx<Set<Marker>?>(null);
  var vetMarkers = Rx<Set<Marker>?>(null);
  var petStoreMarkers = Rx<Set<Marker>?>(null);
  var dogFriendlyRestaurantMarkers = Rx<Set<Marker>?>(null);
  var myPetMarkers = Rx<Set<Marker>?>(null);
  var otherPetMarkers = Rx<Set<Marker>?>(null);
  RxBool isOutdoorSubDisplay = false.obs;
  List<GeoPlace>? searchedPoints;
  List<GeoPlace>? parks;
  List<GeoPlace>? trails;
  List<GeoPlace>? vets;
  List<GeoPlace>? petStores;
  List<GeoPlace>? dogFriendlyRestaurants;
  RxBool isParkSelected = false.obs;
  RxBool isTrailSelected = false.obs;
  RxBool isVetSelected = false.obs;
  RxBool isPetStoreSelected = false.obs;
  RxBool isDogFriendlyRestaurantSelected = false.obs;
  RxBool isFavorSelected = false.obs;
  late CommonButton3Controller filterButtonControllerOutdoor;
  late CommonButton3Controller filterButtonControllerPark;
  late CommonButton3Controller filterButtonControllerTrail;
  late CommonButton3Controller filterButtonControllerVet;
  late CommonButton3Controller filterButtonControllerPetStore;
  late CommonButton3Controller filterButtonControllerDogFriendlyRestaurant;
  late CommonButton3Controller filterButtonControllerFavor;
  var favorParks = Rx<List<GeoFavorPlace>?>(null);
  var favorTrails = Rx<List<GeoFavorPlace>?>(null);
  var favorVets = Rx<List<GeoFavorPlace>?>(null);
  var favorPetStores = Rx<List<GeoFavorPlace>?>(null);
  var favorDogFriendlyRestaurants = Rx<List<GeoFavorPlace>?>(null);
  var parkIcon = Rx<BitmapDescriptor?>(null);
  var parkIconSelected = Rx<BitmapDescriptor?>(null);
  var trailIcon = Rx<BitmapDescriptor?>(null);
  var trailIconSelected = Rx<BitmapDescriptor?>(null);
  var vetIcon = Rx<BitmapDescriptor?>(null);
  var vetIconSelected = Rx<BitmapDescriptor?>(null);
  var petStoreIcon = Rx<BitmapDescriptor?>(null);
  var petStoreIconSelected = Rx<BitmapDescriptor?>(null);
  var dogFriendlyRestaurantIcon = Rx<BitmapDescriptor?>(null);
  var dogFriendlyRestaurantIconSelected = Rx<BitmapDescriptor?>(null);
  var petIconActivated = Rx<BitmapDescriptor?>(null);
  var petIconInActivated = Rx<BitmapDescriptor?>(null);
  var commonIcon = Rx<BitmapDescriptor?>(null);

  Future<MapPageController> init() async {
    // Auth check
    await PageInterceptor.pageAuthCheck();

    // Geo location service enabling check
    geoLocationServiceEnabled.value =
        await Geolocator.isLocationServiceEnabled();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    await rootBundle.loadString('assets/scripts/mapStyle.json').then((string) {
      mapStyle.value = string;
    });

    // Loading init data
    await setCurrentLocation();
    await loadFavorPlaces();
    await loadPlaceIcons();

    // Build avatar
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(theme,
        userId: authService.userAccount.value!.sid!,
        avatar: authService.userData.value?.avatar);

    // build username input controller
    textQueryInputController =
        GeoLocationWidgetBuilder.buildSearchTextFieldController(
      theme,
      textQueryInputBox.obs,
      onCleared: onSearchTextCleared.obs,
      onChanged: isInputSearchTextValid.obs,
    );
    // Listen to the focus node
    textQueryInputController.textFieldFocusNode.addListener(() {
      textQueryInputController.isFocused.value =
          textQueryInputController.textFieldFocusNode.hasFocus;
    });

    searchButtonController = CommonButton3Controller(
      onPressed: clickSearchText.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.park)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthPark.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isParkSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    // Init filter buttons
    filterButtonControllerOutdoor = CommonButton3Controller(
      onPressed: clickFilterOutdoor.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.outdoor)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthOutdoor.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: false.obs,
      // hasBorder: (isParkSelected.isTrue || isTrailSelected.isTrue).obs,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerPark = CommonButton3Controller(
      onPressed: clickFilterPark.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.park)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthPark.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isParkSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerTrail = CommonButton3Controller(
      onPressed: clickFilterTrail.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.trail)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthTrail.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isTrailSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerVet = CommonButton3Controller(
      onPressed: clickFilterVet.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.vet)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthVet.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isVetSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerPetStore = CommonButton3Controller(
      onPressed: clickFilterPetStore.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.petStore)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthPetStore.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isPetStoreSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerDogFriendlyRestaurant = CommonButton3Controller(
      onPressed: clickFilterDogFriendlyRestaurant.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.dogFriendlyRestaurant)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthDogFriendlyRestaurant.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isDogFriendlyRestaurantSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    filterButtonControllerFavor = CommonButton3Controller(
      onPressed: clickFilterFavor.obs,
      shownWidget: GeoLocationWidgetBuilder.buildShownWidgetOfGeoFilterButton(
              theme,
              type: GeoPlaceFilterType.favor)
          .obs,
      textStyle: theme.textStyleExtension.geoFilterButtonText
          .copyWith(
            color: theme.themeData.colorScheme.primary,
          )
          .obs,
      width: theme.layout.geoFilterButtonWidthFavor.obs,
      height: theme.layout.geoFilterButtonHeight.obs,
      hasRadius: true.obs,
      circular: theme.layout.geoFilterButtonBorderRadius.obs,
      hasBorder: isFavorSelected,
      borderColor: theme.themeData.colorScheme.primary.obs,
      borderWidth: theme.layout.geoFilterButtonSelectedBorderWidth.obs,
      hasShadow: true.obs,
      shadowColor: theme.colorExtension.geoFilterButtonShadow
          .withAlpha(0.2.colorAlpha)
          .obs,
      shadowBlurRadius: 4.w.obs,
    );

    return this;
  }

  @override
  void onInit() {
    super.onInit();

    // Upload pet location every 30 seconds
    // startTimer(); // 注释掉持续定位功能
  }

  @override
  void onClose() {
    endTimer();
    searchController.dispose();
    super.onClose();
  }

  // Load icons
  Future<void> loadPlaceIcons() async {
    parkIcon.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    parkIconSelected.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    trailIcon.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    trailIconSelected.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    vetIcon.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    vetIconSelected.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    petStoreIcon.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    petStoreIconSelected.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    dogFriendlyRestaurantIcon.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    dogFriendlyRestaurantIconSelected.value = await BitmapDescriptor.asset(
      ImageConfiguration(size: Size(50.w, 62.5.w)), // adjust as needed
      'assets/images/<EMAIL>',
    );
    petIconActivated.value =
        await GeoLocationWidgetBuilder.buildPetActiveLocationIcon(theme)
            .toBitmapDescriptor();
    petIconInActivated.value =
        await GeoLocationWidgetBuilder.buildPetInActiveLocationIcon(theme)
            .toBitmapDescriptor();
    commonIcon.value =
        await GeoLocationWidgetBuilder.buildCommonLocationIcon(theme)
            .toBitmapDescriptor();
  }

  // Location functions
  Future<LatLng?> getLatLngScreenCenter() async {
    if (map.isCompleted) {
      final GoogleMapController gmc = await map.future;

      // 假设你获取中心点，通常是屏幕宽高的一半
      final screenCoordinate = ScreenCoordinate(
        x: screenSize.width ~/ 2,
        y: (screenSize.height - 59.w) ~/ 2, // 减去 tab bar height
      );

      return await gmc.getLatLng(screenCoordinate);
    }
    return null;
  }

  Future<LatLng?> getLatLngScreenBottomLeft() async {
    if (map.isCompleted) {
      final GoogleMapController gmc = await map.future;

      LatLngBounds bounds = await gmc.getVisibleRegion();
      return bounds.southwest;
    }
    return null;
  }

  Future<LatLng?> getLatLngScreenTopRight() async {
    if (map.isCompleted) {
      final GoogleMapController gmc = await map.future;

      LatLngBounds bounds = await gmc.getVisibleRegion();
      return bounds.northeast;
    }
    return null;
  }

  Future<void> setCurrentLocation() async {
    if (geoLocationServiceEnabled.isFalse) {
      return;
    }

    Position? position = await GeoLocationService.getCurrentLocation();

    if (position == null) {
      return;
    }

    // current position
    currentLocation.value = LatLng(position.latitude, position.longitude);
  }

  Future<void> animateToPosition(CameraPosition position) async {
    if (map.isCompleted) {
      final GoogleMapController gmc = await map.future;
      await gmc.animateCamera(CameraUpdate.newCameraPosition(position));
    }
  }

  Future<void> navigateToCurrentLocation() async {
    if (currentLocation.value == null) {
      await setCurrentLocation();
    }

    if (currentLocation.value != null) {
      await animateToPosition(
          GeoLocationService.buildCameraPosition(currentLocation.value!));
    }

    await startTimer();
  }

  Future<void> openInfoCard(GeoPlace place, GeoPlaceFilterType type) async {
    print(place.toString());
    print("info:  place photos：  ${place.photos?.length}");
    await reArrangePlaceList(type, place.name ?? '');
    List<GeoPlace> places = [];
    if (type == GeoPlaceFilterType.park) {
      places = parks ?? [];
    } else if (type == GeoPlaceFilterType.trail) {
      places = trails ?? [];
    }

    await Get.bottomSheet(
      isDismissible: true,
      Container(
        height: 500.w,
        child: DraggableScrollableSheet(
          expand: false,
          initialChildSize: 1.0, // 使用全部可用高度
          minChildSize: 1.0, // 固定高度，不允许缩小
          maxChildSize: 1.0, // 固定高度，不允许放大
          builder: (context, scrollController) {
          return Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: EdgeInsets.only(top: 20.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(26.w)),
                ),
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    children: [
                      SingleChildScrollView(
                          child: Column(
                        children: List.generate(
                          places.length,
                          (i) => Container(
                            margin: EdgeInsets.only(
                              bottom: i < places.length - 1 ? 2.w : 0,
                              left: 8.w,
                              right: 8.w,
                            ),
                            child:
                                GeoLocationWidgetBuilder.buildGeoPlaceInfoCard(
                                    theme,
                                    place: places[i]),
                          ),
                        ),
                      ))
                    ],
                  ),
                ),
              ),
              CommonBar(
                color: theme.colorExtension.geoPlaceInfoListScrollBar,
                width: 50.w,
                height: 5.w,
                borderRadius: BorderRadius.all(Radius.circular(100.w)),
                margin: EdgeInsets.only(top: 10.w),
              ),
            ],
          );
        },
      ),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
  }

  Future<void> reArrangePlaceList(
      GeoPlaceFilterType type, String placeName) async {
    if (type == GeoPlaceFilterType.park && parks != null && parks!.isNotEmpty) {
      List<GeoPlace> reArrangedParks = [];
      reArrangedParks.add(parks!.firstWhere((e) => e.name == placeName));
      reArrangedParks.addAll(parks!.where((e) => e.name != placeName));
      parks = reArrangedParks;
    } else if (type == GeoPlaceFilterType.trail &&
        trails != null &&
        trails!.isNotEmpty) {
      List<GeoPlace> reArrangedTrails = [];
      reArrangedTrails.add(trails!.firstWhere((e) => e.name == placeName));
      reArrangedTrails.addAll(trails!.where((e) => e.name != placeName));
      trails = reArrangedTrails;
    }
  }

  void addGeofenceCircle(LatLng center, double radiusInMeters) {
    final Circle geofenceCircle = Circle(
      circleId: CircleId('geofence'),
      center: center,
      radius: radiusInMeters, // in meters
      fillColor: Colors.blue.withAlpha(0.2.colorAlpha),
      strokeColor: Colors.blue,
      strokeWidth: 2,
    );

    circles.value = {geofenceCircle}; // or add to existing set
  }

  Future<void> loadOtherPets() async {
    LatLng? bottomLeft = await getLatLngScreenBottomLeft();
    LatLng? topRight = await getLatLngScreenTopRight();

    if (bottomLeft != null && topRight != null) {
      GeoPoint southwest = GeoPoint(
        bottomLeft.latitude,
        bottomLeft.longitude,
      );
      GeoPoint northeast = GeoPoint(
        topRight.latitude,
        topRight.longitude,
      );

      // Geo fence
      // double lat = (southwest.latitude + northeast.latitude) / 2;
      // double lng = (southwest.longitude + northeast.longitude) / 2;
      // LatLng center = LatLng(lat, lng);
      // controller.addGeofenceCircle(center, 2690.0);

      // getNearbyPetsInRectangle
      List<Pet>? pets = await _petService.getNearbyPetsInRectangle(
        uid: authService.userAccount.value!.sid!,
        southwest: southwest,
        northeast: northeast,
      );

      if (pets != null && pets.isNotEmpty) {
        // update markers
        otherPetMarkers.value = pets
            .where((item) => item.latestLocation != null)
            .toList()
            .asMap()
            .entries
            .map((entry) {
          int index = entry.key;
          // GeoPlace place = entry.value;

          return Marker(
            markerId: MarkerId(
                'marker/${entry.value.latestLocation!.latitude},${entry.value.latestLocation!.longitude}'),
            onTap: () {
              // openInfoCard(place, GeoPlaceFilterType.park);
            },
            position: LatLng(entry.value.latestLocation!.latitude,
                entry.value.latestLocation!.longitude),
            icon: petIconInActivated.value!,
          );
        }).toSet();

        reLoadMarkers();
      }
    } else {
      return;
    }
  }

  Future<void> updateMyPets() async {
    await setCurrentLocation();
    LatLng? center = currentLocation.value;

    // Load latest default pet
    Pet? pet = authService.defaultPet.value;
    List<Pet>? myPets =
        await _petService.getOwnedPets(authService.userAccount.value!.sid!);
    Pet? myPet = myPets?.where((p) => p.sid == pet?.sid).firstOrNull;

    // Actions on default pet
    if (myPet != null) {
      // Update my pet location icon
      if (center != null) {
        Marker current = Marker(
          markerId: MarkerId('marker/${center.latitude},${center.longitude}'),
          onTap: () {
            // openInfoCard(place, GeoPlaceFilterType.park);
          },
          position: LatLng(center.latitude, center.longitude),
          icon: petIconActivated.value!,
        );
        (myPetMarkers.value ??= <Marker>{}).add(current);
        // myPetMarkers.refresh();

        reLoadMarkers();
      } else {
        return;
      }

      // Upload current location
      PetGeoRecord record = PetGeoRecord.create(
        uid: authService.userAccount.value!.sid!,
        pid: myPet.sid!,
        location: GeoFirePoint(GeoPoint(center.latitude, center.longitude)),
      );
      await _petService.recordPetGeoLocation(record);
    }
  }

  Future<void> execTask() async {
    if (isTimerRunning.isTrue) return;
    isTimerRunning.value = true;
    try {
      await updateMyPets();
      await loadOtherPets();
    } catch (e) {
      logger.e(e.toString());
    } finally {
      isTimerRunning.value = false;
    }
  }

  Future<void> startTimer() async {
    await execTask();
    timer = Timer.periodic(Duration(seconds: 30), (_) async {
      await execTask();
    });
  }

  void endTimer() {
    timer?.cancel();
  }

  void reLoadMarkers() {
    markers.value = {
      ...searchedMarkers.value?.toSet() ?? {},
      ...parkMarkers.value?.toSet() ?? {},
      ...trailMarkers.value?.toSet() ?? {},
      ...vetMarkers.value?.toSet() ?? {},
      ...petStoreMarkers.value?.toSet() ?? {},
      ...dogFriendlyRestaurantMarkers.value?.toSet() ?? {},
      ...myPetMarkers.value?.toSet() ?? {},
      ...otherPetMarkers.value?.toSet() ?? {},
    };
  }

  // Search functions
  Future<void> onSearchTextCleared(BuildContext context) async {
    await resetState();
  }

  Future<void> isInputSearchTextValid(
      {required BuildContext context, required bool result}) async {
    // isPhoneNumberValid.value = result;
    // usernameInputController.hasMistake.value = isPhoneNumberValid.isFalse && usernameInputBox.text.length > phoneNumberValidLength;
    //
    // if (isPhoneNumberValid.isTrue) {
    //   buttonController.text = 'auth.button.send.code'.t18.obs;
    //   buttonController.onPressed = sendVerificationCodeOfPhoneNumber.obs;
    //   buttonController.isEnabled.value = true;
    // }
    // else {
    //   buttonController.isEnabled.value = false;
    // }
    //
    // await Future.delayed(Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }
  Future<void> clickSearchText() async {
    if (textQueryInputBox.text == '' || textQueryInputBox.text.isEmpty) {
      return;
    }

    LatLng? center = await getLatLngScreenCenter();
    LatLng? low = await getLatLngScreenBottomLeft();
    LatLng? high = await getLatLngScreenTopRight();

    if (center == null) {
      return;
    }

    if (low == null) {
      return;
    }

    if (high == null) {
      return;
    }

    // Load places from google place api
    searchedPoints = await geoLocationService.searchText(
        low: low,
        high: high,
        latlng: center,
        keyword: textQueryInputBox.text,
        zoomLevel: GeoLocationZoomLevel.level_14,
        pageSize: ApiConst.gmApiResponseNumber);
    // trails = await geoLocationService.searchNearBy(
    //     latlng: center,
    //     type: ApiConst.gmApiSearchTypeTrail,
    //     zoomLevel: GeoLocationZoomLevel.level_14,
    //     pageSize: ApiConst.gmApiResponseNumber
    // );
    // update markers
    searchedMarkers.value = searchedPoints
        ?.where((item) =>
            item.location != null &&
            item.location!.longitude != null &&
            item.location!.latitude != null)
        .toList()
        .asMap()
        .entries
        .map((entry) {
      int index = entry.key;
      GeoPlace place = entry.value;

      return Marker(
        markerId: MarkerId('marker/${place.name}'),
        position: LatLng(place.location!.latitude!, place.location!.longitude!),
        infoWindow: InfoWindow(
          title: 'Marker #$index',
          snippet:
              'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
        ),
        icon: commonIcon.value!,
      );
    }).toSet();

    reLoadMarkers();
  }

  Future<void> resetState() async {
    // Text field
    textQueryInputBox.text = '';
    textQueryInputController.isEnabled.value = true;
    textQueryInputController.hasMistake.value = false;
    textQueryInputController.textFieldFocusNode.unfocus();

    // Markers
    searchedMarkers.value = null;

    // Button
    searchButtonController.isEnabled.value = true;
    searchButtonController.isInProgress.value = false;
    await Future.delayed(
        Duration(milliseconds: 200)); // To ensure the keyboard is closed
  }

  Future<void> clearFilteredMarkers() async {
    // Reset flags
    isParkSelected.value = false;
    isTrailSelected.value = false;
    isVetSelected.value = false;
    isPetStoreSelected.value = false;
    isDogFriendlyRestaurantSelected.value = false;

    // Clear markers
    markers.value = null;
    searchedMarkers.value = null;
    parkMarkers.value = null;
    trailMarkers.value = null;
    vetMarkers.value = null;
    petStoreMarkers.value = null;
    dogFriendlyRestaurantMarkers.value = null;
    myPetMarkers.value = null;
    otherPetMarkers.value = null;
  }

  // filter operations (single selection, which means only one type can be selected plus favor)
  Future<void> toggleFilter(GeoPlaceFilterType type) async {
    await resetState();
    if (type == GeoPlaceFilterType.park) {
      if (isParkSelected.isTrue) {
        isParkSelected.value = false;
      } else {
        isParkSelected.value = true;
        isTrailSelected.value = false;
        isVetSelected.value = false;
        isPetStoreSelected.value = false;
        isDogFriendlyRestaurantSelected.value = false;
      }
    } else if (type == GeoPlaceFilterType.trail) {
      if (isTrailSelected.isTrue) {
        isTrailSelected.value = false;
      } else {
        isParkSelected.value = false;
        isTrailSelected.value = true;
        isVetSelected.value = false;
        isPetStoreSelected.value = false;
        isDogFriendlyRestaurantSelected.value = false;
      }
    } else if (type == GeoPlaceFilterType.vet) {
      if (isVetSelected.isTrue) {
        isVetSelected.value = false;
      } else {
        isParkSelected.value = false;
        isTrailSelected.value = false;
        isVetSelected.value = true;
        isPetStoreSelected.value = false;
        isDogFriendlyRestaurantSelected.value = false;
      }
      isOutdoorSubDisplay.value = false;
    } else if (type == GeoPlaceFilterType.petStore) {
      if (isPetStoreSelected.isTrue) {
        isPetStoreSelected.value = false;
      } else {
        isParkSelected.value = false;
        isTrailSelected.value = false;
        isVetSelected.value = false;
        isPetStoreSelected.value = true;
        isDogFriendlyRestaurantSelected.value = false;
      }
      isOutdoorSubDisplay.value = false;
    } else if (type == GeoPlaceFilterType.dogFriendlyRestaurant) {
      if (isDogFriendlyRestaurantSelected.isTrue) {
        isDogFriendlyRestaurantSelected.value = false;
      } else {
        isParkSelected.value = false;
        isTrailSelected.value = false;
        isVetSelected.value = false;
        isPetStoreSelected.value = false;
        isDogFriendlyRestaurantSelected.value = true;
      }
      isOutdoorSubDisplay.value = false;
    } else if (type == GeoPlaceFilterType.favor) {
      isFavorSelected.value = !isFavorSelected.value;
    }
  }

  Future<void> clickFilterButton() async {
    LatLng? center = await getLatLngScreenCenter();
    LatLng? low = await getLatLngScreenBottomLeft();
    LatLng? high = await getLatLngScreenTopRight();

    if (isParkSelected.isTrue) {
      if (center == null) {
        return;
      }

      // Load places from google place api
      parks = await geoLocationService.searchNearBy(
          latlng: center,
          type: ApiConst.gmApiSearchTypePark,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);
      // update markers
      parkMarkers.value = parks
          ?.where((item) => item.location != null &&
                  item.location!.longitude != null &&
                  item.location!.latitude != null &&
                  isFavorSelected.isTrue
              ? (favorParks.value?.any((p) => p.placeName == item.name) ??
                  false)
              : true)
          .toList()
          .asMap()
          .entries
          .map((entry) {
        int index = entry.key;
        GeoPlace place = entry.value;
        print("place reviews: ${place.reviews}");
        return Marker(
          markerId: MarkerId('marker/${place.name}'),
          onTap: () {
            openInfoCard(place, GeoPlaceFilterType.park);
          },
          position:
              LatLng(place.location!.latitude!, place.location!.longitude!),
          // infoWindow: InfoWindow(
          //   title: 'Marker #$index',
          //   snippet: 'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
          // ),
          icon: parkIcon.value!,
        );
      }).toSet();
    } else {
      parkMarkers.value = null;
    }

    if (isTrailSelected.isTrue) {
      if (center == null) {
        return;
      }

      if (low == null) {
        return;
      }

      if (high == null) {
        return;
      }

      // Load places from google place api
      trails = await geoLocationService.searchText(
          low: low,
          high: high,
          latlng: center,
          keyword: ApiConst.gmApiSearchKeywordOffLeashTrails,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);
      // trails = await geoLocationService.searchNearBy(
      //     latlng: center,
      //     type: ApiConst.gmApiSearchTypeTrail,
      //     zoomLevel: GeoLocationZoomLevel.level_14,
      //     pageSize: ApiConst.gmApiResponseNumber
      // );
      // update markers
      trailMarkers.value = trails
          ?.where((item) => item.location != null &&
                  item.location!.longitude != null &&
                  item.location!.latitude != null &&
                  isFavorSelected.isTrue
              ? (favorTrails.value?.any((p) => p.placeName == item.name) ??
                  false)
              : true)
          .toList()
          .asMap()
          .entries
          .map((entry) {
        int index = entry.key;
        GeoPlace place = entry.value;

        return Marker(
          markerId: MarkerId('marker/${place.name}'),
          position:
              LatLng(place.location!.latitude!, place.location!.longitude!),
          infoWindow: InfoWindow(
            title: 'Marker #$index',
            snippet:
                'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
          ),
          icon: trailIcon.value!,
        );
      }).toSet();
    } else {
      trailMarkers.value = null;
    }

    if (isVetSelected.isTrue) {
      if (center == null) {
        return;
      }

      // Load places from google place api
      vets = await geoLocationService.searchNearBy(
          latlng: center,
          type: ApiConst.gmApiSearchTypeVet,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);
      // update markers
      vetMarkers.value = vets
          ?.where((item) => item.location != null &&
                  item.location!.longitude != null &&
                  item.location!.latitude != null &&
                  isFavorSelected.isTrue
              ? (favorVets.value?.any((p) => p.placeName == item.name) ?? false)
              : true)
          .toList()
          .asMap()
          .entries
          .map((entry) {
        int index = entry.key;
        GeoPlace place = entry.value;

        return Marker(
          markerId: MarkerId('marker/${place.name}'),
          position:
              LatLng(place.location!.latitude!, place.location!.longitude!),
          infoWindow: InfoWindow(
            title: 'Marker #$index',
            snippet:
                'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
          ),
          icon: vetIcon.value!,
        );
      }).toSet();
    } else {
      vetMarkers.value = null;
    }

    if (isPetStoreSelected.isTrue) {
      if (center == null) {
        return;
      }

      // Load places from google place api
      petStores = await geoLocationService.searchNearBy(
          latlng: center,
          type: ApiConst.gmApiSearchTypePetStore,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);
      // update markers
      petStoreMarkers.value = petStores
          ?.where((item) => item.location != null &&
                  item.location!.longitude != null &&
                  item.location!.latitude != null &&
                  isFavorSelected.isTrue
              ? (favorPetStores.value?.any((p) => p.placeName == item.name) ??
                  false)
              : true)
          .toList()
          .asMap()
          .entries
          .map((entry) {
        int index = entry.key;
        GeoPlace place = entry.value;

        return Marker(
          markerId: MarkerId('marker/${place.name}'),
          position:
              LatLng(place.location!.latitude!, place.location!.longitude!),
          infoWindow: InfoWindow(
            title: 'Marker #$index',
            snippet:
                'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
          ),
          icon: petStoreIcon.value!,
        );
      }).toSet();
    } else {
      petStoreMarkers.value = null;
    }

    if (isDogFriendlyRestaurantSelected.isTrue) {
      if (center == null) {
        return;
      }

      if (low == null) {
        return;
      }

      if (high == null) {
        return;
      }

      // Query dog cafe via search nearby
      List<GeoPlace>? dogCafes = await geoLocationService.searchNearBy(
          latlng: center,
          type: ApiConst.gmApiSearchTypeDogCafe,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);

      // Query dog friendly restaurants via search text
      List<GeoPlace>? dogRestaurants = await geoLocationService.searchText(
          low: low,
          high: high,
          keyword: ApiConst.gmApiSearchKeywordDogFriendlyRestaurants,
          zoomLevel: GeoLocationZoomLevel.level_14,
          pageSize: ApiConst.gmApiResponseNumber);

      // Merge them into dog friendly restaurants
      List<GeoPlace> places = [];
      if (dogCafes != null) {
        places.addAll(dogCafes);
      }
      if (dogRestaurants != null) {
        places.addAll(dogRestaurants);
      }
      if (places.isNotEmpty) {
        Map<String, GeoPlace> merged = {
          for (var place in [...places]) place.name!: place
        };
        dogFriendlyRestaurants = merged.values.toList();
      }

      // update markers
      dogFriendlyRestaurantMarkers.value = dogFriendlyRestaurants
          ?.where((item) => item.location != null &&
                  item.location!.longitude != null &&
                  item.location!.latitude != null &&
                  isFavorSelected.isTrue
              ? (favorDogFriendlyRestaurants.value
                      ?.any((p) => p.placeName == item.name) ??
                  false)
              : true)
          .toList()
          .asMap()
          .entries
          .map((entry) {
        int index = entry.key;
        GeoPlace place = entry.value;

        return Marker(
          markerId: MarkerId('marker/${place.name}'),
          position:
              LatLng(place.location!.latitude!, place.location!.longitude!),
          infoWindow: InfoWindow(
            title: 'Marker #$index',
            snippet:
                'Lat: ${place.location!.latitude}, Lng: ${place.location!.longitude}',
          ),
          icon: dogFriendlyRestaurantIcon.value!,
        );
      }).toSet();
    } else {
      dogFriendlyRestaurantMarkers.value = null;
    }

    reLoadMarkers();
  }

  Future<void> clickFilterOutdoor() async {
    // todo
    await toggleOutdoorSub();
  }

  Future<void> toggleOutdoorSub() async {
    isOutdoorSubDisplay.value = !isOutdoorSubDisplay.value;
  }

  Future<void> clickFilterPark() async {
    await toggleFilter(GeoPlaceFilterType.park);
    await clickFilterButton();
  }

  Future<void> clickFilterTrail() async {
    await toggleFilter(GeoPlaceFilterType.trail);
    await clickFilterButton();
  }

  Future<void> clickFilterVet() async {
    await toggleFilter(GeoPlaceFilterType.vet);
    await clickFilterButton();
  }

  Future<void> clickFilterPetStore() async {
    await toggleFilter(GeoPlaceFilterType.petStore);
    await clickFilterButton();
  }

  Future<void> clickFilterDogFriendlyRestaurant() async {
    await toggleFilter(GeoPlaceFilterType.dogFriendlyRestaurant);
    await clickFilterButton();
  }

  Future<void> clickFilterFavor() async {
    await toggleFilter(GeoPlaceFilterType.favor);
    await clickFilterButton();
  }

  // favorites operations
  Future<void> toggleFavorites(GeoPlace place, GeoPlaceFilterType type) async {
    // Query existing favorite place
    List<GeoFavorPlace>? existingFavorites =
        await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: type,
    );
    GeoFavorPlace? exist =
        existingFavorites?.firstWhereOrNull((p) => p.placeName == place.name!);

    // if exist, remove it
    if (exist != null) {
      // remove from firestore
      await geoService.deleteGeoFavorPlace(exist.sid!);
      // remove from local
      if (type == GeoPlaceFilterType.park) {
        favorParks.value?.removeWhere((p) => p.sid == exist.sid);
      } else if (type == GeoPlaceFilterType.trail) {
        favorTrails.value?.removeWhere((p) => p.sid == exist.sid);
      } else if (type == GeoPlaceFilterType.vet) {
        favorVets.value?.removeWhere((p) => p.sid == exist.sid);
      } else if (type == GeoPlaceFilterType.petStore) {
        favorPetStores.value?.removeWhere((p) => p.sid == exist.sid);
      } else if (type == GeoPlaceFilterType.dogFriendlyRestaurant) {
        favorDogFriendlyRestaurants.value
            ?.removeWhere((p) => p.sid == exist.sid);
      }
      return;
    }
    // if not exist, add it
    else {
      GeoFavorPlace gfp = GeoFavorPlace.create(
        uid: authService.userAccount.value!.sid!,
        placeName: place.name!,
        filterType: type,
      );
      // add to firestore
      await geoService.addGeoFavorPlace(gfp);
      // add to local
      if (type == GeoPlaceFilterType.park) {
        favorParks.value?.add(gfp);
      } else if (type == GeoPlaceFilterType.trail) {
        favorTrails.value?.add(gfp);
      } else if (type == GeoPlaceFilterType.vet) {
        favorVets.value?.add(gfp);
      } else if (type == GeoPlaceFilterType.petStore) {
        favorPetStores.value?.add(gfp);
      } else if (type == GeoPlaceFilterType.dogFriendlyRestaurant) {
        favorDogFriendlyRestaurants.value?.add(gfp);
      }
    }
  }

  Future<void> loadFavorPlaces() async {
    // Load favorite parks
    if (isParkSelected.isTrue) await loadFavorParks();
    // Load favorite trails
    if (isTrailSelected.isTrue) await loadFavorTrails();
    // Load favorite vets
    if (isVetSelected.isTrue) await loadFavorVets();
    // Load favorite petStores
    if (isPetStoreSelected.isTrue) await loadFavorPetStore();
    // Load favorite dog friendly restaurants
    if (isDogFriendlyRestaurantSelected.isTrue)
      await loadFavorDogFriendlyRestaurant();
  }

  Future<void> loadFavorParks() async {
    favorParks.value = await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: GeoPlaceFilterType.park,
    );
  }

  Future<void> loadFavorTrails() async {
    favorTrails.value = await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: GeoPlaceFilterType.trail,
    );
  }

  Future<void> loadFavorVets() async {
    favorTrails.value = await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: GeoPlaceFilterType.vet,
    );
  }

  Future<void> loadFavorPetStore() async {
    favorTrails.value = await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: GeoPlaceFilterType.petStore,
    );
  }

  Future<void> loadFavorDogFriendlyRestaurant() async {
    favorTrails.value = await geoService.queryGeoFavorPlaceList(
      uid: authService.userAccount.value!.sid!,
      type: GeoPlaceFilterType.dogFriendlyRestaurant,
    );
  }
}

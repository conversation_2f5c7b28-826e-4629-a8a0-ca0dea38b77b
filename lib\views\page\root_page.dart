import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/components_i.dart';
import 'home/home_pages_i.dart';

class RootPage extends StatefulWidget {
  final PageNavigation? page;
  const RootPage({super.key, this.page = PageNavigation.home});

  @override
  RootPageState createState() => RootPageState();
}

class RootPageState extends State<RootPage> {

  // Declare your controller here
  late Future<RootPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => RootPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {

    return FutureBuilder<RootPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Indicator for loading
          return const Center(
            child: LoadingWidget(),
          );
        } else if (snapshot.hasError) {
          // Error handling
          return Center(child: Text('Error: ${snapshot.error}'));
        } else {
          // Page functions
          final controller = snapshot.data!;

          // Navigate to target page
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (widget.page != null) {
              controller.pageNavigationController.navigateToPage(widget.page!);
            }
          });

          return CommonPage(
            theme: controller.theme,
            title: null,
            body: PageView.builder(
              controller: controller.pageNavigationController.pageController,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: PageNavigation.values.length,
              itemBuilder: (context, index) =>
                  controller.pageNavigationController.getPage(PageNavigation.values[index]),
              onPageChanged: (index) {
                controller.pageNavigationController.pageIndex.value = index;
                if (index != PageNavigation.home.key) {
                  // force destroy MapPageController to ensure onClose() was executed and timer cancelled
                  Get.delete<MapPageController>();
                }
              }
            ),
            bottomBar: PageNavigationBar(
              controller: controller.pageNavigationController,
              width: screenSize.width,
              height: controller.theme.layout.bottomNaviBarHeight,
              horizontalPadding: 8.w,
              verticalPadding: 8.w,
              color: controller.theme.themeData.colorScheme.surface,
              alignment: Alignment.center,
              widget: Obx(() => Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: controller.pageNavigationController.buttonItems.value ?? [],
              )),
            ),
          );
        }
      }
    );
  }
}

class RootPageController extends GetxController {

  // Theme plugin
  late ThemePlugin theme;

  // Plugins
  late PageNavigationController pageNavigationController = Get.put(PageNavigationController());

  Future<RootPageController> init() async {

    // Initialize theme plugin
    theme = await Get.putAsync(() => ThemePlugin().init());

    pageNavigationController.buttonItems.value =
      List.generate( PageNavigation.values.length, (i) {
        return bottomNavItem(PageNavigation.fromKey(i));
      }
    );

    return this;
  }
  
  /// Bottom navigation item
  Widget bottomNavItem(PageNavigation page) {

    // Bg box
    Widget bgBox = CommonBar(
      width: theme.layout.bottomNaviItemBoxWidth,
      height: theme.layout.bottomNaviItemBoxHeight,
      color: theme.themeData.colorScheme.surface.withAlpha(0),
      hasShadow: false,
    );

    // Icon
    Widget icon = Obx(()=> CommonImageAsset(
      path: page.assetPath,
      width: theme.layout.bottomNaviItemIconSize,
      height: theme.layout.bottomNaviItemIconSize,
      color: page.index == pageNavigationController.pageIndex.value
          ? theme.themeData.colorScheme.primary
          : theme.themeData.colorScheme.tertiary
    ));

    // Text
    Widget text = Obx(()=> CommonText(
      page.t18key.t18,
      page.index == pageNavigationController.pageIndex.value
        ? theme.textStyleExtension.bottomItemSelected
        : theme.textStyleExtension.bottomItem,
    ));

    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () async {
          pageNavigationController.navigateToPage(page);
        },
        child: Stack(
          alignment: Alignment.center,
          children: [
            bgBox,
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                icon,
                SizedBox(height: theme.layout.bottomNaviItemIconSpaceAfter),
                text,
              ],
            ),
          ],
        )
      ),
    );
  }
}

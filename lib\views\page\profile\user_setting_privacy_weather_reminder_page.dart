import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingWeatherReminderPage extends StatefulWidget {
  const UserSettingWeatherReminderPage({super.key});

  @override
  State<UserSettingWeatherReminderPage> createState() => _UserSettingWeatherReminderPageState();
}

class _UserSettingWeatherReminderPageState extends State<UserSettingWeatherReminderPage> {
  late Future<UserSettingWeatherReminderPageController> _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.putAsync(() => UserSettingWeatherReminderPageController().init());
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingWeatherReminderPageController>(
      future: _controller,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final controller = snapshot.data!;
        return CommonPage(
          theme: controller.theme,
          backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ProfileWidgetBuilder.buildUserSettingTopSection(
                theme: controller.theme,
                onBack: () => Get.back(),
                topic: 'Weather Reminders',
              ),
              _buildMainSwitch(controller),
              Obx(() {
                if (!controller.weatherReminderEnabled.value) return const SizedBox.shrink();
                return Column(
                  children: _buildSubSwitches(controller),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMainSwitch(UserSettingWeatherReminderPageController controller) {
    return Obx(() => SwitchListTile(
      title: Text("Turn on Weather Reminders", style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text("Stay updated with local weather conditions.",
          style: controller.theme.textStyleExtension.petAvatar),
      value: controller.weatherReminderEnabled.value,
      onChanged: (value) => controller.weatherReminderEnabled.value = value,
    ));
  }


  List<Widget> _buildSubSwitches(UserSettingWeatherReminderPageController controller) {
    return [
      _buildSubSwitch(controller, "Get Severe Weather Alerts", "Be warned right away when dangerous weather is on the way.",
          controller.severeAlertEnabled),
      _buildSubSwitch(controller, "Get Good Weather Tips", "We’ll let you know when the weather’s great for going out.",
          controller.goodTipsEnabled),
      _buildSubSwitch(controller, "Use My Location", "We’ll use your city to give accurate local weather. Never tracked or shared.",
          controller.useLocationEnabled),
      _buildSubSwitch(controller, "Smart Reminder Timing", "We’ll time reminders around your usual walking or travel patterns.",
          controller.smartTimingEnabled),
    ];
  }

  Widget _buildSubSwitch(UserSettingWeatherReminderPageController controller, String title, String subtitle, RxBool toggleValue) {
    return Obx(() => SwitchListTile(
      title: Text(title, style: controller.theme.textStyleExtension.userProfileSettingBody),
      subtitle: Text(subtitle, style: controller.theme.textStyleExtension.petAvatar),
      value: toggleValue.value,
      onChanged: (value) => toggleValue.value = value,
    ));
  }

}

class UserSettingWeatherReminderPageController extends GetxController {
  final AuthService _authService = AuthService.instance;

  var pageHeaderUserAvatar = Rx<Widget?>(null);
  var pageHeaderUserName = Rx<Widget?>(null);
  late ThemePlugin theme;

  // 🔧 天气提醒开关主控 + 子选项
  final RxBool weatherReminderEnabled = false.obs;
  final RxBool severeAlertEnabled = false.obs;
  final RxBool goodTipsEnabled = false.obs;
  final RxBool useLocationEnabled = false.obs;
  final RxBool smartTimingEnabled = false.obs;

  Future<UserSettingWeatherReminderPageController> init() async {
    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());

    UserData? userData = _authService.userData.value;
    pageHeaderUserAvatar.value = await AuthWidgetBuilder.buildUserAvatar(
      theme,
      userId: _authService.userAccount.value!.sid!,
      avatar: userData?.avatar,
    );

    return this;
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/page/auth/auth_pages_i.dart';
import 'package:onenata_app/views/page/community/community_pages_i.dart';
import 'package:onenata_app/views/page/home/<USER>';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/page/record/record_pages_i.dart';

import '../page/report/report_page.dart';

class PageNavigationBar extends StatelessWidget {
  final PageNavigationController controller;
  final Alignment? alignment;
  final double? width;
  final double? height;
  final double? horizontalPadding;
  final double? verticalPadding;
  final Color? color;
  final bool? hasShadow;
  final Color? shadowColor;
  final Offset? shadowOffset;
  final double? shadowBlurRadius;
  final double? shadowSpreadRadius;
  final Widget? widget;

  const PageNavigationBar({
    super.key,
    required this.controller,
    this.alignment,
    this.width = 403,
    this.height = 59,
    this.horizontalPadding = 8,
    this.verticalPadding = 8,
    this.color = Colors.white,
    this.hasShadow = false,
    this.shadowColor = Colors.black12,
    this.shadowOffset,
    this.shadowBlurRadius,
    this.shadowSpreadRadius,
    this.widget,
  });

  @override
  Widget build(BuildContext context) {
    // Bg bar
    return Container(
      alignment: alignment ?? Alignment.center,
      width: width,
      height: height,
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding!, vertical: verticalPadding!),
      decoration: BoxDecoration(
        color: color,
        boxShadow: hasShadow!
            ? [
                BoxShadow(
                  color: shadowColor!,
                  offset: shadowOffset ?? Offset(0, 2.w), // 阴影的偏移量
                  blurRadius: shadowBlurRadius ?? 5.w, // 阴影的模糊半径
                  spreadRadius: shadowSpreadRadius ?? 0, // 阴影的扩散半径
                ),
              ]
            : [],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: controller.buttonItems.value ?? [SizedBox.shrink()],
      ),
    );
  }
}

class PageNavigationController extends GetxController {
  var args = {}.obs;
  final pageIndex = 0.obs;
  final pageController = PageController();
  var buttonItems = Rx<List<Widget>?>(null);

  Widget get currentPage => getPage(PageNavigation.fromKey(pageIndex.value));
  String get currentPageIcon =>
      PageNavigation.fromKey(pageIndex.value).assetPath;
  String get currentPageName =>
      PageNavigation.fromKey(pageIndex.value).t18key.t18;

  void navigateToPage(
    PageNavigation page, {
    Duration duration = const Duration(milliseconds: 100),
    Curve curve = Curves.easeInOut,
    Map<String, dynamic>? arguments,
  }) {
    if (pageIndex.value == page.index) return;

    // Set arguments if provided
    if (arguments != null) {
      args.value = arguments;
    }

    if ((pageIndex.value - page.index).abs() == 1) {
      pageController.animateToPage(
        page.index,
        duration: duration,
        curve: curve,
      );
    } else {
      pageController.jumpToPage(page.index);
    }

    // Set page index
    pageIndex.value = page.index;
  }

  Widget getPage(PageNavigation page) {
    if (page == PageNavigation.wellness) return const RecordPage();
    if (page == PageNavigation.shop) return const ReportPage();
    if (page == PageNavigation.home) return const MapPage();
    if (page == PageNavigation.community) return const CommunityPage();
    if (page == PageNavigation.profile) return const UserProfilePage();
    return const ResetPasswordSuccessPage();
  }
}

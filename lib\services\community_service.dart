import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';

import 'auth_service.dart';
import 'service_response.dart';

class CommunityService {

  // Daos
  final CommunityDao _communityDao = CommunityDao();
  final UserDao _userDao = UserDao();

  // Services
  final AuthService _authService = AuthService.instance;

  // Local storage
  final CloudStorage _cloudStorage = CloudStorage();
  final LocalStorage _localStorage = LocalStorage.instance;
  final SecureStorage _secureStorage = SecureStorage.instance;
  final Storage _storage = Storage.instance;

  // Operations
  Future<List<Post>?> loadPosts ({PostType? type, String? uid, List<String>? pets, List<String>? keywords, bool? isValid = true, int? createDateFrom, int? createDateTo, int? pageSize = 20, int? lastCreateDate}) async {

    // Query preps
    bool isEnd = false;
    List<Post>? rl = [];
    int lastCreateDate = DateTimeUtil.currentMilliseconds() + 3600000;


    // Iteration
    while (rl.length < pageSize! && isEnd == false) {

      // Query one page without filter
      List<Post>? oPosts = await _communityDao.getPosts(
          type: type,
          uid: uid,
          pets: pets,
          keywords: keywords,
          isValid: isValid,
          createDateFrom: createDateFrom,
          createDateTo: createDateTo,
          pageSize: pageSize,
          lastCreateDate: lastCreateDate
      );

      if (oPosts == null || (oPosts.isNotEmpty && oPosts.length < pageSize)) {
        isEnd = true;
      }

      // Filter by visibility and expire date
      // if (oPosts != null && oPosts.isNotEmpty) {
      //   final authorUids = oPosts.map((e) => e.uid).toSet().toList();
      //   List<UserData>? udList = await _userDao.getUserDataList(authorUids);
      //   if (udList != null && udList.isNotEmpty) {
      //     List<Post>
      //   }
      // }

      // final userSettingsSnapshots = await Future.wait(
      //     authorUids.map((uid) => FirebaseFirestore.instance
      //         .collection('users')
      //         .doc(uid)
      //         .get())
      // );

      // final Map<String, int> visibilityPeriodMap = {
      //   for (var snap in userSettingsSnapshots)
      //     snap.id: snap.data()?['post_visible_period'] ?? 7 // 默认7天
      // };
    }
  }

}

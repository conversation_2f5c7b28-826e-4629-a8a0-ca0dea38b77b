import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';

class ToastUtil {

  // Show text toast
  static void showToast(String? msg, {Duration? duration}) {
    if (msg != null && msg.isNotEmpty) {

      BotToast.showText(
        text: msg,
        align: const Alignment(0, 0.4),
        animationDuration: Duration.zero,
        animationReverseDuration: Duration.zero,
        duration: duration ?? const Duration(seconds: 3),
        backgroundColor: Colors.white70,
        // textStyle: const TextStyle(fontSize: 14, color: Colors.red),
      );
    }
  }
}

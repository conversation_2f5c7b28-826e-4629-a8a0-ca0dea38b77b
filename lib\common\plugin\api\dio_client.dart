import 'dart:async';

import 'package:dio/dio.dart' as d;
import 'package:get/get.dart';

import 'package:onenata_app/models/vo/vo_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';

class DioClient extends GetxService{
  static DioClient get instance => Get.find();

  final d.Dio _dio = d.Dio();

  @override
  void onInit() {
    super.onInit();
    _dio.interceptors.add(ApiInterceptor());
  }

  // API methods --------------------------------------------------------------

  Future<ApiResponseData> gmGet(String path, {Map<String, dynamic>? params, d.Options? options, Map<String, dynamic>? data}) async {

    _determineOptions(RemoteType.googleMap);

    try {
      d.Response r = await _dio.get(path, queryParameters: params, options: options, data: data);
      return ApiResponseData.fromJson(r.data);
    }
    catch (e) {
      return buildExceptionResponse(e);
    }
  }

  Future<ApiResponseData> gmPost(String path, {Map<String, dynamic>? params, d.Options? options, Map<String, dynamic>? data}) async {

    _determineOptions(RemoteType.googleMap);

    try {
      d.Response r = await _dio.post(path, queryParameters: params, options: options, data: data);
      return ApiResponseData.create(
        code: r.statusCode,
        msg: r.statusMessage,
        data: r.data,
      );
    }
    catch (e) {
      return buildExceptionResponse(e);
    }
  }

  /// Determine the options for the dio instance based on the type
  /// URI is needed for the case of S3 Pre-Sign URL
  void _determineOptions(RemoteType type) {

    // Reset to default options
    _dio.options = d.BaseOptions();

    // Set options based on the type
    switch (type) {
      case RemoteType.googleMap:
        _dio.options.baseUrl = ApiConst.gmApiBaseUrl;
        _dio.options.contentType = ApiConst.gmApiContentType;
        _dio.options.connectTimeout = const Duration(seconds: ApiConst.gmApiConnectTimeout);
        _dio.options.receiveTimeout = const Duration(seconds: ApiConst.gmApiConnectTimeout);
        break;
    }
  }

  ApiResponseData buildExceptionResponse(dynamic e) {

    if (e is d.DioException) {

      final error = e.error;
      if (error != null) {

        if (error is d.DioException) {
          if (error.type == d.DioExceptionType.connectionTimeout) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.timeout'.t18);
            return ApiResponseData.exTimeout();
          }
          if (error.type == d.DioExceptionType.receiveTimeout) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.timeout'.t18);
            return ApiResponseData.exTimeout();
          }
          if (error.type == d.DioExceptionType.sendTimeout) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.timeout'.t18);
            return ApiResponseData.exTimeout();
          }
          if (error.type == d.DioExceptionType.connectionError) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.connection'.t18);
            return ApiResponseData.exConnectionError();
          }
          if (error.type == d.DioExceptionType.cancel) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.unknown'.t18);
            return ApiResponseData.exUnknown();
          }
          if (error.type == d.DioExceptionType.badCertificate) {
            Get.snackbar('api.exception.network.issue.title'.t18, 'api.exception.network.issue.cert'.t18);
            return ApiResponseData.exBadCert();
          }
        }

        if (error is NotSuccessException) {
          return error.data!;
        }

        if (error is ClientException) {
          return error.data!;
        }
      }
    }

    return ApiResponseData.exUnknown();
  }
}

enum RemoteType {
  googleMap('GMS'),
  ;

  const RemoteType(this.code);
  final String code;
}

enum RequestType {
  get('GET'),
  post('POST'),
  put('PUT'),
  delete('DELETE'),
  ;

  const RequestType(this.code);
  final String code;
}

class ApiInterceptor extends d.InterceptorsWrapper {

  @override
  onResponse(d.Response response, d.ResponseInterceptorHandler handler) {

    handler.next(response); // use handler.resolve for mock or cache
  }

  @override
  onError(d.DioException err, d.ErrorInterceptorHandler handler) {

    final response = err.response;
    if (response != null) {
      ApiResponseData respData = ApiResponseData.create(
        code: response.statusCode!,
        msg: response.statusMessage ?? 'api.exception.client.error'.t18,
        data: response.data,
      );

      throw NotSuccessException.fromRespData(respData);
    }
  }
}

class ApiException implements Exception {
  ApiResponseData? data;
  ApiException.fromRespData(ApiResponseData respData) {
    data = respData;
  }
}

// Response status code >= 300
class NotSuccessException extends ApiException {
  NotSuccessException.fromRespData(super.data) : super.fromRespData();

  @override
  String toString() {
    return 'NotSuccessException{msg: ${data!.msg}}';
  }
}

// Client exception (app side)
class ClientException extends ApiException {
  ClientException.fromRespData(super.data) : super.fromRespData();

  @override
  String toString() {
    return 'ClientException{msg: ${data!.msg}}';
  }
}

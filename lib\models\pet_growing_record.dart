import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';

part 'pet_growing_record.g.dart';

@JsonSerializable()
class PetGrowingRecord extends BaseFullModel {

  String uid;
  String pid;
  double? weight;

  PetGrowingRecord({
    required this.uid,
    required this.pid,
    this.weight,
    super.id,
    required super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory PetGrowingRecord.fromJson(Map<String, dynamic> json) => _$PetGrowingRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PetGrowingRecordToJson(this);

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return jsonData;
  }

  factory PetGrowingRecord.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return PetGrowingRecord.fromJson(jsonData);
  }

  factory PetGrowingRecord.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return PetGrowingRecord.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'pet_feeding_record';

  static create ({
    required String uid,
    required String pid,
    double? weight,
  }) {

    return PetGrowingRecord(
      sid: uuid.v4(),
      uid: uid,
      pid: pid,
      weight: weight,
      isValid: true,
    );
  }

  static copyFrom(PetGrowingRecord other) {
    return PetGrowingRecord(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      pid: other.pid,
      weight: other.weight,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

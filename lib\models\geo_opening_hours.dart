import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'geo_i.dart';

part 'geo_opening_hours.g.dart';

/// Refer to google map place API (new)
/// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places

@JsonSerializable()
class GeoOpeningHours {

  List<GeoPeriod>? periods;
  List<String>? weekdayDescriptions;
  SecondaryHoursType? secondaryHoursType;
  List<GeoSpecialDay>? specialDays;
  String? nextOpenTime;
  String? nextCloseTime;
  bool? openNow;

  GeoOpeningHours({
    this.periods,
    this.weekdayDescriptions,
    this.secondaryHoursType,
    this.specialDays,
    this.nextOpenTime,
    this.nextCloseTime,
    this.openNow,
  });

  factory GeoOpeningHours.fromJson(Map<String, dynamic> json) => _$GeoOpeningHoursFromJson(json);
  Map<String, dynamic> toJson() => _$GeoOpeningHoursToJson(this);

  static Map<String, dynamic> fromApi(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = {};
    apiJson.forEach((key, value) {
      if (key == 'periods') {
        jsonData[key] = (apiJson['periods']?? []).map((e) => GeoPeriod.fromApi(e)).toList();
      } else if (key == 'specialDays') {
        jsonData[key] = (apiJson['specialDays']?? []).map((e) => GeoSpecialDay.fromApi(e)).toList();
      } else {
        jsonData[key] = value;
      }
    });

    return jsonData;
  }

  factory GeoOpeningHours.fromApiMap(Map<String, dynamic> apiJson) {

    final Map<String, dynamic> jsonData = fromApi(apiJson);
    return GeoOpeningHours.fromJson(jsonData);
  }

  static Map<String, dynamic> fromFirestore(Map<String, dynamic> map) {

    final Map<String, dynamic> jsonData = {};
    map.forEach((key, value) {
      if (key == 'periods') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = (map['periods']?? []).map((e) => GeoPeriod.fromFirestore(e)).toList();
      } else if (key == 'special_days') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = (map['special_days']?? []).map((e) => GeoSpecialDay.fromFirestore(e)).toList();
      } else if (key == 'openNow') {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = JsonUtil.boolFromJson(value);
      } else {
        jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
      }
    });

    return jsonData;
  }

  factory GeoOpeningHours.fromFirestoreMap(Map<String, dynamic> map) {
    final Map<String, dynamic> jsonData = fromFirestore(map);
    return GeoOpeningHours.fromJson(jsonData);
  }

  factory GeoOpeningHours.fromFirestoreData(DocumentSnapshot doc) {
    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;
    return GeoOpeningHours.fromFirestoreMap(dbData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      if (key == 'periods') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = (dbData['periods']?? []).map((e) => GeoPeriod.toFirestoreJson(e)).toList();
      } else if (key == 'specialDays') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = (dbData['specialDays']?? []).map((e) => GeoSpecialDay.toFirestoreJson(e)).toList();
      } else if (key == 'openNow') {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = JsonUtil.boolToJson(value);
      } else {
        dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
      }
    });

    return dbData;
  }

  static Map<String, dynamic> toFirestoreJson(GeoOpeningHours? o) {
    return o?.toFirestoreData() ?? {};
  }

  static create ({
    List<GeoPeriod>? periods,
    List<String>? weekdayDescriptions,
    SecondaryHoursType? secondaryHoursType,
    List<GeoSpecialDay>? specialDays,
    String? nextOpenTime,
    String? nextCloseTime,
    bool? openNow,
  }) {

    return GeoOpeningHours(
      periods: periods,
      weekdayDescriptions: weekdayDescriptions,
      secondaryHoursType: secondaryHoursType,
      specialDays: specialDays,
      nextOpenTime: nextOpenTime,
      nextCloseTime: nextCloseTime,
      openNow: openNow,
    );
  }

  static GeoOpeningHours copyFrom(GeoOpeningHours? other) {

    return GeoOpeningHours(
      periods: other?.periods?.map((e)=>GeoPeriod.copyFrom(e)).toList(),
      weekdayDescriptions: other?.weekdayDescriptions,
      secondaryHoursType: other?.secondaryHoursType,
      specialDays: other?.specialDays?.map((e)=>GeoSpecialDay.copyFrom(e)).toList(),
      nextOpenTime: other?.nextOpenTime,
      nextCloseTime: other?.nextCloseTime,
      openNow: other?.openNow,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

@JsonEnum(valueField: 'code')
enum SecondaryHoursType {
  unspecified('SECONDARY_HOURS_TYPE_UNSPECIFIED', 'geo.secondary.hours.type.unspecified'),
  driveThrough('DRIVE_THROUGH', 'geo.secondary.hours.type.drive.through'),
  happyHour('HAPPY_HOUR', 'geo.secondary.hours.type.happy.hour'),
  delivery('DELIVERY', 'geo.secondary.hours.type.delivery'),
  takeout('TAKEOUT', 'geo.secondary.hours.type.takeout'),
  kitchen('KITCHEN', 'geo.secondary.hours.type.kitchen'),
  breakfast('BREAKFAST', 'geo.secondary.hours.type.breakfast'),
  lunch('LUNCH', 'geo.secondary.hours.type.lunch'),
  dinner('DINNER', 'geo.secondary.hours.type.dinner'),
  brunch('BRUNCH', 'geo.secondary.hours.type.brunch'),
  pickup('PICKUP', 'geo.secondary.hours.type.pickup'),
  access('ACCESS', 'geo.secondary.hours.type.access'),
  seniorHours('SENIOR_HOURS', 'geo.secondary.hours.type.senior.hours'),
  onlineServiceHours('ONLINE_SERVICE_HOURS', 'geo.secondary.hours.type.online.service.hours'),
  ;

  final String code;
  final String t18key;
  const SecondaryHoursType(this.code, this.t18key);

  // Factory constructor to create a TestType object based on the code
  factory SecondaryHoursType.fromCode(String code) {
    return SecondaryHoursType.values.firstWhere((element) => element.code == code, orElse: () => throw ArgumentError('Invalid code: $code'));
  }
}

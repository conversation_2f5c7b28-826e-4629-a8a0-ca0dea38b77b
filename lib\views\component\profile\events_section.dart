import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class EventsSection extends StatelessWidget {
  final List<Map<String, dynamic>> events = [
    {
      "eventsId": "1",
      "name": "Morning walk with <PERSON>",
      "coverImageUrl":
      "https://images.pexels.com/photos/4587993/pexels-photo-4587993.jpeg",
      "startTime": DateTime(2025, 3, 9, 9, 30).millisecondsSinceEpoch ~/ 1000,
      "tags": ["walking"], // 事件类型
    },
    {
      "eventsId": "2",
      "name": "Dog party @ Marine Dr. Park",
      "coverImageUrl":
      "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "startTime": DateTime(2025, 3, 12, 14, 30).millisecondsSinceEpoch ~/ 1000,
      "tags": ["playing"], // 事件类型
    },
    {
      "eventsId": "3",
      "name": "City walk with friends",
      "coverImageUrl":
      "https://images.pexels.com/photos/256686/pexels-photo-256686.jpeg",
      "startTime": DateTime(2025, 4, 5, 16, 0).millisecondsSinceEpoch ~/ 1000,
      "tags": ["walking"], // 事件类型
    },
    {
      "eventsId": "4",
      "name": "Dog meetup @ Central Park",
      "coverImageUrl":
      "https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg",
      "startTime": DateTime(2025, 5, 1, 10, 30).millisecondsSinceEpoch ~/ 1000,
      "tags": ["playing"], // 事件类型
    },
  ];

  @override
  Widget build(BuildContext context) {
    double w = MediaQuery.of(context).size.width / 403;
    double h = MediaQuery.of(context).size.height / 822;

    return Padding(
      padding: EdgeInsets.only(top: 8 * h),
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 24 * w),
        itemCount: events.length,
        itemBuilder: (context, index) {
          var event = events[index];
          return _buildEventItem(event, w, h);
        },
      ),
    );
  }

  Widget _buildEventItem(Map<String, dynamic> event, double w, double h) {
    DateTime eventTime =
    DateTime.fromMillisecondsSinceEpoch(event["startTime"] * 1000);
    String formattedDate = DateFormat("E dd MMM yyyy").format(eventTime);
    String formattedTime = DateFormat("HH:mm").format(eventTime);

    // **✅ 选择 `icon` 和 `边框颜色`**
    IconData eventIcon;
    Color borderColor;
    Color iconColor;

    if (event["tags"].contains("walking")) {
      eventIcon = Icons.directions_walk;
      borderColor = const Color(0xFF82C43C); // 绿色边框
      iconColor = const Color(0xFF82C43C); // 绿色图标
    } else {
      eventIcon = Icons.sports_baseball;
      borderColor = const Color(0xFF3897F0); // 蓝色边框
      iconColor = const Color(0xFF3897F0); // 蓝色图标
    }

    return Container(
      //width: 355 * w,
      //height: 84 * h,
      margin: EdgeInsets.only(bottom: 16 * h),
      padding: EdgeInsets.all(16 * w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(14),
        border: Border(
          bottom: BorderSide(width: 1.5 * w, color: borderColor),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF323247).withOpacity(0.04),
            blurRadius: 20,
            spreadRadius: -2,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: const Color(0xFF0C1A4B).withOpacity(0.08),
            blurRadius: 5,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Row(
        children: [
          // **头像**
          Container(
            width: 52 * w,
            height: 52 * h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: const Color(0xFFECEFF2),
                width: 0.7,
              ),
              image: DecorationImage(
                image: NetworkImage(event["coverImageUrl"]),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SizedBox(width: 16 * w),

          // **事件信息**
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Text(
                    formattedDate,
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                  SizedBox(width: 6 * w),
                  const Icon(Icons.calendar_today,
                      size: 13, color: Color(0xFFC6C6C6)),
                  SizedBox(width: 6 * w),
                  Text(
                    formattedTime,
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4 * h),
              Row(
                children: [
                  Icon(eventIcon, size: 24, color: iconColor), // ✅ 事件类型图标
                  SizedBox(width: 6 * w),
                  Text(
                    event["name"],
                    style: const TextStyle(
                      fontFamily: 'Manrope',
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

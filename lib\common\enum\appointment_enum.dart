import 'package:json_annotation/json_annotation.dart';

@JsonEnum(valueField: 'code')
enum AppointmentStatus {
  draft('DRAFT'),
  confirmed('CONFIRMED'),
  inProgress('IN_PROGRESS'),
  completed('COMPLETED'),
  cancelled('CANCELLED'),
  noShow('NO_SHOW');

  final String code;
  const AppointmentStatus(this.code);

  factory AppointmentStatus.fromCode(String code) {
    return AppointmentStatus.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

@JsonEnum(valueField: 'code')
enum AppointmentSource {
  portal('PORTAL'),
  phone('PHONE'),
  walkIn('WALK_IN'),
  admin('ADMIN');

  final String code;
  const AppointmentSource(this.code);

  factory AppointmentSource.fromCode(String code) {
    return AppointmentSource.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

@JsonEnum(valueField: 'code')
enum StoreServiceStatus {
  active('ACTIVE'),
  inactive('INACTIVE'),
  suspended('SUSPENDED');

  final String code;
  const StoreServiceStatus(this.code);

  factory StoreServiceStatus.fromCode(String code) {
    return StoreServiceStatus.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

@JsonEnum(valueField: 'code')
enum ServiceCategory {
  grooming('GROOMING'),
  veterinary('VETERINARY'),
  daycare('DAYCARE'),
  boarding('BOARDING'),
  training('TRAINING'),
  other('OTHER');

  final String code;
  const ServiceCategory(this.code);

  factory ServiceCategory.fromCode(String code) {
    return ServiceCategory.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

@JsonEnum(valueField: 'code')
enum ServiceBreed {
  dog('DOG'),
  cat('CAT'),
  other('OTHER');

  final String code;
  const ServiceBreed(this.code);

  factory ServiceBreed.fromCode(String code) {
    return ServiceBreed.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

@JsonEnum(valueField: 'code')
enum Currency {
  cad('CAD'),
  usd('USD'),
  eur('EUR');

  final String code;
  const Currency(this.code);

  factory Currency.fromCode(String code) {
    return Currency.values.firstWhere(
      (element) => element.code == code,
      orElse: () => throw ArgumentError('Invalid code: $code'),
    );
  }
}

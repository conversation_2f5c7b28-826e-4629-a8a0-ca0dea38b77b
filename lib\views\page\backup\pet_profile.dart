// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
//
// import 'posts_section.dart';
// import 'events_section.dart';
// import 'places_section.dart';
// class PetProfilePage extends StatefulWidget {
//   @override
//   _PetProfilePageState createState() => _PetProfilePageState();
// }
//
// class _PetProfilePageState extends State<PetProfilePage> {
//   double _topSectionHeight = 100.0;
//   double _opacity = 1.0;
//   double _avatarSize = 128;
//   double _avatarMinSize = 74;
//   double _avatarBorder = 17;
//   double _avatarMinBorder = 10;
//
//   final RxString selectedPet = "Miro".obs;
//   final RxMap<String, String> currentPetData = <String, String>{}.obs;
//
//   final List<Map<String, String>> pets = [
//     {
//       "name": "<PERSON><PERSON>",
//       "image": "assets/images/pet1.png",
//       "type": "Dog",
//       "breed": "Toy fox terrier",
//       "gender": "Male",
//       "weight": "3.2 kg",
//       "birthday": "3 November 2023",
//       "age": "1 y 5 m",
//       "home": "Surrey, BC, Canada",
//       "nextVaccine": "18 March 2025",
//     },
//     {
//       "name": "Lily",
//       "image": "assets/images/pet2.png",
//       "type": "Cat",
//       "breed": "Siamese",
//       "gender": "Female",
//       "weight": "2.5 kg",
//       "birthday": "15 July 2022",
//       "age": "2 y 1 m",
//       "home": "Vancouver, BC, Canada",
//       "nextVaccine": "10 December 2024",
//     },
//     {
//       "name": "Buddy",
//       "image": "assets/images/pet3.png",
//       "type": "Dog",
//       "breed": "Golden Retriever",
//       "gender": "Male",
//       "weight": "5.8 kg",
//       "birthday": "21 May 2021",
//       "age": "3 y 3 m",
//       "home": "Toronto, ON, Canada",
//       "nextVaccine": "5 June 2025",
//     },
//   ];
//
//   @override
//   void initState() {
//     super.initState();
//     currentPetData.value = pets.firstWhere((pet) => pet["name"] == selectedPet.value);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     double screenWidth = MediaQuery.of(context).size.width;
//     double screenHeight = MediaQuery.of(context).size.height;
//     double widthRatio = screenWidth / 390;
//     double heightRatio = screenHeight / 844;
//
//     return Scaffold(
//       body: NotificationListener<ScrollNotification>(
//         onNotification: (scrollNotification) {
//           if (scrollNotification is ScrollUpdateNotification) {
//             setState(() {
//               _topSectionHeight = (100 - scrollNotification.metrics.pixels)
//                   .clamp(0, 100)
//                   .toDouble();
//               _opacity = (_topSectionHeight / 100).clamp(0, 1);
//               _avatarSize = (128 - scrollNotification.metrics.pixels)
//                   .clamp(_avatarMinSize, 128)
//                   .toDouble();
//               _avatarBorder = (17 - scrollNotification.metrics.pixels)
//                   .clamp(_avatarMinBorder, 17)
//                   .toDouble();
//             });
//           }
//           return true;
//         },
//         child: Column(
//           children: [
//             _buildTopSection(widthRatio, heightRatio),
//             Expanded(
//               child: SingleChildScrollView(
//                 physics: BouncingScrollPhysics(),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     _buildProfileSection(widthRatio, heightRatio),
//                     _buildInfoSection(widthRatio, heightRatio),
//                     _buildTabSection(widthRatio, heightRatio), // ✅ 保持 `TabBarView` 结构
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildTopSection(double w, double h) {
//     return AnimatedContainer(
//       duration: Duration(milliseconds: 200),
//       height: _topSectionHeight * h,
//       padding: EdgeInsets.symmetric(horizontal: 24 * w),
//       child: Opacity(
//         opacity: _opacity,
//         child: Row(
//           children: [
//             // **返回按钮**
//             IconButton(
//               onPressed: () {
//                 Get.back();
//               },
//               icon: const Icon(Icons.arrow_back, color: Colors.black),
//             ),
//             SizedBox(width: 8 * w),
//
//             // **竖线分隔符**
//             Container(
//               width: 1,
//               height: 22 * h,
//               color: const Color(0xFFECEFF2),
//             ),
//             SizedBox(width: 8 * w),
//
//             // **"Pet Profile" 文字**
//             const Text(
//               "Pet Profile",
//               style: TextStyle(
//                 fontFamily: 'Manrope',
//                 fontWeight: FontWeight.w500,
//                 fontSize: 16,
//                 color: Color(0xFF262626),
//               ),
//             ),
//             const Spacer(),
//
//             // **右侧：宠物头像 + 名字 + 下拉按钮**
//             Obx(() => Container(
//               padding: EdgeInsets.symmetric(horizontal: 8 * w),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: DropdownButtonHideUnderline(
//                 child: DropdownButton<String>(
//                   value: selectedPet.value,
//                   icon: const Icon(Icons.keyboard_arrow_down,
//                       color: Color(0xFFC6C6C6)),
//                   dropdownColor: Colors.white,
//                   items: pets.map((pet) {
//                     return DropdownMenuItem<String>(
//                       value: pet["name"],
//                       child: Row(
//                         children: [
//                           CircleAvatar(
//                             radius: 10 * w,
//                             backgroundImage: AssetImage(pet["image"]!),
//                           ),
//                           SizedBox(width: 8 * w),
//                           Text(
//                             pet["name"]!,
//                             style: const TextStyle(
//                               fontFamily: 'Manrope',
//                               fontWeight: FontWeight.w500,
//                               fontSize: 12,
//                               color: Color(0xFF262626),
//                             ),
//                           ),
//                         ],
//                       ),
//                     );
//                   }).toList(),
//                   onChanged: (String? newValue) {
//                     if (newValue != null) {
//                       selectedPet.value = newValue;
//                       currentPetData.value = pets
//                           .firstWhere((pet) => pet["name"] == newValue);
//                     }
//                   },
//                 ),
//               ),
//             )),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildProfileSection(double w, double h) {
//     return Obx(() => Padding(
//       padding: EdgeInsets.all(20 * w),
//       child: Row(
//         children: [
//           Container(
//             decoration: BoxDecoration(
//               shape: BoxShape.circle,
//               border: Border.all(color: const Color(0xFFFDECCE), width: _avatarBorder),
//             ),
//             child: AnimatedContainer(
//               duration: Duration(milliseconds: 200),
//               width: _avatarSize * w,
//               height: _avatarSize * w,
//               child: CircleAvatar(
//                 backgroundImage: AssetImage(currentPetData["image"] ?? "assets/images/default.png"),
//               ),
//             ),
//           ),
//           SizedBox(width: 20 * w),
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Row(
//                 children: [
//                   Text(
//                     currentPetData["name"] ?? "Unknown",
//                     style: const TextStyle(
//                       fontFamily: 'Manrope',
//                       fontWeight: FontWeight.w600,
//                       fontSize: 22,
//                       color: Color(0xFF262626),
//                     ),
//                   ),
//                   SizedBox(width: 8 * w),
//                   Opacity(
//                     opacity: _opacity,
//                     child: Container(
//                       padding: EdgeInsets.all(9 * w),
//                       decoration: BoxDecoration(
//                         border: Border.all(color: const Color(0xFFF2D3A4), width: 1),
//                         borderRadius: BorderRadius.circular(14),
//                       ),
//                       child: const Icon(
//                         Icons.edit,
//                         size: 20,
//                         color: Color(0xFFF2D3A4),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//               SizedBox(height: 4 * h),
//               Opacity(
//                 opacity: _opacity,
//                 child: Text(
//                   "${currentPetData["type"] ?? "-"}  |  ${currentPetData["breed"] ?? "-"}",
//                   style: const TextStyle(
//                     fontFamily: 'Manrope',
//                     fontWeight: FontWeight.w500,
//                     fontSize: 12,
//                     color: Color(0xFFC6C6C6),
//                   ),
//                 ),
//               ),
//             ],
//           )
//         ],
//       ),
//     ));
//   }
//
//   Widget _buildInfoSection(double w, double h) {
//     return Obx(() => Padding(
//       padding: EdgeInsets.symmetric(horizontal: 38 * w),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SizedBox(height: 24 * h),
//           _buildInfoRow("Gender", currentPetData["gender"] ?? "-", w, h),
//           SizedBox(height: 12 * h),
//           _buildInfoRow("Weight", currentPetData["weight"] ?? "-", w, h),
//           SizedBox(height: 24 * h),
//           _buildDetailRow(
//               Icons.cake,
//               "Birthday",
//               currentPetData["birthday"] ?? "-",
//               _calculateAge(currentPetData["birthday"] ?? ""),
//               w,
//               h),
//           _buildDetailRow(
//               Icons.home, "My home", currentPetData["home"] ?? "-", "", w, h),
//           _buildDetailRow(Icons.vaccines, "Next vaccine",
//               currentPetData["nextVaccine"] ?? "-", "", w, h),
//         ],
//       ),
//     ));
//   }
//
//   Widget _buildInfoRow(String title, String value, double w, double h) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween, // ✅ 左右对齐
//       children: [
//         Text(
//           title,
//           style: const TextStyle(
//             fontFamily: 'Manrope',
//             fontWeight: FontWeight.w400,
//             fontSize: 14,
//             color: Color(0xFF262626),
//           ),
//         ),
//         Text(
//           value,
//           style: const TextStyle(
//             fontFamily: 'Manrope',
//             fontWeight: FontWeight.w600, // ✅ 加粗
//             fontSize: 14,
//             color: Color(0xFF262626),
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildDetailRow(IconData icon, String title, String value,
//       String? subValue, double w, double h) {
//     return Padding(
//       padding: EdgeInsets.symmetric(vertical: 8 * h),
//       child: Row(
//         children: [
//           Container(
//             width: 46 * w,
//             height: 46 * h,
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(12),
//               border: Border.all(color: const Color(0xFFF2D3A4), width: 1),
//             ),
//             child: Icon(icon, size: 20, color: const Color(0xFFF2D3A4)),
//           ),
//           SizedBox(width: 16 * w),
//           Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 title,
//                 style: const TextStyle(
//                   fontFamily: 'Manrope',
//                   fontWeight: FontWeight.w400,
//                   fontSize: 14,
//                   color: Color(0xFFC6C6C6),
//                 ),
//               ),
//               Text(
//                 value,
//                 style: const TextStyle(
//                   fontFamily: 'Manrope',
//                   fontWeight: FontWeight.w600,
//                   fontSize: 14,
//                   color: Color(0xFF262626),
//                 ),
//               ),
//             ],
//           ),
//           const Spacer(),
//           if (subValue != null && subValue.isNotEmpty)
//             Text(
//               subValue,
//               style: const TextStyle(
//                 fontFamily: 'Manrope',
//                 fontWeight: FontWeight.w600,
//                 fontSize: 16,
//                 color: Color(0xFF262626),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildTabSection(double widthRatio, double heightRatio) {
//     return Column(
//       children: [
//         DefaultTabController(
//           length: 3,
//           child: Column(
//             children: [
//               TabBar(
//                 labelStyle: TextStyle(
//                   fontFamily: 'Manrope',
//                   fontWeight: FontWeight.w600,
//                   fontSize: 14,
//                   color: Color(0xFF262626), // 选中时的颜色
//                 ),
//                 unselectedLabelStyle: TextStyle(
//                   fontFamily: 'Manrope',
//                   fontWeight: FontWeight.w400,
//                   fontSize: 14,
//                   color: Color(0xFFC6C6C6), // 未选中时的颜色
//                 ),
//                 labelColor: Colors.black, // 选中标签颜色
//                 unselectedLabelColor: Color(0xFFC6C6C6), // 未选中标签颜色
//                 tabs: [
//                   Tab(text: "Posts"),
//                   Tab(text: "Events"),
//                   Tab(text: "Places"),
//                 ],
//               ),
//               Container(
//                 height: 350 * heightRatio,
//                 child: TabBarView(
//                   children: [
//                     PostsSection(),
//                     EventsSection(),
//                     PlacesSection(),
//                   ],
//                 ),
//               )
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   String _calculateAge(String birthday) {
//     try {
//       DateTime birthDate = DateFormat("d MMMM yyyy").parse(birthday);
//       DateTime now = DateTime.now();
//
//       int years = now.year - birthDate.year;
//       int months = now.month - birthDate.month;
//
//       if (months < 0) {
//         years -= 1;
//         months += 12;
//       }
//
//       return "$years y $months m";
//     } catch (e) {
//       return "N/A"; // ✅ 解析失败时返回 "N/A"
//     }
//   }
// }

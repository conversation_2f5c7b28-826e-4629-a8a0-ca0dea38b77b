import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/component/widgets/common_button.dart';
//import 'package:onenata_app/views/component/widgets/common_whitesection.dart';
import 'package:onenata_app/views/component/widgets/common_backbutton.dart';
import 'package:onenata_app/views/component/widgets/common_avatar.dart';
import 'package:onenata_app/views/theme/layouts/theme_layout_i.dart';

import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../../component/progress/loading_widget.dart';
import '../../theme/colors/color_extension.dart';
import '../../theme/text_schemes/text_style_extension.dart';
import '../../theme/theme_service.dart';

import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../page_interceptor.dart';
import '../root_page.dart';

class SelectPetPage extends StatefulWidget {
  const SelectPetPage({super.key});

  @override
  SelectPetPageState createState() => SelectPetPageState();
}

class SelectPetPageState extends State<SelectPetPage> {
  late Future<SelectPetPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => SelectPetPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<SelectPetPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.primary,
              body: Stack(
                children: [
                  ProfileWidgetBuilder.buildWhiteSection2(controller.theme),
                  ListView(
                    children: [
                      SizedBox(height: 680.w),
                      AuthWidgetBuilder.buildButton(
                          controller.theme,
                          controller.buttonController,
                          text: 'continue'.t18,
                          isEnabled:true,
                          onPressed: controller.submit
                      ),
                      AuthWidgetBuilder.buildAlterButton(
                        controller.theme,
                        controller.alterButtonController,
                        text: 'skip'.t18,
                        onPressed: controller.onSkipPressed,
                      ),
                    ],
                  ),
                  ProfileWidgetBuilder.buildTopSection(
                      controller.theme, context),
                  Column(
                    children: [
                      SizedBox(height: 261.w),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 27.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ProfileWidgetBuilder.buildPetSelectCard(
                              theme: controller.theme,
                              imagePath: 'assets/images/dog.png',
                              onTap: () => controller.selectPet('dog'),
                              name: PetType.dog.t18key.t18,
                            ),
                            ProfileWidgetBuilder.buildPetSelectCard(
                              theme: controller.theme,
                              imagePath: 'assets/images/cat.png',
                              onTap: () => controller.selectPet('cat'),
                              name: PetType.cat.t18key.t18,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 25.w),
                      Center(
                        child: ProfileWidgetBuilder.buildPetSelectCard(
                          theme: controller.theme,
                          imagePath: 'assets/images/pig.png',
                          onTap: () => controller.selectPet('other'),
                          name: PetType.others.t18key.t18,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          }
        });
  }
}

class SelectPetPageController extends GetxController {
  final CommonButtonController buttonController =
  Get.put(CommonButtonController(), tag: 'select-pet-submit');
  final CommonButtonController alterButtonController =
  Get.put(CommonButtonController(), tag: 'select-pet-alter');

  final RxString selectedPet = ''.obs;

  late ThemePlugin theme;

  Future<SelectPetPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());
    buttonController.isEnabled.value = false;
    buttonController.text.value = 'continue'.t18;

    // 设置备用按钮
    alterButtonController.onPressed.value = onSkipPressed;
    return this;
  }

  void selectPet(String pet) {
    selectedPet.value = pet;
  }

  // Future<void> submit() async {
  //   Get.toNamed('/petDetail');
  // }
  Future<void> submit() async {

    logger.d(selectedPet.value);
    userDataPetListSkippedThisTime = true;

    Get.toNamed('/petDetail', arguments: {'selectedPet': selectedPet.value});
  }

  Future<void> onSkipPressed() async {

    // Skip select pet
    // await Storage.instance.write(StorageKeys.userDataPetListSkipped, true);
    userDataPetListSkippedThisTime = true;

    if (Get.key.currentState?.canPop() == null || Get.key.currentState?.canPop() == false) {
      await Get.offAll(()=> const RootPage());
    } else {
      Get.back();
    }
  }
}

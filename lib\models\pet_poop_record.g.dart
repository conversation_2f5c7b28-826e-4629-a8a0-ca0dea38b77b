// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pet_poop_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PetPoopRecord _$PetPoopRecordFromJson(Map<String, dynamic> json) =>
    PetPoopRecord(
      sid: json['sid'] as String,
      poopTime: (json['poopTime'] as num?)?.toInt(),
      shapeTags: (json['shapeTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      colorTags: (json['colorTags'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$PetPoopRecordToJson(PetPoopRecord instance) =>
    <String, dynamic>{
      'sid': instance.sid,
      'poopTime': instance.poopTime,
      'shapeTags': instance.shapeTags,
      'colorTags': instance.colorTags,
      'images': instance.images,
      'notes': instance.notes,
    };

import 'dart:core';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import 'package:onenata_app/common/const/const_i.dart';

class Storage extends GetxService {

  static Storage get instance => Get.find();

  bool newInstall = false;

  // Singleton factory
  static final Storage _instance = Storage._internal();
  factory Storage() => _instance;
  Storage._internal();

  Future<Storage> init() async {

    await GetStorage.init();
    await _isNewInstall();

    return this;
  }

  final GetStorage _storage = GetStorage();

  Future<void> write(String key, dynamic value) async {
    await _storage.write(key, value);
  }

  dynamic read(String key) {
    return _storage.read(key);
  }

  Future<void> remove(String key) async {
    await _storage.remove(key);
  }

  Future<void> deleteAll() async {
    await _storage.erase();
  }

  bool hasData(String key) {
    return _storage.hasData(key);
  }

  /// Load new install flag
  Future<void> _isNewInstall() async {

    if (!hasData(StorageKeys.intIsNewInstall)) {
      newInstall = true;
      await write(StorageKeys.intIsNewInstall, true.toString());
    }
    else {
      newInstall = read(StorageKeys.intIsNewInstall) == true.toString();
    }
  }

  /// Update new install flag
  Future<void> updateNewInstall(bool isNew) async {
    newInstall = isNew;
    await write(StorageKeys.intIsNewInstall, isNew.toString());
  }
}

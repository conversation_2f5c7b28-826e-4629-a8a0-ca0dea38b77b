class StorageKeys {

  // State
  static const String intIsNewInstall = 'onenata.init.is.new.install';
  static const String preferenceLocale = 'onenata.preference.locale';

  static const String userAccount = 'onenata.user.account';
  static const String userAuthRecord = 'onenata.user.auth.record';
  static const String userData = 'onenata.user.data';
  static const String ownedPets = 'onenata.user.owned.pets';
  static const String defaultPet = 'onenata.user.default.pet';

  static const String userDataNameSkipped = 'onenata.user.data.name.skipped';
  static const String userDataEmailSkipped = 'onenata.user.data.email.skipped';
  static const String userDataAddressSkipped = 'onenata.user.data.address.skipped';
  static const String userDataPetListSkipped = 'onenata.user.data.pet.list.skipped';
}
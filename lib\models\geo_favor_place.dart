import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/utils/utils_i.dart';

import 'base_full_model.dart';
import 'geo_i.dart';

part 'geo_favor_place.g.dart';

@JsonSerializable()
class GeoFavorPlace extends BaseFullModel {

  String uid; // sid of user account
  String placeName; // place name in google map, in format 'places/$placeId'
  GeoPlaceFilterType filterType;

  GeoFavorPlace({
    required this.uid,
    required this.placeName,
    required this.filterType,
    super.id,
    super.sid,
    super.name,
    super.isValid,
    super.isSynced,
    super.createdBy,
    super.createDate,
    super.updatedBy,
    super.updateDate,
    super.reviewedBy,
    super.reviewDate,
    super.approveStatus,
    super.notes,
    super.tags,
  });

  factory GeoFavorPlace.fromJson(Map<String, dynamic> json) => _$GeoFavorPlaceFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GeoFavorPlaceToJson(this);

  factory GeoFavorPlace.fromFirestoreData(DocumentSnapshot doc) {

    Map<String, dynamic> dbData = doc.data() as Map<String, dynamic>;

    final Map<String, dynamic> jsonData = {};
    dbData.forEach((key, value) {
      jsonData[JsonUtil.snakeCaseToCamelCase(key)] = value;
    });

    return GeoFavorPlace.fromJson(jsonData);
  }

  Map<String, dynamic> toFirestoreData() {

    final Map<String, dynamic> jsonData = toJson();
    final Map<String, dynamic> dbData = {};
    jsonData.forEach((key, value) {
      dbData[JsonUtil.camelCaseToSnakeCase(key)] = value;
    });

    return dbData;
  }

  static String get collection => 'geo_favor_place';

  static create ({
    required String uid,
    required String placeName,
    required GeoPlaceFilterType filterType,
  }) {

    return GeoFavorPlace(
      sid: uuid.v4(),
      uid: uid,
      placeName: placeName,
      filterType: filterType,
      isValid: true,
    );
  }

  static GeoFavorPlace copyFrom(GeoFavorPlace other) {

    return GeoFavorPlace(
      id: other.id,
      sid: other.sid,
      name: other.name,
      isValid: other.isValid,
      isSynced: other.isSynced,
      createdBy: other.createdBy,
      createDate: other.createDate,
      updatedBy: other.updatedBy,
      updateDate: other.updateDate,
      reviewedBy: other.reviewedBy,
      reviewDate: other.reviewDate,
      approveStatus: other.approveStatus,
      notes: other.notes,
      tags: other.tags,
      uid: other.uid,
      placeName: other.placeName,
      filterType: other.filterType,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

}

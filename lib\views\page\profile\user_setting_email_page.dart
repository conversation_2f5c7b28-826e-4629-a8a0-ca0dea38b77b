import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/profile/profile_widget_builder.dart';
import '../page_interceptor.dart';

class UserSettingEmailPage extends StatefulWidget {
  const UserSettingEmailPage({super.key});

  @override
  UserSettingEmailPageState createState() => UserSettingEmailPageState();
}

class UserSettingEmailPageState extends State<UserSettingEmailPage> {
  late Future<UserSettingEmailPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => UserSettingEmailPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserSettingEmailPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Obx(() {
                return Stack(
                  children: [
                    ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        controller.clearStepInput(controller.step.value);
                        if (controller.step.value > 1 && controller.step.value<4) {
                          controller.step.value -= 1;
                        } else {
                          Get.back();
                        }
                        //Get.back();
                      },
                      topic: "Email",
                      showAvatar: false,
                    ),
                    if (controller.step.value == 1)
                      _buildCurrentEmailSection(controller),
                    if (controller.step.value == 2)
                      _buildEmailInputSection(controller),
                    if (controller.step.value == 3)
                      _buildOtpInputSection(controller),
                    if (controller.step.value == 4)
                      _buildFinalScreenSection(controller),
                    if (controller.step.value != 4)
                      ProfileWidgetBuilder.buildButton2(
                        controller.theme,
                        controller.buttonController,
                        721,
                      ),
                    if (controller.step.value != 4)
                      ProfileWidgetBuilder.buildButton2(
                        controller.theme,
                        controller.alterButtonController,
                        781,
                      ),
                  ],
                );
              }),
            );
          }
        });
  }

  /// **步骤 1：当前 Email**
  Widget _buildCurrentEmailSection(UserSettingEmailPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText(
            controller.theme, 128, 'Current email address'),
        ProfileWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.emailDisplayInputController, 12),
      ],
    );
  }

  /// **步骤 2：输入 Email**
  Widget _buildEmailInputSection(UserSettingEmailPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText2(
          controller.theme,
          128,
          "Current email address: <EMAIL>\n"
          "Enter the new email address you would like to associate\n"
          "with your account below. We will send a One Time\n"
          "Password (OTP) to that address.",
        ),
        ProfileWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.emailInputController, 12),
      ],
    );
  }

  /// **步骤 3：输入 OTP**
  Widget _buildOtpInputSection(UserSettingEmailPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText(
          controller.theme,
          128,
          "Verify email address",
        ),
        //SizedBox(height: 12.h),
        ProfileWidgetBuilder.buildText2(
          controller.theme,
          12,
          "We’ve sent a One Time Password (OTP) to your email\n"
          "address. Please enter it below.",
        ),
        ProfileWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.codeInputController, 12),
      ],
    );
  }

  /// **步骤 4：最终确认页面**
  Widget _buildFinalScreenSection(UserSettingEmailPageController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ProfileWidgetBuilder.buildText(
          controller.theme,
          128,
          "Current email address",
        ),
        ProfileWidgetBuilder.buildText2(
          controller.theme,
          12,
          "Your current email address has been verified, use the new"
          "\naddress next time you log in.",
        ),
        ProfileWidgetBuilder.buildSettingInputBox(
            controller.theme, controller.emailDisplayInputController, 12),
      ],
    );
  }
}

class UserSettingEmailPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;

  late CommonTextField3Controller emailDisplayInputController;
  final TextEditingController emailDisplayInputBox = TextEditingController();
  late CommonTextField3Controller emailInputController;
  final TextEditingController emailInputBox = TextEditingController();
  late CommonTextField3Controller codeInputController;
  final TextEditingController codeInputBox = TextEditingController();

  late ThemePlugin theme;
  late final CommonButtonController submitButtonController;
  late final CommonButtonController cancelButtonController;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  final RxInt step = 1.obs;
  //final RxString emailHint = "<EMAIL>".obs;
  late RxString emailHint;
  final RxBool isEmailValid = false.obs;
  final RxString buttonText = "Change your email".obs;

  Future<UserSettingEmailPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    theme = await Get.putAsync(() => ThemePlugin().init());
    userAccount.value = _authService.userAccount.value;
    emailHint = (userAccount.value!.email??'userEmail').obs;
    account = _authService.userAccount.value;

    step.listen((value) {
      if (value == 1) {
        buttonText.value = "Change your email";
      } else {
        buttonText.value = "Continue";
      }
    });
    emailDisplayInputController =
        ProfileWidgetBuilder.buildEmailDisplayTextFieldController(
            theme, emailDisplayInputBox.obs, emailHint);
    codeInputController =
        ProfileWidgetBuilder.buildValidCodeTextFieldController(
            theme, codeInputBox.obs, ''.obs);
    emailInputController = ProfileWidgetBuilder.buildEmailTextFieldController(
        theme, emailInputBox.obs, ''.obs,
        onChanged: validateEmail.obs);
    buttonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: buttonText,
      onPressed: nextStep.obs,
      color: theme.themeData.colorScheme.secondary.obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    alterButtonController = CommonButton3Controller(
      isEnabled: true.obs,
      text: 'cancel'.t18.obs,
      onPressed: onCancelPressed.obs,
      color: theme.themeData.colorScheme.surface.withAlpha(0.0.colorAlpha).obs,
      disabledColor: theme.themeData.colorScheme.tertiary.obs,
      textStyle: theme.textStyleExtension.authPageAlterButton.obs,
      disabledTextStyle: theme.textStyleExtension.authPageDisabledButton.obs,
    );
    return this;
  }

  Future<void> validateEmail(
      {required BuildContext context, required bool result}) async {
    isEmailValid.value = result;
  }

  void clearStepInput(int step) {
    switch (step) {
      case 1:
        break;
      case 2:
        break;
      case 3:
        emailInputBox.text = "";
        break;
      case 4:
        codeInputBox.text ="";
        break;
    }
  }

  //void nextStep() {
  Future<void> nextStep() async {
    if (step.value == 1) {
      step.value = 2;
    } else if (step.value == 2) {
      if (isEmailValid.value) {
        step.value = 3;
      }
    } else if (step.value == 3) {
      if (codeInputBox.text == "123456") {

        //await _authService.linkEmailAddress(email, password)
        account = UserAccount.copyFrom(_authService.userAccount.value!);
        account?.email=emailInputBox.text;
        await _userService.updateUserAccount(account!);

        emailHint.value = emailInputBox.text;
        //emailDisplayInputBox.text = emailHint.value;
        emailInputBox.clear();
        codeInputBox.clear();
        step.value = 4;
      }
    }
  }

  Future<void> onCancelPressed() async {
    Get.back();
  }
}

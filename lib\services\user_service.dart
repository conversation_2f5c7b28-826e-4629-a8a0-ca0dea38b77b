import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:image_picker/image_picker.dart';

import 'package:onenata_app/common/enum/enum_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/logger/logger_i.dart';
import 'package:onenata_app/common/plugin/firebase/firebase_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/utils/utils_i.dart';
import 'package:onenata_app/models/models_i.dart';
import 'package:onenata_app/dao/daos_i.dart';
import 'package:onenata_app/services/auth_service.dart';

class UserService {

  // Firebase services
  final FirebaseMessaging _messagingService = FirebaseMessaging.instance;

  // Services
  final AuthService authService = AuthService.instance;

  // Local storage
  final CloudStorage cloudStorage = CloudStorage();
  final Storage getStorage = Storage.instance;
  final LocalStorage localStorage = LocalStorage.instance;
  final SecureStorage secureStorage = SecureStorage.instance;

  final UserDao _userDao = UserDao();

  Future<UserService> init() async {

    return this;
  }

  // User account ------------------------------------------------------------
  Future<void> createUserAccount(User user, AuthChannel channel, {String? salt, String? hashedPassword}) async {

    // Get user id from firestore
    UserAccount? exist = await _userDao.getUserAccountById(fid: user.uid);

    if (exist == null) {

      // Persist user account in the firestore
      UserAccount account = UserAccount.create(
          fid: user.uid,
          phoneNumber: user.phoneNumber,
          email: user.email,
          salt: salt,
          hashedCredential: hashedPassword,
          isEmailVerified: user.emailVerified
      );

      // Update user account in auth service
      authService.userAccount.value = account;

      // Add user account in firestore
      await _userDao.addUserAccount(account: account);

      // Update user account in secure storage
      await saveLocalUserAccount(account);

    } else {

      // Update user account
      exist.phoneNumber = user.phoneNumber;
      exist.email = user.email;
      exist.salt = salt;
      exist.hashedCredential = hashedPassword;
      exist.isEmailVerified = user.emailVerified;

      // Update user account in auth service
      authService.userAccount.value = UserAccount.copyFrom(exist);

      // Update user account in firestore
      await _userDao.updateUserAccount(exist);

      // Update user account in secure storage
      await saveLocalUserAccount(exist);
    }
  }

  Future<void> updateUserAccount(UserAccount account) async {

    // Update user account in auth service
    authService.userAccount.value = UserAccount.copyFrom(account);

    // Update user account in firestore
    await _userDao.updateUserAccount(account);

    // Update user account in secure storage
    await saveLocalUserAccount(account);
  }

  // TODO local storage first
  Future<UserAccount?> getUserAccountById({String? uid, String? fid, String? email, String? phoneNumber}) async {
    return await _userDao.getUserAccountById(uid: uid, fid: fid, email: email, phoneNumber: phoneNumber);
  }

  // User auth record --------------------------------------------------------
  Future<void> recordAuthActivity(User user, AuthChannel channel) async {

    // Get user id from firestore
    UserAccount? account = await _userDao.getUserAccountById(fid: user.uid);

    if (account == null) {
      logger.e('User account not found for firebase id: ${user.uid}');
      return;
    }

    // Prepare auth record
    UserAuthRecord authRecord = UserAuthRecord.create(
      uid: account.sid!,
      authChannel: channel,
      deviceModel: await DeviceInfoUtil.getDeviceModel(),
      deviceOS: await DeviceInfoUtil.getDeviceOs(),
      deviceId: await DeviceInfoUtil.getDeviceId(),
      // fcmToken: await _messagingService.getToken(),
    );

    // Save auth record in auth service
    authService.userAuthRecord.value = authRecord;

    // Save auth record in firestore
    await _userDao.addUserAuthRecord(authRecord);

    // Save auth record in secure storage
    await saveLocalUserAuthRecord(authRecord);
  }
  Future<void> getLatestUserAuthRecordById({String? userAuthRecordId, String? userId}) async {
    await _userDao.getLatestUserAuthRecordById(userAuthRecordId: userAuthRecordId);
  }

  // User Data ---------------------------------------------------------------
  Future<UserData?> getUserDataById({String? userDataId, String? userId}) async {
    return await _userDao.getUserDataById(userDataId: userDataId, userId: userId);
  }
  Future<void> createUserData(UserData data) async {

    // Get user id from firestore
    UserAccount? account = await _userDao.getUserAccountById(uid: data.uid);

    if (account == null) {
      logger.e('Create user data failed. User account not found for firebase id: ${data.uid}');
      return;
    }

    // store user data in firestore
    if (authService.userData.value != null && authService.userData.value!.uid != data.uid) {
      await _userDao.updateUserData(data);
    } else {
      await _userDao.addUserData(data);
    }

    // store user data in auth service
    authService.userData.value = data;

    // Save user data in secure storage
    await saveLocalUserData(data);
  }
  Future<void> updateUserData(UserData data) async {

    // Update user account in auth service
    authService.userData.value = data;

    // Update user account in firestore
    await _userDao.updateUserData(data);

    // Update user account in secure storage
    await saveLocalUserData(data);
  }

  /// Upload user avatar to firebase storage and save into Document directory
  /// [uid] sid of user account
  /// [image] file picked by image picker
  Future<void> uploadUserAvatar({required String uid, required XFile image}) async {

      String? fileType = FileUtil.getFileType(image.path);
      String path = FileUtil.buildUserResourcePath(MediaType.userAvatar, uid);
      String name = fileType != null ? '${uuid.v4()}.$fileType' : uuid.v4();
      String filePath = '$path/$name';

      // Upload file to cloud storage
      Uint8List bytes = await image.readAsBytes();
      await cloudStorage.uploadFile(filePath, bytes: bytes);

      // Save file at local document directory
      await localStorage.saveImageToPath(path: path, filename: name, bytes: bytes);

      // Update user data if exists
      UserData? data = await getUserDataById(userId: uid);
      if (data != null) {
        data.avatar = name;
        await updateUserData(data);
      }
  }

  // Local storage -----------------------------------------------------------
  Future<void> saveLocalUserAccount(UserAccount account) async {
    await secureStorage.write(StorageKeys.userAccount, jsonEncode(account.toJson()));
  }
  Future<void> clearLocalUserAccount() async {
    await secureStorage.delete(StorageKeys.userAccount);
  }

  Future<void> saveLocalUserAuthRecord(UserAuthRecord record) async {
    await secureStorage.write(StorageKeys.userAuthRecord, jsonEncode(record.toJson()));
  }
  Future<void> clearLocalUserAuthRecord() async {
    await secureStorage.delete(StorageKeys.userAuthRecord);
  }

  Future<void> saveLocalUserData(UserData data) async {
    await secureStorage.write(StorageKeys.userData, jsonEncode(data.toJson()));
  }
  Future<void> clearLocalUserData() async {
    await secureStorage.delete(StorageKeys.userData);
  }

  Future<void> clearUserProfileSkip() async {
    await getStorage.remove(StorageKeys.userDataAddressSkipped);
    await getStorage.remove(StorageKeys.userDataPetListSkipped);
  }

  // Util methods --------------------------------------------------------------
  Future<String?> getFcmToken() async {
    return await _messagingService.getToken();
  }
}

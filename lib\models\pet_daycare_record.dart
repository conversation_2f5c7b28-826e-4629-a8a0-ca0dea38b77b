import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'base_full_model.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

part 'pet_daycare_record.g.dart';

@JsonSerializable()
class PetDaycareRecord{
  String sid;
  int? startTime;
  int? endTime;
  String? location;
  List<String>? content;
  List<String>? images;
  String? notes;

  PetDaycareRecord({
    required this.sid,
    required this.startTime,
    required this.endTime,
    this.location,
    this.content,
    this.images,
    this.notes,
  });

  factory PetDaycareRecord.fromJson(Map<String, dynamic> json) => _$PetDaycareRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetDaycareRecordToJson(this);

  static create({
    required String uid,
    required String pid,
    int? startTime,
    int? endTime,
    String? location,
    List<String>? content,
    String? notes,
    List<String>? images,
  }) {
    return PetDaycareRecord(
      sid: uuid.v4(),
      startTime: startTime,
      endTime: endTime,
      location: location,
      content: content,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetDaycareRecord other) {
    return PetDaycareRecord(
      sid: other.sid,
      startTime: other.startTime,
      endTime: other.endTime,
      location: other.location,
      notes: other.notes,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

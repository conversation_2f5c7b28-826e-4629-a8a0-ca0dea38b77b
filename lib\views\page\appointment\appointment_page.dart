import 'dart:async';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:onenata_app/common/enum/enum_i.dart';

import 'package:onenata_app/common/plugin/i18n/i18n_i.dart';
import 'package:onenata_app/common/const/const_i.dart';
import 'package:onenata_app/common/plugin/storage/storage_i.dart';
import 'package:onenata_app/common/type/global_var.dart';
import 'package:onenata_app/views/page/profile/profile_pages_i.dart';
import 'package:onenata_app/views/theme/colors/color_extension.dart';
import 'package:onenata_app/views/theme/theme_i.dart';
import 'package:onenata_app/views/component/widgets/common_i.dart';
import 'package:onenata_app/views/component/progress/progress_i.dart';

import '../../../models/user_account.dart';
import '../../../models/user_data.dart';
import '../../../services/auth_service.dart';
import '../../../services/user_service.dart';
import '../../component/auth/auth_widget_builder.dart';
import '../../component/profile/profile_widget_builder.dart';

import '../page_interceptor.dart';
import '../root_page.dart';


class AppointmentPage extends StatefulWidget {
  const AppointmentPage({super.key});

  @override
  AppointmentPageState createState() => AppointmentPageState();
}

class AppointmentPageState extends State<AppointmentPage> {
  late Future<AppointmentPageController> _controller;

  @override
  void initState() {
    super.initState();
    // Initialize the controller in initState
    _controller = Get.putAsync(() => AppointmentPageController().init());
    // controller = Get.put(WelcomePageController() as Future<WelcomePageController>);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<AppointmentPageController>(
        future: _controller,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Indicator for loading
            return const Center(
              child: LoadingWidget(),
            );
          } else if (snapshot.hasError) {
            // Error handling
            return Center(child: Text('Error: ${snapshot.error}'));
          } else {
            // Page functions
            final controller = snapshot.data!;
            return CommonPage(
              theme: controller.theme,
              backgroundColor: controller.theme.themeData.colorScheme.onPrimary,
              body: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: ProfileWidgetBuilder.buildUserSettingTopSection(
                      theme: controller.theme,
                      onBack: () {
                        Get.back();
                      },
                      topic: "Appointment",
                      showAvatar: false,
                    ),
                  ),
                  // Main content - positioned below top section
                  Positioned(
                    top: 90.w,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Column(
                      children: [
                        SizedBox(height: 16.w),
                        // Tab navigation
                        _buildTabNavigation(controller),
                        SizedBox(height: 16.w),
                        // Tab content
                        Expanded(
                          child: Obx(() => _buildTabContent(controller)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }
        });
  }

  // Build tab navigation
  Widget _buildTabNavigation(AppointmentPageController controller) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(25.w),
      ),
      child: Row(
        children: [
          _buildTabItem(controller, 0, 'Upcoming'),
          _buildTabItem(controller, 1, 'History'),
        ],
      ),
    );
  }

  Widget _buildTabItem(AppointmentPageController controller, int index, String title) {
    return Expanded(
      child: Obx(() => GestureDetector(
        onTap: () => controller.selectedTabIndex.value = index,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.w),
          decoration: BoxDecoration(
            color: controller.selectedTabIndex.value == index
                ? Color(0xFFF2D3A4)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(20.w),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Manrope',
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: controller.selectedTabIndex.value == index
                  ? Colors.white
                  : Color(0xFF666666),
            ),
          ),
        ),
      )),
    );
  }

  // Build tab content
  Widget _buildTabContent(AppointmentPageController controller) {
    if (controller.selectedTabIndex.value == 0) {
      return _buildUpcomingTab(controller);
    } else {
      return _buildHistoryTab(controller);
    }
  }

  // Build upcoming appointments tab
  Widget _buildUpcomingTab(AppointmentPageController controller) {
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      children: [
        _buildAppointmentCard(
          iconColor: Color(0xFFE8B4FF),
          iconData: Icons.content_cut,
          dayDate: "Sun 25 Jul 2025",
          time: "14:00 - 15:00",
          shopName: "Grooming @ PerfectPet",
        ),
        SizedBox(height: 16.w),
        _buildAppointmentCard(
          iconColor: Color(0xFFFFC107),
          iconData: Icons.medical_services,
          dayDate: "Wed 5 Jun 2025",
          time: "10:00",
          shopName: "DDP2 @ All About Pet Clinic",
        ),
        SizedBox(height: 16.w),
        _buildAppointmentCard(
          iconColor: Color(0xFF4CAF50),
          iconData: Icons.home,
          dayDate: "Wed 20 Jun 2025",
          time: "10:00 - Fri 23 Jun 2025",
          shopName: "Daycare @ Pet house",
        ),
      ],
    );
  }

  // Build history appointments tab
  Widget _buildHistoryTab(AppointmentPageController controller) {
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      children: [
        _buildAppointmentCard(
          iconColor: Color(0xFFE8B4FF),
          iconData: Icons.content_cut,
          dayDate: "Sun 15 Jul 2025",
          time: "14:00 - 15:00",
          shopName: "Grooming @ PerfectPet",
        ),
        SizedBox(height: 16.w),
        _buildAppointmentCard(
          iconColor: Color(0xFFFFC107),
          iconData: Icons.medical_services,
          dayDate: "Mon 1 Jun 2025",
          time: "09:00",
          shopName: "Health Check @ Pet Clinic",
        ),
      ],
    );
  }

  // Build appointment card
  Widget _buildAppointmentCard({
    required Color iconColor,
    required IconData iconData,
    required String dayDate,
    required String time,
    required String shopName,
  }) {
    return GestureDetector(
      onTap: () {
        // Navigate to upcoming appointment detail page
        Get.toNamed('/upcoming_appointment');
      },
      child: Container(
        width: 371.w,
        height: 65.w,
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.w),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Left icon
            Container(
              width: 52.w,
              height: 52.w,
              decoration: BoxDecoration(
                color: iconColor,
                borderRadius: BorderRadius.circular(12.w),
              ),
              child: Icon(
                iconData,
                color: Colors.white,
                size: 24.w,
              ),
            ),
            SizedBox(width: 16.w),
            // Middle content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Date and time
                  Text(
                    "$dayDate | $time",
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF666666),
                    ),
                  ),
                  SizedBox(height: 4.w),
                  // Shop name
                  Text(
                    shopName,
                    style: TextStyle(
                      fontFamily: 'Manrope',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF262626),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

}

class AppointmentPageController extends GetxController {
  final AuthService _authService = AuthService.instance;
  final UserService _userService = UserService();
  var userAccount = Rx<UserAccount?>(null);
  UserAccount? account;
  UserData? userData;

  final RxBool isChangedNotificationVisible = false.obs;
  late RxString firstNameHint;
  late RxString lastNameHint;
  late RxString displayNameHint;
  final RxBool isContinueEnabled = true.obs;
  var pageHeaderUserAvatar = Rx<Widget?>(null);
  //var pageHeaderUserName = Rx<Widget?>(null);
  // Theme plugin
  late ThemePlugin theme;
  late CommonButton3Controller buttonController;
  late CommonButton3Controller alterButtonController;

  // Rewards page specific variables
  final RxInt selectedTabIndex = 0.obs; // 0: Tasks, 1: Redeem, 2: History
  final RxBool isPointsCardExpanded = false.obs;
  final RxInt availablePoints = 1590.obs;
  final RxInt weeklyProgress = 40.obs;
  final RxInt weeklyGoal = 50.obs;

  Future<AppointmentPageController> init() async {

    await PageInterceptor.pageAuthCheck();

    // Loading theme
    theme = await Get.putAsync(() => ThemePlugin().init());

    userAccount.value = _authService.userAccount.value;
    return this;
  }

}

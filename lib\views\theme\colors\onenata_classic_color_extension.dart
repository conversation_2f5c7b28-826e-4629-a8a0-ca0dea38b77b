import 'package:flutter/material.dart';

import 'color_extension.dart';
import 'onenata_classic_colors.dart';

class OneNataClassicColorExtension extends ColorExtension {

  OneNataClassicColorExtension() {

    // Common colors
    commonIconBackground = OneNataClassicColors.white2;
    commonSurface2 = OneNataClassicColors.sunsetMild;

    // Auth page colors
    authWhiteSectionShadow = OneNataClassicColors.silver2.withAlpha(0.4.colorAlpha);
    authBackButtonShadow = OneNataClassicColors.cadetGray.withAlpha(0.2.colorAlpha);
    authAvatarShadow = OneNataClassicColors.cadetGray.withAlpha(0.2.colorAlpha);
    authPetBreedBallDogLargeGradient1 = OneNataClassicColors.jasmine;
    authPetBreedBallDogLargeGradient2 = OneNataClassicColors.atomicTangerine;
    authPetBreedBallDogSmallGradient1 = OneNataClassicColors.skyBlue;
    authPetBreedBallDogSmallGradient2 = OneNataClassicColors.mediumSlateBlue;
    authPetBreedBallCatLargeGradient1 = OneNataClassicColors.cyclamen;
    authPetBreedBallCatLargeGradient2 = OneNataClassicColors.rosePompadour;
    authPetBreedBallCatSmallGradient1 = OneNataClassicColors.atomicTangerine2;
    authPetBreedBallCatSmallGradient2 = OneNataClassicColors.burntSienna;
    authPetBreedBallOtherLargeGradient1 = OneNataClassicColors.celticBlue;
    authPetBreedBallOtherLargeGradient2 = OneNataClassicColors.lightBlue;
    authPetBreedBallOtherSmallGradient1 = OneNataClassicColors.celadon;
    authPetBreedBallOtherSmallGradient2 = OneNataClassicColors.lightGreen;
    authPetAddAvatarCameraShadow = OneNataClassicColors.cadetGray.withAlpha(0.2.colorAlpha);
    authPendingVerifyLightRingLarge = OneNataClassicColors.cadetGray2;
    authPendingVerifyLightRingMedium = OneNataClassicColors.payneGray;
    authPendingVerifyLightRingSmall = OneNataClassicColors.eerieBlack3;

    // Event page colors
    eventPostTileShadow = OneNataClassicColors.oxfordBlue.withAlpha(0.08.colorAlpha);
    eventEventTileShadow1 = OneNataClassicColors.spaceCadet.withAlpha(0.04.colorAlpha);
    eventEventTileShadow2 = OneNataClassicColors.pennBlue.withAlpha(0.08.colorAlpha);
    eventPlaceTileShadow1 = OneNataClassicColors.spaceCadet.withAlpha(0.04.colorAlpha);
    eventPlaceTileShadow2 = OneNataClassicColors.pennBlue.withAlpha(0.08.colorAlpha);

    // Profile page colors
    profileAvatarCameraShadow = OneNataClassicColors.cadetGray.withAlpha(0.2.colorAlpha);

    // Geo page colors
    geoOutdoor = OneNataClassicColors.yellowGreen;
    geoPark = OneNataClassicColors.unitedNationBlue;
    geoTrail = OneNataClassicColors.yellowGreen;
    geoVet = OneNataClassicColors.mikadoYellow;
    geoPetStore = OneNataClassicColors.amethyst;
    geoDogFriendlyRestaurant = OneNataClassicColors.majorelleBlue;
    geoFavor = OneNataClassicColors.frenchRose;
    geoCommonPointBg = OneNataClassicColors.slateGray;
    geoPointParkGradient1 = OneNataClassicColors.white.withAlpha(0.7.colorAlpha);
    geoPointParkGradient2 = OneNataClassicColors.white;
    geoPointParkSelected = OneNataClassicColors.imperialRed;
    geoPointTrailGradient1 = OneNataClassicColors.white.withAlpha(0.7.colorAlpha);
    geoPointTrailGradient2 = OneNataClassicColors.white;
    geoPointTrailSelected = OneNataClassicColors.imperialRed;
    geoPointPetActiveGradientStart = OneNataClassicColors.white.withAlpha(0.7.colorAlpha);
    geoPointPetActiveGradientEnd = OneNataClassicColors.white;
    // geoPointPetActiveGradientEnd = OneNataClassicColors.white.withAlpha(0.7.colorAlpha);
    geoPointPetInactiveGradientStart = OneNataClassicColors.skyBlue;
    geoPointPetInactiveGradientEnd = OneNataClassicColors.white.withAlpha(0.7.colorAlpha);
    geoFilterSelected = OneNataClassicColors.cosmicLatte;
    geoFilterButtonShadow = OneNataClassicColors.cadetGray;
    geoPlaceInfoListScrollBar = OneNataClassicColors.silver;
  }
}

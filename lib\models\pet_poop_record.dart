import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'base_full_model.dart';
import 'package:onenata_app/common/utils/utils_i.dart';

part 'pet_poop_record.g.dart';

@JsonSerializable()
class PetPoopRecord{
  String sid;
  int? poopTime;
  List<String>? shapeTags;
  List<String>? colorTags;
  List<String>? images;
  String? notes;

  PetPoopRecord({
    required this.sid,
    this.poopTime,
    this.shapeTags,
    this.colorTags,
    this.images,
    this.notes,
  });

  factory PetPoopRecord.fromJson(Map<String, dynamic> json) =>
      _$PetPoopRecordFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$PetPoopRecordToJson(this);

  static create({
    int? poopTime,
    List<String>? shapeTags,
    List<String>? colorTags,
    List<String>? images,
    String? notes,
  }) {
    return PetPoopRecord(
      sid: uuid.v4(),
      poopTime: poopTime,
      shapeTags: shapeTags,
      colorTags: colorTags,
      images: images,
      notes: notes,
    );
  }

  static copyFrom(PetPoopRecord other) {
    return PetPoopRecord(
      sid: other.sid,
      notes: other.notes,
      poopTime: other.poopTime,
      shapeTags: other.shapeTags,
      colorTags: other.colorTags,
      images: other.images,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

